# 导入所需的库
from jqdata import *
import numpy as np
import pandas as pd
import datetime
import math
import talib

# 初始化函数
def initialize(context):
    """初始化函数"""
    # 设置动态复权模式
    set_option('use_real_price', True)
    # 设置日志级别为 DEBUG
    log.set_level('strategy', 'debug')
    
    # 设置基准
    set_benchmark('000300.XSHG')  # 沪深300作为基准
    
    # 设置滑点
    set_slippage(FixedSlippage(0.002))
    
    # 更新手续费为万一免5
    set_order_cost(OrderCost(open_tax=0, close_tax=0.001, open_commission=0.0001, close_commission=0.0001, min_commission=0), type='stock')
    
    # 设置交易标的（必须最先定义）
    context.stock_list = ['513100.XSHG',  # 纳斯达克ETF
                         '510300.XSHG',  # 沪深300ETF
                         '518880.XSHG']  # 黄金ETF
    
    # 设置技术指标参数
    context.bias_period = 24  # BIAS周
    context.adx_period = 13   # ADX周期
    context.atr_period = 12  # ATR周期
    
    # 添加缓存字典
    context.cache = {
        'technical_indicators': {},
        'last_update_date': None,
        'price_data': {},
        'last_minute': -1,
        'last_money_fund_trade_date': None,
        'last_volatility_calc': {},  # 新增：缓存波动率计算
        'last_trend_calc': {},       # 新增：缓存趋势计算
        'last_rebalance_check': None # 新增：缓存再平衡检查
    }
    
    # 初始化初始买入标志
    context.initial_buy_needed = True
    
    # 预加载历史数据
    preload_historical_data(context)
    
    # 设置参数
    context.total_position_pct = 0.7  # 总仓位70%
    context.reserve_position_pct = 0.3  # 备用资金30%
    
    # 依赖stock_list的初始化全部放在此处之后
    context.buy_prices = {s: [] for s in context.stock_list}
    context.sell_prices = {s: [] for s in context.stock_list}
    context.daily_grid_prices = {s: {'buy_prices': [], 'sell_prices': [], 'amounts': []} for s in context.stock_list}
    context.grid_status = {s: {'triggered_buy': set(), 'triggered_sell': set(), 'last_price': None, 'base_price': None, 'dynamic_buy_prices': [], 'dynamic_sell_prices': [], 'dynamic_amounts': [], 'cum_pnl': 0, 'layer_win_rate': {}} for s in context.stock_list}
    
    # 添加动态网格参数
    context.dynamic_grid = True  # 是否启用动态网格
    
    # 初始化市场状态字典
    context.market_states = {s: 'oscillating' for s in context.stock_list}
    
    # 设置BIAS阈值
    context.bias_threshold = {
        '513100.XSHG': 0.09,   # 纳斯达克ETF阈值±9%
        '510300.XSHG': 0.07,  # 沪深300ETF阈值±7%
        '518880.XSHG': 0.05   # 黄金ETF阈值±5%
    }
    
    # 添加持仓限制参数
    context.position_limits = {
        'oscillating': {'lower': 0.5, 'upper': 1.4},  # 震荡市
        'uptrend': {'lower': 0.7, 'upper': 1.6},      # 上涨趋势
        'downtrend': {'lower': 0.5, 'upper': 1.1}     # 下跌趋势
    }
    
    # 设置网格参数
    context.grid_params = {
        '513100.XSHG': {  # 纳斯达克ETF
            'oscillating': {
                'layers': int(15),  # 增加网格层数
                'buy_atr_multiple': 0.2,  # 降低ATR倍数，提高触发频率
                'sell_atr_multiple': 0.2,
                'grid_value_pct': 0.05  # 增加每格资金比例
            },
            'uptrend': {
                'layers': int(12),
                'buy_atr_multiple': 0.15,  # 上涨趋势下更容易买入
                'sell_atr_multiple': 0.25,
                'grid_value_pct': 0.06
            },
            'downtrend': {
                'layers': int(18),  # 下跌趋势增加网格数量
                'buy_atr_multiple': 0.25,
                'sell_atr_multiple': 0.15,  # 下跌趋势下更容易卖出
                'grid_value_pct': 0.04
            }
        },
        '510300.XSHG': {  # 沪深300ETF
            'oscillating': {
                'layers': 15,
                'buy_atr_multiple': 0.25,  # 降低ATR倍数
                'sell_atr_multiple': 0.25,
                'grid_value_pct': 0.05
            },
            'uptrend': {
                'layers': 12,
                'buy_atr_multiple': 0.2,
                'sell_atr_multiple': 0.3,
                'grid_value_pct': 0.06
            },
            'downtrend': {
                'layers': 18,
                'buy_atr_multiple': 0.3,
                'sell_atr_multiple': 0.2,
                'grid_value_pct': 0.04
            }
        },
        '518880.XSHG': {  # 黄金ETF
            'oscillating': {
                'layers': 12,
                'buy_atr_multiple': 0.35,
                'sell_atr_multiple': 0.35,
                'grid_value_pct': 0.03
            },
            'uptrend': {
                'layers': 10,
                'buy_atr_multiple': 0.3,
                'sell_atr_multiple': 0.4,
                'grid_value_pct': 0.04
            },
            'downtrend': {
                'layers': 15,
                'buy_atr_multiple': 0.4,
                'sell_atr_multiple': 0.3,
                'grid_value_pct': 0.03
            }
        }
    }
    
    # 设置风控参数
    context.single_stock_stop_loss = 0.05  # 单个标的5%止损
    context.portfolio_stop_loss = 0.04  # 组合4%止损
    
    # 运行时需要的变量
    context.last_rebalance_date = None  # 上次再平衡时间
    context.stop_trading = False  # 是否停止交易的标志
    
    # 添加货币基金相关参数
    context.money_fund = '511990.XSHG'  # 华宝添益货币ETF
    context.min_fund_amount = 5  # 最小货基交易金额（万元）
    context.money_fund_threshold = 0.02  # 货基交易阈值（2%）
    
    # 设置定时任务
    run_daily(market_open, time='9:30', reference_security='000300.XSHG')
    run_daily(check_stop_loss, time='9:31', reference_security='000300.XSHG')
    run_daily(trade_money_fund, time='9:32', reference_security='000300.XSHG')
    run_daily(check_fund_income, time='15:10', reference_security='000300.XSHG')
    run_daily(monitor_strategy_performance, time='15:00', reference_security='000300.XSHG')
    
    # 设置网格交易的定时运行
    run_daily(grid_trading, time='9:31', reference_security='000300.XSHG')

    # 优化：减少网格监控频率，改为每5分钟一次
    for hour in [9, 10, 11, 13, 14]:
        for minute in range(0, 60, 5):  # 每5分钟执行一次
            if (hour == 9 and minute < 31) or (hour == 11 and minute > 30) or hour == 15:
                continue
            time_str = f"{hour:02d}:{minute:02d}"
            run_daily(grid_trading_monitor, time=time_str, reference_security='000300.XSHG')
    
    # 设置最小交易数量
    context.min_trade_amount = 100
    
    # 设置是否开启交易日志
    log.set_level('order', 'info')
    log.set_level('strategy', 'info')
    
    # 添加网格最小间距参数
    context.min_grid_spacing_pct = 0.001  # 降低最小网格间距为0.1%
    
    # 设置波动率分级参数
    context.volatility_level = {
        '513100.XSHG': [0.6, 1.3],  # 降低波动率阈值，使网格更密集
        '510300.XSHG': [0.5, 1.3],
        '518880.XSHG': [0.4, 1.5]
    }
    
    # 设置价格区间参数
    context.price_zones = {
        '513100.XSHG': {  # 纳斯达克ETF
            'low': {'range': (1.5, 2.0), 'layers': 12, 'buy_multiple': 0.8, 'sell_multiple': 0.8},
            'medium': {'range': (2.0, 2.5), 'layers': 10, 'buy_multiple': 1.0, 'sell_multiple': 1.0},
            'high': {'range': (2.5, 3.0), 'layers': 8, 'buy_multiple': 1.2, 'sell_multiple': 1.2}
        },
        '510300.XSHG': {  # 沪深300ETF
            'low': {'range': (3.0, 3.5), 'layers': 10, 'buy_multiple': 0.8, 'sell_multiple': 0.8},
            'medium': {'range': (3.5, 4.0), 'layers': 8, 'buy_multiple': 1.0, 'sell_multiple': 1.0},
            'high': {'range': (4.0, 4.5), 'layers': 6, 'buy_multiple': 1.2, 'sell_multiple': 1.2}
        },
        '518880.XSHG': {  # 黄金ETF
            'low': {'range': (2.5, 3.5), 'layers': 12, 'buy_multiple': 0.6, 'sell_multiple': 0.6},
            'medium': {'range': (3.5, 4.5), 'layers': 10, 'buy_multiple': 0.8, 'sell_multiple': 0.8},
            'high': {'range': (4.5, 5.5), 'layers': 8, 'buy_multiple': 1.0, 'sell_multiple': 1.0}
        }
    }
    
    # 设置价格区间切换缓冲带比例
    context.zone_buffer_pct = 0.03  # 3%的缓冲带
    
    # 设置是否已经完成初始建仓的标志
    context.initial_position_established = False

def preload_historical_data(context):
    """预加载历史数据"""
    try:
        # 获取所需的最长周期
        max_period = max(60, context.bias_period, context.adx_period*2, context.atr_period*2)
        
        # 一次性获取所有需要的数据
        for security in context.stock_list:
            try:
                # 获取历史数据
                hist_data = attribute_history(security, max_period, '1d', 
                                           ['high', 'low', 'close', 'factor', 'volume'],
                                           df=True)
                
                # 存储到缓存
                context.cache['price_data'][security] = hist_data
                
                # 计算并缓存初始技术指标
                calculate_initial_indicators(context, security, hist_data)
                
            except Exception as e:
                log.error(f"预加载 {security} 历史数据时出错: {str(e)}")
                continue
                
    except Exception as e:
        log.error(f"预加载历史数据时出错: {str(e)}")

def calculate_initial_indicators(context, security, hist_data):
    """计算并缓存初始技术指标"""
    try:
        # 计算真实价格
        hist_data['factor'] = hist_data['factor'].fillna(method='ffill').fillna(1.0)
        hist_data['factor'] = hist_data['factor'].replace(0, 1.0)
        
        real_closes = hist_data['close'] / hist_data['factor']
        real_highs = hist_data['high'] / hist_data['factor']
        real_lows = hist_data['low'] / hist_data['factor']
        
        # 计算TR和ATR
        tr = pd.Series(np.nan, index=hist_data.index)
        if len(real_highs) > 1:
            tr.iloc[0] = real_highs.iloc[0] - real_lows.iloc[0]
        
        for i in range(1, len(real_highs)):
            tr1 = real_highs.iloc[i] - real_lows.iloc[i]
            prev_close = real_closes.iloc[i-1]
            if pd.isna(prev_close):
                tr2 = np.nan
                tr3 = np.nan
            else:
                tr2 = abs(real_highs.iloc[i] - prev_close)
                tr3 = abs(real_lows.iloc[i] - prev_close)
            
            try:
                tr.iloc[i] = np.nanmax([tr1, tr2, tr3])
            except ValueError:
                tr.iloc[i] = np.nan
        
        atr = tr.ewm(span=context.atr_period, adjust=False).mean()
        new_atr_value = atr.iloc[-1]
        
        # 计算波动率（当前和历史）
        returns = real_closes.pct_change().dropna()
        current_volatility = returns[-20:].std()
        historical_volatility = returns.std()
        
        # 计算其他指标
        ma20 = real_closes[-20:].mean()
        ma60 = real_closes.mean()
        latest_price = real_closes[-1]
        bias = (latest_price - ma20) / ma20
        
        # 计算ADX
        plus_dm = pd.Series(0.0, index=hist_data.index)
        minus_dm = pd.Series(0.0, index=hist_data.index)
        
        for i in range(1, len(real_highs)):
            up_move = real_highs.iloc[i] - real_highs.iloc[i-1]
            down_move = real_lows.iloc[i-1] - real_lows.iloc[i]
            
            if up_move > down_move and up_move > 0:
                plus_dm.iloc[i] = up_move
            if down_move > up_move and down_move > 0:
                minus_dm.iloc[i] = down_move
        
        tr_ema = tr.ewm(span=context.adx_period, adjust=False).mean()
        plus_di = 100 * plus_dm.ewm(span=context.adx_period, adjust=False).mean() / tr_ema
        minus_di = 100 * minus_dm.ewm(span=context.adx_period, adjust=False).mean() / tr_ema
        dx = 100 * abs(plus_di - minus_di) / (plus_di + minus_di)
        adx = dx.ewm(span=context.adx_period, adjust=False).mean()
        
        # 计算平均成交量
        avg_volume = hist_data['volume'][-20:].mean()
        
        # 计算量比
        current_volume = hist_data['volume'][-1]
        volume_5d_avg = hist_data['volume'][-5:].mean()
        volume_ratio = current_volume / volume_5d_avg if volume_5d_avg > 0 else 1.0
        
        # 计算趋势强度
        trend_strength = adx[-20:].mean()
        
        # 存储计算结果
        context.cache['technical_indicators'][security] = {
            'bias': bias,
            'adx': adx.iloc[-1] if not adx.empty else np.nan,
            'plus_di': plus_di.iloc[-1] if not plus_di.empty else np.nan,
            'minus_di': minus_di.iloc[-1] if not minus_di.empty else np.nan,
            'atr': new_atr_value,
            'ma20': ma20,
            'ma60': ma60,
            'volatility': current_volatility,
            'historical_volatility': historical_volatility,
            'avg_volume': avg_volume,
            'trend_strength': trend_strength,
            'latest_price': latest_price,
            'real_closes': real_closes,
            'volume_ratio': volume_ratio
        }
        
    except Exception as e:
        log.error(f"计算初始技术指标时出错: {str(e)}")

def should_rebalance(context):
    """判断是否需要再平衡"""
    try:
        current_date = context.current_dt.date()
        
        # 使用缓存检查再平衡
        if context.cache['last_rebalance_check'] == current_date:
            return False
            
        # 修改为每月最后一个交易日进行再平衡
        current_month = current_date.month
        next_month = current_month + 1 if current_month < 12 else 1
        next_month_first_day = datetime.date(current_date.year + (current_month == 12), next_month, 1)
        current_month_first_day = datetime.date(current_date.year, current_date.month, 1)
        month_trading_days = get_trade_days(start_date=current_month_first_day, end_date=next_month_first_day - datetime.timedelta(days=1))
        
        if len(month_trading_days) == 0:
            return False
            
        last_trading_day = month_trading_days[-1]
        if current_date == last_trading_day:
            if context.last_rebalance_date is not None:
                if context.last_rebalance_date.year == current_date.year and context.last_rebalance_date.month == current_date.month:
                    return False
            context.cache['last_rebalance_check'] = current_date
            return True
        return False
    except Exception as e:
        log.error(f"判断再平衡时出错: {str(e)}")
        return False

def calculate_volatility(context, security, period=20):
    """计算波动率，使用缓存优化"""
    try:
        # 检查缓存
        cache_key = f"{security}_{period}"
        if cache_key in context.cache['last_volatility_calc']:
            last_calc = context.cache['last_volatility_calc'][cache_key]
            if last_calc['date'] == context.current_dt.date():
                return last_calc['value']
        
        # 计算波动率
        prices = context.cache['technical_indicators'][security]['real_closes']
        returns = prices.pct_change().dropna()
        volatility = returns.rolling(period).std().iloc[-1]
        
        # 更新缓存
        context.cache['last_volatility_calc'][cache_key] = {
            'date': context.current_dt.date(),
            'value': volatility
        }
        
        return volatility
    except Exception as e:
        log.error(f"计算波动率时出错: {str(e)}")
        return 0.02  # 默认波动率

def get_trend_strength(context, security, period=20):
    """计算趋势强度，使用缓存优化"""
    try:
        # 检查缓存
        cache_key = f"{security}_{period}"
        if cache_key in context.cache['last_trend_calc']:
            last_calc = context.cache['last_trend_calc'][cache_key]
            if last_calc['date'] == context.current_dt.date():
                return last_calc['value']
        
        # 使用缓存的趋势强度
        indicators = context.cache['technical_indicators'].get(security)
        if indicators and 'trend_strength' in indicators:
            trend_strength = indicators['trend_strength']
        else:
            update_technical_indicators(context, security)
            indicators = context.cache['technical_indicators'].get(security)
            trend_strength = indicators.get('trend_strength', 20)
        
        # 更新缓存
        context.cache['last_trend_calc'][cache_key] = {
            'date': context.current_dt.date(),
            'value': trend_strength
        }
        
        return trend_strength
    except Exception as e:
        log.error(f"计算趋势强度时出错: {str(e)}")
        return 20  # 默认中等趋势

# 优化技术指标计算
def update_technical_indicators(context, security):
    """批量更新技术指标，增加缓存机制和调试日志"""
    current_date = context.current_dt.date()
    
    # 如果不是新的交易日，且已有缓存，直接返回
    if (context.cache.get('last_update_date') == current_date and
        security in context.cache.get('technical_indicators', {})):
        return context.cache['technical_indicators'][security]
    
    try:
        # 获取所需的最长周期
        max_period = max(60, context.bias_period, context.adx_period*2, context.atr_period*2)
        
        # 一次性获取所有需要的数据
        prices = attribute_history(security, max_period, '1d', 
                                 ['high', 'low', 'close', 'factor', 'volume'],
                                 df=True)
        
        # 计算真实价格
        prices['factor'] = prices['factor'].fillna(method='ffill').fillna(1.0)
        prices['factor'] = prices['factor'].replace(0, 1.0)
        
        real_closes = prices['close'] / prices['factor']
        real_highs = prices['high'] / prices['factor']
        real_lows = prices['low'] / prices['factor']
        
        # 计算TR和ATR
        tr = pd.Series(np.nan, index=prices.index)
        if len(real_highs) > 1:
            tr.iloc[0] = real_highs.iloc[0] - real_lows.iloc[0]
        
        for i in range(1, len(real_highs)):
            tr1 = real_highs.iloc[i] - real_lows.iloc[i]
            prev_close = real_closes.iloc[i-1]
            if pd.isna(prev_close):
                tr2 = np.nan
                tr3 = np.nan
            else:
                tr2 = abs(real_highs.iloc[i] - prev_close)
                tr3 = abs(real_lows.iloc[i] - prev_close)
            
            try:
                tr.iloc[i] = np.nanmax([tr1, tr2, tr3])
            except ValueError:
                tr.iloc[i] = np.nan
        
        atr = tr.ewm(span=context.atr_period, adjust=False).mean()
        new_atr_value = atr.iloc[-1]
        
        # 计算波动率（当前和历史）
        returns = real_closes.pct_change().dropna()
        current_volatility = returns[-20:].std()
        historical_volatility = returns.std()
        
        # 计算其他指标
        ma20 = real_closes[-20:].mean()
        ma60 = real_closes.mean()
        latest_price = real_closes[-1]
        bias = (latest_price - ma20) / ma20
        
        # 计算ADX
        plus_dm = pd.Series(0.0, index=prices.index)
        minus_dm = pd.Series(0.0, index=prices.index)
        
        for i in range(1, len(real_highs)):
            up_move = real_highs.iloc[i] - real_highs.iloc[i-1]
            down_move = real_lows.iloc[i-1] - real_lows.iloc[i]
            
            if up_move > down_move and up_move > 0:
                plus_dm.iloc[i] = up_move
            if down_move > up_move and down_move > 0:
                minus_dm.iloc[i] = down_move
        
        tr_ema = tr.ewm(span=context.adx_period, adjust=False).mean()
        plus_di = 100 * plus_dm.ewm(span=context.adx_period, adjust=False).mean() / tr_ema
        minus_di = 100 * minus_dm.ewm(span=context.adx_period, adjust=False).mean() / tr_ema
        dx = 100 * abs(plus_di - minus_di) / (plus_di + minus_di)
        adx = dx.ewm(span=context.adx_period, adjust=False).mean()
        
        # 计算平均成交量
        avg_volume = prices['volume'][-20:].mean()
        
        # 计算量比
        current_volume = prices['volume'][-1]
        volume_5d_avg = prices['volume'][-5:].mean()
        volume_ratio = current_volume / volume_5d_avg if volume_5d_avg > 0 else 1.0
        
        # 计算趋势强度
        trend_strength = adx[-20:].mean()
        
        # 存储计算结果
        if 'technical_indicators' not in context.cache:
            context.cache['technical_indicators'] = {}
        
        indicators_to_update = {
            'bias': bias,
            'adx': adx.iloc[-1] if not adx.empty else np.nan,
            'plus_di': plus_di.iloc[-1] if not plus_di.empty else np.nan,
            'minus_di': minus_di.iloc[-1] if not minus_di.empty else np.nan,
            'atr': new_atr_value,
            'ma20': ma20,
            'ma60': ma60,
            'volatility': current_volatility,
            'historical_volatility': historical_volatility,
            'avg_volume': avg_volume,
            'trend_strength': trend_strength,
            'latest_price': latest_price,
            'real_closes': real_closes,
            'volume_ratio': volume_ratio
        }
        
        if security not in context.cache['technical_indicators']:
            context.cache['technical_indicators'][security] = {}
        context.cache['technical_indicators'][security].update(indicators_to_update)
        context.cache['last_update_date'] = current_date
        
        return context.cache['technical_indicators'][security]
        
    except Exception as e:
        log.error(f"更新 {security} 技术指标时出错: {str(e)}")
        default_indicators = {
            'bias': 0, 'adx': 25, 'plus_di': 20, 'minus_di': 20, 'atr': 0.0,
            'ma20': 0, 'ma60': 0, 'volatility': 0.02, 'historical_volatility': 0.02,
            'avg_volume': 1e6, 'trend_strength': 20, 'latest_price': 0,
            'volume_ratio': 1.0, 'real_closes': pd.Series([])
        }
        return context.cache.get('technical_indicators', {}).get(security, default_indicators)

# 修改原有的技术指标计算函数
def calculate_bias(context, security, n=24):
    """使用缓存的技术指标"""
    try:
        return context.cache['technical_indicators'][security]['bias']
    except Exception as e:
        log.error(f"计算BIAS时出错: {str(e)}")
        return 0.0

def calculate_adx(context, security, n=14):
    """使用缓存的技术指标"""
    try:
        indicators = context.cache['technical_indicators'][security]
        return indicators['adx'], indicators['plus_di'], indicators['minus_di']
    except Exception as e:
        log.error(f"计算ADX时出错: {str(e)}")
        return 0.0, 0.0, 0.0

def calculate_atr(context, security, n=14):
    """使用缓存的技术指标"""
    try:
        return context.cache['technical_indicators'][security]['atr']
    except Exception as e:
        log.error(f"计算ATR时出错: {str(e)}")
        return 0.0

# 修改market_open函数
def market_open(context):
    """开盘时运行"""
    try:
        # 更新技术指标
        for security in context.stock_list:
            update_technical_indicators(context, security)
            # 移除ATR日志记录，改为debug级别
            atr = context.cache['technical_indicators'][security].get('atr', 0)
            log.debug(f"{security} 当前ATR: {atr:.4f}")
            
        # 检查是否需要建立初始仓位
        if context.initial_buy_needed and not context.initial_position_established:
            log.info("准备进行初始买入")
            execute_initial_buy(context)
            context.initial_buy_needed = False
            
        # 取消所有未完成订单
        cancel_all_orders(context)
        
        # 检查是否需要月度再平衡
        if should_rebalance(context):
            log.info("执行月度再平衡")
            rebalance_portfolio(context)  # 使用更复杂的再平衡函数
            
        # 更新市场状态
        for security in context.stock_list:
            old_state = context.market_states[security]
            new_state = get_market_state(context, security)
            if old_state != new_state:
                # 只在状态发生显著变化时记录
                if (old_state == 'oscillating' or new_state == 'oscillating') and abs(get_trend_strength(context, security)) > 20:
                    log.info(f"{security} 市场状态从 {old_state} 变为 {new_state}")
                context.market_states[security] = new_state
                    
        # 更新网格资金分配
        update_grid_allocation(context)
        
        # 执行网格交易
        for security in context.stock_list:
            if security in context.grid_status:
                # 检查是否需要暂停网格交易
                if should_pause_grid_trading(context, security):
                    log.info(f"{security} 暂停网格交易")
                    continue
                    
                # 计算自适应网格参数
                spacing, layers = calculate_adaptive_grid_params(context, security)
                context.grid_params[security][context.market_states[security]].update({
                    'buy_atr_multiple': spacing,
                    'layers': layers
                })
                
                # 获取当前价格
                current_data = get_current_data()[security]
                if current_data.paused:
                    continue
                current_price = current_data.last_price
                
                # 执行网格交易
                grid_status = context.grid_status[security]
                buy_prices = grid_status.get('dynamic_buy_prices', [])
                sell_prices = grid_status.get('dynamic_sell_prices', [])
                amounts = grid_status.get('dynamic_amounts', [])
                
                for i, (buy_price, sell_price, amount) in enumerate(zip(buy_prices, sell_prices, amounts)):
                    if current_price <= buy_price * 1.002:
                        execute_grid_trade(context, security, amount, 'buy', current_price)
                    elif current_price >= sell_price * 0.998:
                        execute_grid_trade(context, security, -amount, 'sell', current_price)
                
    except Exception as e:
        log.error(f"market_open出错: {str(e)}")

# 计算证券收益率
def get_security_returns(security):
    """
    计算证券的日收益率，使用真实价格
    参数:
        security: 证券代码
    返回:
        float: 当日收益率
    """
    # 获取当前价格
    current_data = get_current_data()
    current_price = current_data[security].last_price
    
    # 获取昨日收盘价和复权因子
    hist = attribute_history(security, 1, '1d', ['close', 'factor'])
    yesterday_price = hist['close'][0] / hist['factor'][0]
    
    # 计算收益率
    returns = (current_price - yesterday_price) / yesterday_price
    
    return returns

# 检查止损
def check_stop_loss(context):
    """检查止损条件"""
    for security in context.stock_list:
        returns = get_security_returns(security)
        stop_loss_line = calculate_dynamic_stop_loss(context, security)  # 使用更复杂的止损函数
        
        if returns <= -stop_loss_line:
            log.info(f"触发止损 - {security}: 收益率={returns:.2%}, 止损线={stop_loss_line:.2%}")
            position = context.portfolio.positions.get(security)
            if position and position.total_amount > 0:
                sell_amount = int(position.total_amount * 0.5)
                if sell_amount >= 100:
                    order(security, -sell_amount)
                    log.info(f"分批止损 - {security}: 卖出{sell_amount}股")
                else:
                    order_target(security, 0)
                    log.info(f"完全止损 - {security}: 清仓")
            context.stop_trading = True
            return
            
    portfolio_returns = context.portfolio.returns
    if portfolio_returns <= -context.portfolio_stop_loss:
        log.info(f"触发组合止损: 收益率={portfolio_returns:.2%}")
        context.stop_trading = True

# 网格交易主函数
def grid_trading(context):
    """初始化或更新当日网格价格"""
    if context.stop_trading:
        return
    
    # 计算当日网格价格
    calculate_daily_grid_prices(context)
    
    # 重置网格状态并记录关键信息
    for security in context.stock_list:
        current_data = get_current_data()[security]
        current_price = current_data.last_price
        
        # 重置网格状态
        context.grid_status[security] = {
            'triggered_buy': set(),
            'triggered_sell': set(),
            'last_price': current_price,
            'base_price': current_price,
            'dynamic_buy_prices': [],
            'dynamic_sell_prices': [],
            'dynamic_amounts': [],
            'cum_pnl': 0
        }
        
        # 如果启用动态网格，初始化动态网格价格
        if context.dynamic_grid:
            grid_prices = context.daily_grid_prices[security]
            context.grid_status[security].update({
                'dynamic_buy_prices': grid_prices['buy_prices'].copy(),
                'dynamic_sell_prices': grid_prices['sell_prices'].copy(),
                'dynamic_amounts': grid_prices['amounts'].copy()
            })
            
            # 只记录初始网格的关键信息
            if len(grid_prices['buy_prices']) > 0:
                # log.info(f"{security} 网格初始化 - 基准价: {current_price:.3f}, 层数: {len(grid_prices['buy_prices'])}")
                pass

def grid_trading_monitor(context):
    """优化后的网格交易监控函数"""
    if context.stop_trading:
        return
    
    for security in context.stock_list:
        try:
            # 增加前置条件判断
            if context.stop_trading or should_pause_grid_trading(context, security):
                continue
            
            if not check_risk_limits(context, security):
                continue
            
            current_data = get_current_data()[security]
            if current_data.paused:
                continue
            
            current_price = current_data.last_price
            grid_status = context.grid_status[security]
            last_price = grid_status['last_price']
            
            # 动态计算价格变动触发阈值
            adaptive_trigger = calculate_adaptive_trigger(context, security)
            
            price_change_pct = abs(current_price - last_price) / last_price
            if price_change_pct >= adaptive_trigger:
                # 执行网格交易逻辑
                manage_grid_trading(context, security, current_price)
                grid_status['last_price'] = current_price
                
        except Exception as e:
            log.error(f"处理 {security} 网格交易时发生错误: {str(e)}")
            continue
            
        # 每30分钟检查资金分配
        if context.current_dt.minute % 30 == 0:
            if not should_pause_grid_trading(context, security):
                current_price = get_current_data()[security].last_price
                recalculate_grid_fund(context, security, current_price)
            else:
                log.debug(f"{security} 网格交易暂停，跳过资金再平衡计算")

# 判断是否需要再平衡
def should_rebalance(context):
    """判断是否需要再平衡"""
    try:
        current_date = context.current_dt.date()
        
        # 使用缓存检查再平衡
        if context.cache['last_rebalance_check'] == current_date:
            return False
            
        # 修改为每月最后一个交易日进行再平衡
        current_month = current_date.month
        next_month = current_month + 1 if current_month < 12 else 1
        next_month_first_day = datetime.date(current_date.year + (current_month == 12), next_month, 1)
        current_month_first_day = datetime.date(current_date.year, current_date.month, 1)
        month_trading_days = get_trade_days(start_date=current_month_first_day, end_date=next_month_first_day - datetime.timedelta(days=1))
        
        if len(month_trading_days) == 0:
            return False
            
        last_trading_day = month_trading_days[-1]
        if current_date == last_trading_day:
            if context.last_rebalance_date is not None:
                if context.last_rebalance_date.year == current_date.year and context.last_rebalance_date.month == current_date.month:
                    return False
            context.cache['last_rebalance_check'] = current_date
            return True
        return False
    except Exception as e:
        log.error(f"判断再平衡时出错: {str(e)}")
        return False

# 计算风险平价权重
def calculate_risk_parity_weights(context):
    # 初始化波动率字典
    volatilities = {}
    
    # 计算每个ETF的60日波动率
    for security in context.stock_list:
        # 获取过去60个交易日的收盘价
        price_data = get_price(security, count=60, frequency='daily', fields=['close'])
        close = price_data['close']
        
        # 计算日收益率
        returns = close.pct_change().dropna()
        
        # 计算波动率（标准差）
        volatility = returns.std()
        volatilities[security] = volatility
    
    # 计算权重：1/波动率 / 所有(1/波动率)之和
    inv_vol_sum = sum(1.0 / vol for vol in volatilities.values())
    weights = {security: (1.0 / vol) / inv_vol_sum for security, vol in volatilities.items()}
    
    # 优化：将权重转换为整数百分比
    total_pct = 100
    int_weights = {}
    remaining = total_pct
    
    # 先将权重转换为整数百分比（向下取整）
    for security, weight in weights.items():
        int_weight = int(weight * 100)
        int_weights[security] = int_weight
        remaining -= int_weight
    
    # 将剩余的百分比按原权重比例分配
    if remaining > 0:
        # 按原始权重排序，权重大的优先获得剩余百分比
        sorted_securities = sorted(weights.keys(), key=lambda x: weights[x], reverse=True)
        for i in range(remaining):
            security = sorted_securities[i % len(sorted_securities)]
            int_weights[security] += 1
    
    # 转换回小数形式
    rounded_weights = {security: weight / 100.0 for security, weight in int_weights.items()}
    
    # 输出取整后的权重信息（改为debug级别）
    weight_str = ', '.join([f"{security}: {weight:.0%}" for security, weight in rounded_weights.items()])
    log.debug(f"风险平价权重计算结果: {weight_str}")
    
    return rounded_weights

def calculate_dynamic_allocation_factors(context, security):
    """
    计算动态配置调整因子
    返回: (float, float) - (调整系数, 置信度)
    """
    try:
        indicators = context.cache['technical_indicators'].get(security)
        if not indicators:
            update_technical_indicators(context, security)
            indicators = context.cache['technical_indicators'].get(security)
        
        # 获取关键指标，使用get方法安全获取
        trend_strength = indicators.get('trend_strength', 20)  # 默认中等趋势
        volatility = indicators.get('volatility', 0.02)  # 默认波动率
        volume_ratio = indicators.get('volume_ratio', 1.0)  # 默认量比
        bias = indicators.get('bias', 0)  # 默认BIAS
        adx = indicators.get('adx', 25)  # 默认ADX
        plus_di = indicators.get('plus_di', 20)  # 默认+DI
        minus_di = indicators.get('minus_di', 20)  # 默认-DI
        
        # 计算趋势强度因子 (0.5 ~ 2.0) - 扩大范围以提高收益潜力
        trend_factor = min(2.0, max(0.5, trend_strength / 20))
        
        # 计算趋势方向因子 (0.8 ~ 1.5)
        direction_factor = 1.0
        if plus_di > minus_di:
            direction_factor = min(1.5, max(1.0, 1 + (plus_di - minus_di) / 100))
        else:
            direction_factor = min(1.0, max(0.8, 1 - (minus_di - plus_di) / 100))
        
        # 计算波动率因子 (0.6 ~ 1.5) - 低波动时更积极
        vol_base = 0.02  # 基准波动率
        if volatility < vol_base:
            volatility_factor = min(1.5, vol_base / volatility)
        else:
            volatility_factor = max(0.6, vol_base / volatility)
        
        # 计算成交量因子 (0.7 ~ 1.8) - 放大成交量影响
        if volume_ratio > 1:
            volume_factor = min(1.8, 1 + (volume_ratio - 1) * 0.5)
        else:
            volume_factor = max(0.7, 1 - (1 - volume_ratio) * 0.5)
        
        # 计算BIAS影响因子 (0.6 ~ 1.6) - 扩大BIAS影响
        bias_threshold = context.bias_threshold[security]
        if abs(bias) < bias_threshold * 0.5:  # BIAS在合理范围内，更积极调整
            bias_factor = 1.6 - abs(bias) / bias_threshold
        else:  # BIAS偏离较大，谨慎调整
            bias_factor = max(0.6, 1 - abs(bias) / bias_threshold)
        
        # 计算市场趋势持续性
        trend_persistence = min(1.5, adx / 25)
        
        # 综合计算调整系数 - 调整各因子权重
        adjustment_factor = (
            trend_factor * 0.35 +      # 增加趋势因子权重
            direction_factor * 0.20 +   # 新增趋势方向因子
            volatility_factor * 0.15 +  # 降低波动率影响
            volume_factor * 0.15 +      # 保持成交量影响
            bias_factor * 0.15         # 保持BIAS影响
        ) * trend_persistence  # 使用趋势持续性作为整体调节因子
        
        # 计算置信度 (0 ~ 1) - 优化置信度计算
        confidence = min(1.0, (
            (trend_strength / 40) * 0.4 +                    # 降低趋势强度要求
            (1 - abs(bias) / (bias_threshold * 2)) * 0.3 +   # 放宽BIAS影响
            (adx / 40) * 0.3 +                               # 降低ADX要求
            (volume_ratio - 0.5) * 0.2                       # 添加成交量影响
        ))
        
        # 根据市场状态调整最终系数
        market_state = context.market_states[security]
        if market_state == 'uptrend':
            # 上涨趋势时，如果指标都支持，可以更激进
            if confidence > 0.7 and trend_factor > 1.2:
                adjustment_factor *= 1.2
        elif market_state == 'downtrend':
            # 下跌趋势时，如果指标显示反转可能，保持灵活
            if confidence > 0.6 and bias_factor > 1.2:
                adjustment_factor *= 0.9
        
        # 确保最终调整系数在合理范围内
        adjustment_factor = min(2.5, max(0.5, adjustment_factor))
        
        # 记录详细的调整因子信息
        if adjustment_factor > 1.5 or adjustment_factor < 0.7:
            log.info(f"{security} 调整因子详情:")
            log.info(f"趋势因子: {trend_factor:.2f}, 方向因子: {direction_factor:.2f}")
            log.info(f"波动率因子: {volatility_factor:.2f}, 成交量因子: {volume_factor:.2f}")
            log.info(f"BIAS因子: {bias_factor:.2f}, 趋势持续性: {trend_persistence:.2f}")
        
        return adjustment_factor, confidence
        
    except Exception as e:
        log.error(f"计算动态配置因子时出错: {str(e)}")
        return 1.0, 0.5  # 出错时返回默认值

def rebalance_portfolio(context):
    try:
        # 计算风险平价权重
        weights = calculate_risk_parity_weights(context)
        
        # 记录原始备用资金比例
        original_reserve_pct = context.reserve_position_pct
        current_reserve_pct = original_reserve_pct
        
        # 优化：动态调整备用资金比例
        up_count = sum(1 for s in context.stock_list if context.market_states[s] == 'uptrend')
        down_count = sum(1 for s in context.stock_list if context.market_states[s] == 'downtrend')
        
        # 根据整体市场状况调整备用资金
        if up_count == len(context.stock_list):
            current_reserve_pct = max(0.1, original_reserve_pct * 0.5)  # 全面上涨时降低备用资金
            log.info(f'全面上涨行情，备用资金调整为{current_reserve_pct:.1%}')
        elif down_count == len(context.stock_list):
            current_reserve_pct = min(0.5, original_reserve_pct * 1.5)  # 全面下跌时提高备用资金
            log.info(f'全面下跌行情，备用资金调整为{current_reserve_pct:.1%}')
        
        # 动态调整各个标的的配置
        total_adjustment = 0  # 记录总调整幅度
        adjustments = {}  # 记录每个标的的调整幅度
        
        # 第一轮：计算理论调整幅度
        for security in context.stock_list:
            try:
                market_state = context.market_states[security]
                adjustment_factor, confidence = calculate_dynamic_allocation_factors(context, security)
                
                if market_state == 'uptrend':
                    # 上涨趋势的动态超配 - 提高基础超配上限
                    base_extra = min(0.12, current_reserve_pct)  # 从8%提高到12%
                    extra_weight = base_extra * adjustment_factor * confidence
                    
                    # 添加趋势加速度判断
                    if context.cache['technical_indicators'][security]['trend_strength'] > 30:
                        extra_weight *= 1.2  # 强趋势额外加仓
                    
                    adjustments[security] = extra_weight
                    total_adjustment += extra_weight
                    
                elif market_state == 'downtrend':
                    # 下跌趋势的动态减配 - 更灵活的减配策略
                    base_reduction = min(0.08, weights[security] * 0.6)  # 从5%提高到8%
                    reduction = base_reduction * adjustment_factor * confidence
                    
                    # 添加反转信号判断
                    if context.cache['technical_indicators'][security]['bias'] < -context.bias_threshold[security]:
                        reduction *= 0.8  # 可能超卖时减少减配幅度
                    
                    adjustments[security] = -reduction
                    total_adjustment -= reduction
                    
                log.info(f"{security} 调整系数: {adjustment_factor:.2f}, 置信度: {confidence:.2f}")
                
            except Exception as e:
                log.error(f"计算{security}配置调整时出错: {str(e)}")
                adjustments[security] = 0
        
        # 第二轮：实际调整权重
        available_reserve = current_reserve_pct - 0.05  # 保留5%最小备用资金
        if total_adjustment > 0:
            # 等比例缩放调整幅度，确保不超过可用备用资金
            scale_factor = min(1.0, available_reserve / total_adjustment)
            for security in context.stock_list:
                if security in adjustments:
                    actual_adjustment = adjustments[security] * scale_factor
                    weights[security] += actual_adjustment
                    current_reserve_pct -= actual_adjustment
                    
                    if abs(actual_adjustment) >= 0.001:  # 仅记录显著调整
                        log.info(f"{security} {'超配' if actual_adjustment > 0 else '减配'} {abs(actual_adjustment):.1%}")
        
        # 确保权重非负且总和不超过1
        weights = {s: max(0.0, w) for s, w in weights.items()}
        weight_sum = sum(float(w) for w in weights.values())  # 正确的求和方式
        
        if weight_sum > 1:
            scale = (1 - current_reserve_pct) / weight_sum
            weights = {s: w * scale for s, w in weights.items()}
            log.info(f"权重总和超过1，进行等比例缩减，缩减系数: {scale:.3f}")
            
            # 再次验证调整后的权重总和
            final_weight_sum = sum(float(w) for w in weights.values())
            log.info(f"调整后权重总和: {final_weight_sum:.3f}")
        
        # 更新备用资金比例
        context.reserve_position_pct = current_reserve_pct
        log.info(f"调整后备用资金比例: {current_reserve_pct:.1%} (原始: {original_reserve_pct:.1%})")
        
        # 处理货基持仓
        if context.money_fund in context.portfolio.positions:
            position = context.portfolio.positions[context.money_fund]
            if position and getattr(position, 'total_amount', 0) > 0:
                order_target(context.money_fund, 0)
                log.info(f"季度再平衡: 清空货基持仓 {position.total_amount}份")
        
        # 调整持仓
        total_position_value = context.portfolio.total_value * (1 - current_reserve_pct)
        for security in context.stock_list:
            target_value = total_position_value * weights[security]
            order_target_value(security, target_value)
            log.info(f"调整 {security} 目标市值至: {target_value:.2f}, 权重: {weights[security]:.2%}")
        
        # 更新最后再平衡时间
        context.last_rebalance_date = context.current_dt.date()
        
    except Exception as e:
        log.error(f"执行再平衡时发生错误: {str(e)}")
        raise  # 重新抛出异常，确保错误不被静默处理

def cancel_all_orders(context):
    """取消所有未完成的订单"""
    try:
        orders = get_open_orders()
        if orders:
            log.info(f"取消了 {len(orders)} 个未完成订单")
            for order in orders.values():
                cancel_order(order)
                log.debug(f"取消未完成订单: {order.security}, 委托价格: {order.price}, 委托数量: {order.amount}")
    except Exception as e:
        log.error(f"取消订单时出错: {str(e)}")

def execute_initial_buy(context):
    """执行初始建仓"""
    try:
        # 计算风险平价权重
        weights = calculate_risk_parity_weights(context)
        
        # 计算每个标的的目标金额
        total_value = context.portfolio.total_value
        available_cash = context.portfolio.available_cash
        
        # 执行建仓
        for security in context.stock_list:
            try:
                # 计算目标金额
                target_value = total_value * context.total_position_pct * weights.get(security, 0)
                
                # 获取当前价格
                current_data = get_current_data()[security]
                if current_data.paused:
                    log.warning(f"{security} 当前暂停交易，跳过建仓")
                    continue
                    
                current_price = current_data.last_price
                
                # 计算购买数量（确保是100的整数倍）
                amount = int(target_value / current_price / 100) * 100
                
                if amount >= 100:  # 确保购买数量至少为100股
                    order_value = amount * current_price
                    if order_value <= available_cash:  # 确保资金充足
                        order(security, amount)
                        log.info(f"初始建仓 {security}: 数量={amount}, 价格={current_price:.3f}, 金额={order_value:.2f}")
                    else:
                        log.warning(f"初始建仓 {security}: 资金不足，需要{order_value:.2f}，可用{available_cash:.2f}")
                else:
                    log.warning(f"初始建仓 {security}: 计算购买数量小于100股，跳过购买")
            except Exception as e:
                log.error(f"处理标的 {security} 时发生错误: {str(e)}")
                continue
                
        log.info("初始建仓完成")
        context.initial_position_established = True
    except Exception as e:
        log.error(f"执行初始建仓时发生错误: {str(e)}")
        raise

def manage_grid_trading(context, security, current_price):
    """优化后的网格交易管理函数"""
    try:
        grid_status = context.grid_status[security]
        market_state = context.market_states[security]
        
        # 获取动态网格价格
        buy_prices = grid_status['dynamic_buy_prices']
        sell_prices = grid_status['dynamic_sell_prices']
        amounts = grid_status['dynamic_amounts']
        
        # 计算价格趋势和成交量趋势
        price_trend = calculate_price_trend(context, security)
        volume_trend = calculate_volume_trend(context, security)
        
        # 获取技术指标
        indicators = context.cache['technical_indicators'].get(security, {})
        adx = indicators.get('adx', 25)
        plus_di = indicators.get('plus_di', 20)
        minus_di = indicators.get('minus_di', 20)
        
        # 动态调整网格触发条件
        for i, (buy_price, sell_price, amount) in enumerate(zip(buy_prices, sell_prices, amounts)):
            # 计算预期利润
            expected_profit = (sell_price - buy_price) / buy_price
            
            # 买入条件优化
            if current_price <= buy_price * 1.002:  # 允许2‰的价格偏差
                # 检查是否为逆趋势操作
                if adx > 25 and plus_di > minus_di * 1.1:  # 上升趋势
                    continue  # 跳过买入
                    
                # 调整买入量
                adjusted_amount = amount
                if price_trend < -0.01 and volume_trend > 1.1:
                    adjusted_amount = int(amount * 1.5)
                elif price_trend > 0.01:
                    adjusted_amount = int(amount * 0.8)
                
                # 检查最小利润
                if expected_profit >= 0.01:  # 最小1%利润
                    if execute_grid_trade(context, security, adjusted_amount, 'buy', current_price):
                        grid_status['triggered_buy'].add(buy_price)
                        grid_status['cum_pnl'] += (sell_price - current_price) * adjusted_amount
            
            # 卖出条件优化
            if current_price >= sell_price * 0.998:  # 允许2‰的价格偏差
                # 检查是否为逆趋势操作
                if adx > 25 and minus_di > plus_di * 1.1:  # 下降趋势
                    continue  # 跳过卖出
                    
                # 调整卖出量
                adjusted_amount = amount
                if price_trend > 0.01 and volume_trend > 1.1:
                    adjusted_amount = int(amount * 1.5)
                elif price_trend < -0.01:
                    adjusted_amount = int(amount * 0.8)
                
                # 检查最小利润
                if expected_profit >= 0.01:  # 最小1%利润
                    if execute_grid_trade(context, security, -adjusted_amount, 'sell', current_price):
                        grid_status['triggered_sell'].add(sell_price)
                        grid_status['cum_pnl'] += (current_price - buy_price) * adjusted_amount
        
        # 更新网格状态
        if len(grid_status['triggered_buy']) > 0 or len(grid_status['triggered_sell']) > 0:
            calculate_dynamic_grid_prices(context, security, current_price)
            
    except Exception as e:
        log.error(f"管理网格交易时发生错误: {str(e)}")

def calculate_dynamic_stop_loss(context, security):
    """计算动态止损线"""
    try:
        # 获取市场状态和波动率
        market_state = context.market_states[security]
        volatility = calculate_volatility(context, security)
        
        # 基础止损线
        base_stop_loss = context.single_stock_stop_loss
        
        # 根据市场状态调整
        if market_state == 'uptrend':
            stop_loss_factor = 1.2  # 上涨趋势放宽止损
        elif market_state == 'downtrend':
            stop_loss_factor = 0.8  # 下跌趋势收紧止损
        else:
            stop_loss_factor = 1.0  # 震荡市保持基础止损
            
        # 根据波动率调整
        volatility_factor = 1.0 + (volatility - 0.02) * 2  # 波动率每增加1%，止损放宽2%
        volatility_factor = max(0.8, min(1.5, volatility_factor))  # 限制调整范围
        
        # 计算最终止损线
        final_stop_loss = base_stop_loss * stop_loss_factor * volatility_factor
        
        return max(0.03, min(0.08, final_stop_loss))  # 限制止损线在3%-8%之间
        
    except Exception as e:
        log.error(f"计算动态止损线时出错: {str(e)}")
        return context.single_stock_stop_loss

def monitor_strategy_performance(context):
    """监控策略性能"""
    try:
        # 计算当日收益
        daily_return = context.portfolio.returns
        
        # 计算波动率
        portfolio_volatility = calculate_portfolio_volatility(context)
        
        # 计算夏普比率
        sharpe_ratio = daily_return / portfolio_volatility if portfolio_volatility > 0 else 0
        
        # 检查风险指标
        if daily_return < -0.02:  # 单日亏损超过2%
            log.warning(f"策略单日亏损较大: {daily_return:.2%}")
            
        if portfolio_volatility > 0.03:  # 波动率超过3%
            log.warning(f"策略波动率较高: {portfolio_volatility:.2%}")
            
        if sharpe_ratio < 0.5:  # 夏普比率低于0.5
            log.warning(f"策略夏普比率较低: {sharpe_ratio:.2f}")
            
        # 记录性能指标
        log.info(f"策略性能指标 - 日收益: {daily_return:.2%}, 波动率: {portfolio_volatility:.2%}, 夏普比率: {sharpe_ratio:.2f}")
        
    except Exception as e:
        log.error(f"监控策略性能时出错: {str(e)}")

def dynamic_stop_loss(context, security):
    # 根据历史波动率动态调整止损线
    vol = calculate_volatility(context, security)
    base_stop = 0.05
    stop_loss = max(0.03, min(0.08, base_stop * (1 + vol / 0.02)))
    return stop_loss

def get_trend_strength(context, security, period=20):
    """计算趋势强度，使用缓存数据"""
    try:
        # 使用缓存的趋势强度
        indicators = context.cache['technical_indicators'].get(security)
        if indicators and 'trend_strength' in indicators:
            return indicators['trend_strength']
        
        # 如果缓存不存在，更新技术指标
        update_technical_indicators(context, security)
        indicators = context.cache['technical_indicators'].get(security)
        if indicators and 'trend_strength' in indicators:
            return indicators['trend_strength']
            
        # 如果仍然没有趋势强度，返回默认值
        return 20  # 默认中等趋势
    except Exception as e:
        log.error(f"计算趋势强度时出错: {str(e)}")
        return 20  # 默认中等趋势

def get_avg_volume(context, security, period=20):
    """计算平均成交量，使用缓存数据"""
    try:
        # 使用缓存的平均成交量
        indicators = context.cache['technical_indicators'].get(security)
        if indicators and 'avg_volume' in indicators:
            return indicators['avg_volume']
        
        # 如果缓存不存在，更新技术指标
        update_technical_indicators(context, security)
        return context.cache['technical_indicators'][security]['avg_volume']
    except Exception as e:
        log.error(f"计算平均成交量时出错: {str(e)}")
        return 1e6  # 默认值

def calculate_adaptive_grid_params(context, security):
    """计算自适应网格参数"""
    try:
        # 计算当前波动率
        volatility = calculate_volatility(context, security)
        
        # 获取基础参数
        base_params = context.grid_params[security][context.market_states[security]]
        base_spacing = base_params['buy_atr_multiple']
        base_layers = base_params['layers']
        
        # 计算历史波动率（60日）
        try:
            hist_data = attribute_history(security, 60, '1d', ['close'])
            hist_returns = hist_data['close'].pct_change().dropna()
            hist_volatility = hist_returns.std()
        except:
            hist_volatility = volatility
        
        # 波动率自适应调整 - 增加调整系数
        volatility_ratio = volatility / (hist_volatility if hist_volatility > 0 else volatility)
        adaptive_spacing = base_spacing * (1 + (volatility_ratio - 1) * 0.8)  # 从0.5增加到0.8
        adaptive_spacing = max(0.2, min(1.0, adaptive_spacing))  # 调整范围从0.1-0.5到0.2-1.0
        
        # 波动率越高，网格层数越少
        adaptive_layers = max(8, int(base_layers * (1 - (volatility_ratio - 1) * 0.3)))
        
        return adaptive_spacing, adaptive_layers
        
    except Exception as e:
        log.error(f"计算自适应网格参数时出错: {str(e)}")
        return base_spacing, base_layers

def optimize_grid_by_market_state(context, security):
    """根据市场状态优化网格参数"""
    try:
        market_state = context.market_states[security]
        grid_params = context.grid_params[security][market_state]
        
        # 获取市场特征
        trend_strength = get_trend_strength(context, security)
        volatility = calculate_volatility(context, security)
        
        # 上涨趋势优化
        if market_state == 'uptrend':
            # 提高卖出网格密度，降低买入网格密度
            grid_params['sell_atr_multiple'] *= 0.8
            grid_params['buy_atr_multiple'] *= 1.2
            
            # 趋势越强，调整幅度越大
            if trend_strength > 30:
                grid_params['sell_atr_multiple'] *= 0.9
                grid_params['buy_atr_multiple'] *= 1.1
                
        # 下跌趋势优化
        elif market_state == 'downtrend':
            # 提高买入网格密度，降低卖出网格密度
            grid_params['buy_atr_multiple'] *= 0.8
            grid_params['sell_atr_multiple'] *= 1.2
            
            # 趋势越强，调整幅度越大
            if trend_strength < -30:
                grid_params['buy_atr_multiple'] *= 0.9
                grid_params['sell_atr_multiple'] *= 1.1
                
        # 震荡市优化
        else:
            # 根据波动率调整网格密度
            if volatility > 0.02:
                # 高波动时扩大网格间距
                grid_params['buy_atr_multiple'] *= 1.1
                grid_params['sell_atr_multiple'] *= 1.1
            else:
                # 低波动时缩小网格间距
                grid_params['buy_atr_multiple'] *= 0.9
                grid_params['sell_atr_multiple'] *= 0.9
        
        # 确保参数在合理范围内
        grid_params['buy_atr_multiple'] = max(0.1, min(0.5, grid_params['buy_atr_multiple']))
        grid_params['sell_atr_multiple'] = max(0.1, min(0.5, grid_params['sell_atr_multiple']))
        
    except Exception as e:
        log.error(f"优化网格参数时出错: {str(e)}")

def execute_grid_trade(context, security, amount, trade_type, current_price):
    """优化的网格交易执行"""
    try:
        if not check_trade_conditions(context, security, amount, trade_type):
            return False
        
        # 使用缓存的波动率数据
        indicators = context.cache['technical_indicators'].get(security)
        if not indicators:
            update_technical_indicators(context, security)
            indicators = context.cache['technical_indicators'].get(security)
            
        volatility = indicators.get('volatility', 0.02)  # 默认波动率
        historical_volatility = indicators.get('historical_volatility', 0.02)  # 默认历史波动率
        
        # 计算预期收益
        expected_profit = calculate_expected_profit(context, security, amount, trade_type, current_price)
        
        # 计算交易成本
        commission_rate = 0.0001  # 万1
        min_commission = 0  # 无最低收费
        commission_cost = max(current_price * abs(amount) * commission_rate, min_commission)
        stamp_tax = 0.001 if trade_type == 'sell' else 0  # 千1
        slippage_rate = 0.002  # 千2
        slippage_cost = current_price * abs(amount) * slippage_rate
        total_cost = commission_cost + (current_price * abs(amount) * stamp_tax) + slippage_cost
        
        # 动态收益要求
        win_rate = context.grid_status[security].get('win_rate', 0.5)
        min_profit_ratio = 1.5 + (win_rate - 0.6) * 2  # 胜率每增10%，要求降0.2倍
        
        # 根据波动率调整收益要求
        volatility_ratio = volatility / (historical_volatility if historical_volatility > 0 else volatility)
        min_profit_ratio *= (1 + (volatility_ratio - 1) * 0.5)
        
        # 趋势市下非对称阈值
        market_state = context.market_states[security]
        if market_state == 'downtrend' and trade_type == 'sell':
            min_profit_ratio = max(min_profit_ratio, 1.8)
        elif market_state == 'uptrend' and trade_type == 'buy':
            min_profit_ratio = min(min_profit_ratio, 1.2)
        
        if expected_profit <= total_cost * min_profit_ratio:
            return False
        
        # 使用 order_with_fund 替代直接 order
        order_with_fund(context, security, amount)
        
        # 只记录重要交易
        if abs(amount) >= 500 or abs(expected_profit) > 500:
            log.info(f"网格交易 - {security} {trade_type}: 数量={amount}, 价格={current_price:.3f}, 预期收益={expected_profit:.2f}")
        return True
        
    except Exception as e:
        log.error(f"执行网格交易失败 - {security}: {str(e)}")
        return False

def calculate_portfolio_volatility(context):
    """计算投资组合的波动率"""
    try:
        # 获取每个标的的波动率
        volatilities = []
        for security in context.stock_list:
            indicators = context.cache['technical_indicators'].get(security)
            if indicators and 'volatility' in indicators:
                volatilities.append(indicators['volatility'])
        
        # 计算平均波动率
        if volatilities:
            return sum(volatilities) / len(volatilities)
        return 0.02  # 默认波动率
    except Exception as e:
        log.error(f"计算投资组合波动率时出错: {str(e)}")
        return 0.02  # 默认波动率

def trade_money_fund(context):
    try:
        current_date = context.current_dt.date()
        if context.cache['last_money_fund_trade_date'] == current_date:
            return

        # 计算大额闲置资金阈值
        total_value = context.portfolio.total_value
        min_cash_buffer = total_value * 0.01  # 1%现金缓冲
        available_cash = context.portfolio.available_cash - min_cash_buffer

        # 只有当闲置资金大于1万元时才买入货基
        if available_cash > 10000:
            current_data = get_current_data()[context.money_fund]
            if current_data and not current_data.paused:
                current_price = current_data.last_price
                position = context.portfolio.positions.get(context.money_fund)
                current_amount = position.total_amount if position is not None else 0
                target_amount = int(available_cash / (current_price * 100)) * 100
                if abs(target_amount - current_amount) > 1000:
                    order_target(context.money_fund, target_amount)
                    context.cache['last_money_fund_trade_date'] = current_date
                    log.info(f"货基调整: {target_amount-current_amount:+d}份")
        # 其余情况不动
    except Exception as e:
        log.error(f"货基交易过程中发生错误: {str(e)}")

# 在买ETF时，若资金不足，先卖出货基
def order_with_fund(context, security, amount):
    current_data = get_current_data()[security]
    current_price = current_data.last_price
    required_cash = amount * current_price
    if context.portfolio.available_cash < required_cash:
        # 需要卖出货基
        fund_data = get_current_data()[context.money_fund]
        fund_price = fund_data.last_price
        sell_amount = int((required_cash - context.portfolio.available_cash) / fund_price / 100) * 100
        if sell_amount > 0:
            order_target(context.money_fund, context.portfolio.positions.get(context.money_fund, 0) - sell_amount)
    order(security, amount)

def check_fund_income(context):
    """
    检查货基收益情况
    """
    try:
        # 只在收盘前检查一次
        if context.current_dt.time() != datetime.time(15, 10):
            return
            
        if context.money_fund in context.portfolio.positions:
            position = context.portfolio.positions[context.money_fund]
            if position.total_amount > 0:
                current_data = get_current_data()[context.money_fund]
                current_price = current_data.last_price
                
                cost = position.avg_cost * position.total_amount
                current_value = current_price * position.total_amount
                daily_return = (current_value - cost) / cost
                
                # 只在收益率超过0.01%时记录日志
                if abs(daily_return) > 0.0001:
                    log.info(f"货基当日收益: {daily_return:.4%}")
                    
    except Exception as e:
        log.error(f"检查货基收益时发生错误: {str(e)}")

def get_market_state(context, security):
    """
    根据技术指标判断市场状态
    返回: 'uptrend', 'downtrend', 或 'oscillating'
    """
    try:
        # 更新技术指标
        indicators = update_technical_indicators(context, security)
        
        # 获取BIAS和ADX值
        bias = indicators.get('bias', 0)  # 使用get方法安全获取bias值
        adx = indicators.get('adx', 0)    # 使用get方法安全获取adx值
        plus_di = indicators.get('plus_di', 0)  # 使用get方法安全获取plus_di值
        minus_di = indicators.get('minus_di', 0)  # 使用get方法安全获取minus_di值
        
        # 获取该证券的BIAS阈值
        bias_threshold = context.bias_threshold[security]
        
        # 趋势判断标准
        TREND_THRESHOLD = 20  # ADX高于20即为趋势，放宽标准
        # 震荡市判定标准放宽
        if adx < TREND_THRESHOLD or abs(bias) < bias_threshold * 1.2:
            return 'oscillating'
        
        # 判断市场状态
        if adx > TREND_THRESHOLD:  # 存在明显趋势
            if plus_di > minus_di:  # 上升趋势
                if bias > bias_threshold:  # BIAS过高，可能超买
                    return 'oscillating'
                return 'uptrend'
            else:  # 下降趋势
                if bias < -bias_threshold:  # BIAS过低，可能超卖
                    return 'oscillating'
                return 'downtrend'
        else:  # ADX较低，无明显趋势
            if abs(bias) > bias_threshold:  # BIAS超出阈值
                if bias > bias_threshold:  # 可能超买
                    return 'downtrend'
                else:  # 可能超卖
                    return 'uptrend'
            return 'oscillating'  # 震荡市场
            
    except Exception as e:
        log.error(f"判断市场状态时出错: {str(e)}")
        return 'oscillating'  # 发生错误时默认返回震荡状态

def calculate_price_trend(context, security, period=20):
    """
    计算价格趋势
    返回: float, 正值表示上涨趋势，负值表示下跌趋势
    """
    try:
        # 获取历史价格数据
        hist_data = attribute_history(security, period, '1d', ['close'])
        prices = hist_data['close']
        
        # 计算价格变化率
        price_change = (prices[-1] - prices[0]) / prices[0]
        
        return price_change
        
    except Exception as e:
        log.error(f"计算价格趋势时出错: {str(e)}")
        return 0.0

def calculate_volume_trend(context, security, period=20):
    """
    计算成交量趋势
    返回: float, 大于1表示放量，小于1表示缩量
    """
    try:
        # 获取历史成交量数据
        hist_data = attribute_history(security, period, '1d', ['volume'])
        volumes = hist_data['volume']
        
        # 计算最近5日平均成交量与20日平均成交量的比值
        recent_avg = np.mean(volumes[-5:])
        total_avg = np.mean(volumes)
        
        return recent_avg / total_avg if total_avg > 0 else 1.0
        
    except Exception as e:
        log.error(f"计算成交量趋势时出错: {str(e)}")
        return 1.0

def calculate_volatility(context, security, period=20):
    prices = context.cache['technical_indicators'][security]['real_closes']
    returns = prices.pct_change().dropna()
    return returns.rolling(period).std().iloc[-1]

def get_volume_ratio(context, security, period=20):
    """计算量比，使用缓存数据"""
    try:
        # 使用缓存的量比
        indicators = context.cache['technical_indicators'].get(security)
        if indicators and 'volume_ratio' in indicators:
            return indicators['volume_ratio']
        
        # 如果缓存不存在，更新技术指标
        update_technical_indicators(context, security)
        return context.cache['technical_indicators'][security]['volume_ratio']
    except Exception as e:
        log.error(f"计算量比时出错: {str(e)}")
        return 1.0  # 默认量比为1

def get_bias(context, security):
    """计算BIAS，使用缓存数据"""
    try:
        # 使用缓存的BIAS
        indicators = context.cache['technical_indicators'].get(security)
        if indicators and 'bias' in indicators:
            return indicators['bias']
        
        # 如果缓存不存在，更新技术指标
        update_technical_indicators(context, security)
        return context.cache['technical_indicators'][security]['bias']
    except Exception as e:
        log.error(f"计算BIAS时出错: {str(e)}")
        return 0  # 默认BIAS为0

def check_trade_conditions(context, security, amount, trade_type):
    """
    检查交易条件
    params:
        trade_type: str, 'buy' 或 'sell'
    returns:
        bool, 是否满足交易条件
    """
    try:
        # 获取缓存的技术指标
        indicators = context.cache['technical_indicators'].get(security)
        if not indicators:
            update_technical_indicators(context, security)
            indicators = context.cache['technical_indicators'].get(security)
        
        # 获取各项指标
        volume_ratio = indicators.get('volume_ratio', 1.0)
        trend_strength = indicators.get('trend_strength', 20)
        volatility = indicators.get('volatility', 0.02)
        bias = indicators.get('bias', 0)
        
        # 基本条件检查
        if amount <= 0:
            return False
            
        # 买入条件
        if trade_type == 'buy':
            # 检查趋势强度
            if trend_strength < -30:  # 下跌趋势太强
                return False
                
            # 检查量比
            if volume_ratio < 0.8:  # 成交量不足
                return False
                
            # 检查BIAS
            if bias < -3:  # 超卖
                return True
                
            # 检查波动率
            if volatility > 0.03:  # 波动率过高
                return False
                
        # 卖出条件
        elif trade_type == 'sell':
            # 检查趋势强度
            if trend_strength > 30:  # 上涨趋势太强
                return False
                
            # 检查量比
            if volume_ratio > 2.0:  # 放量过大
                return False
                
            # 检查BIAS
            if bias > 3:  # 超买
                return True
                
            # 检查波动率
            if volatility > 0.03:  # 波动率过高
                return False
        
        return True  # 默认允许交易
        
    except Exception as e:
        log.error(f"检查交易条件时出错: {str(e)}")
        return False  # 出错时不允许交易

def calculate_expected_profit(context, security, amount, trade_type, current_price):
    """
    计算预期收益
    """
    try:
        # 获取网格参数
        market_state = context.market_states[security]
        grid_params = context.grid_params[security][market_state]
        
        # 计算网格间距
        atr = calculate_atr(context, security)
        grid_spacing = atr * grid_params['buy_atr_multiple']
        
        # 计算预期收益
        if trade_type == 'buy':
            expected_price = current_price * (1 + grid_spacing)
        else:
            expected_price = current_price * (1 - grid_spacing)
            
        expected_profit = abs(expected_price - current_price) * abs(amount)
        
        return expected_profit
        
    except Exception as e:
        log.error(f"计算预期收益时出错: {str(e)}")
        return 0.0

def calculate_daily_grid_prices(context):
    """
    计算每日网格价格
    """
    try:
        for security in context.stock_list:
            # 获取当前价格
            current_data = get_current_data()[security]
            if current_data.paused:
                continue
                
            current_price = current_data.last_price
            market_state = context.market_states[security]
            
            # 获取网格参数
            grid_params = context.grid_params[security][market_state]
            
            # 使用自适应网格参数
            layers, spacing, weight = adaptive_grid_params(context, security)
            
            # 计算ATR
            atr = calculate_atr(context, security)
            
            # 计算波动率因子
            try:
                # 获取历史波动率（60日）
                hist_data = attribute_history(security, 60, '1d', ['close'])
                hist_returns = hist_data['close'].pct_change().dropna()
                historical_volatility = hist_returns.std()
                
                # 获取当前波动率（20日）
                current_data = attribute_history(security, 20, '1d', ['close'])
                current_returns = current_data['close'].pct_change().dropna()
                current_volatility = current_returns.std()
                
                # 计算波动率因子
                volatility_sensitivity = 0.5
                volatility_factor = 1 + (current_volatility / historical_volatility - 1) * volatility_sensitivity
                volatility_factor = max(0.2, min(1.2, volatility_factor))  # 极致收窄
                
                # 调整网格间距
                spacing = max(0.08, spacing * volatility_factor)  # 最小0.08倍ATR
                
                # 层数提升
                layers = max(20, layers)
                
                # log.info(f"{security} 波动率调整 - 历史波动率: {historical_volatility:.4f}, 当前波动率: {current_volatility:.4f}, 调整因子: {volatility_factor:.2f}")
            except Exception as e:
                log.error(f"计算波动率因子时出错: {str(e)}")
                volatility_factor = 1.0
            
            # 初始化价格列表
            buy_prices = []
            sell_prices = []
            amounts = []
            
            # 计算基础网格金额
            total_value = context.portfolio.total_value
            base_grid_value = total_value * context.total_position_pct * weight
            base_amount = base_grid_value / layers / current_price  # 保留浮点数
            dynamic_amounts = []
            for i in range(layers):
                try:
                    # 买入价格递减
                    buy_price = current_price * (1 - spacing * atr * (i + 1))
                    # 卖出价格递增
                    sell_price = current_price * (1 + spacing * atr * (i + 1))
                    
                    # 确保网格间距不小于最小间距
                    if i > 0 and len(buy_prices) > 0 and len(sell_prices) > 0:
                        last_buy = buy_prices[-1]
                        last_sell = sell_prices[-1]
                        min_spacing = current_price * context.min_grid_spacing_pct
                        
                        if abs(buy_price - last_buy) < min_spacing:
                            buy_price = last_buy - min_spacing
                        if abs(sell_price - last_sell) < min_spacing:
                            sell_price = last_sell + min_spacing
                    
                    # 计算每层的交易数量
                    dynamic_amounts.append(max(int(base_amount), context.min_trade_amount))
                    buy_prices.append(buy_price)
                    sell_prices.append(sell_price)
                except Exception as e:
                    log.error(f"计算第{i}层网格价格时出错: {str(e)}")
                    continue
            
            # 存储计算结果
            context.daily_grid_prices[security] = {
                'buy_prices': buy_prices,
                'sell_prices': sell_prices,
                'amounts': dynamic_amounts
            }
            
            # 记录网格价格信息
            # log.info(f"{security} 网格价格计算完成:")
            # log.info(f"市场状态: {market_state}")
            # log.info(f"网格层数: {len(buy_prices)}")
            if len(buy_prices) > 0:
                # log.info(f"买入价格范围: {buy_prices[-1]:.3f} - {buy_prices[0]:.3f}")
                # log.info(f"卖出价格范围: {sell_prices[0]:.3f} - {sell_prices[-1]:.3f}")
                # log.info(f"每层交易数量: {dynamic_amounts[0]}")
                pass
            
    except Exception as e:
        log.error(f"计算日网格价格时出错: {str(e)}")

def calculate_dynamic_grid_prices(context, security, current_price):
    """计算动态网格价格，实现非对称网格"""
    try:
        grid_status = context.grid_status[security]
        market_state = context.market_states[security]
        
        # 获取技术指标
        indicators = context.cache['technical_indicators'].get(security, {})
        adx = indicators.get('adx', 25)
        plus_di = indicators.get('plus_di', 20)
        minus_di = indicators.get('minus_di', 20)
        
        # 使用adaptive_grid_params获取基础参数
        layers, base_spacing, weight = adaptive_grid_params(context, security)
        
        # 获取基础网格参数
        grid_params = context.grid_params[security][market_state]
        base_buy_multiple = grid_params['buy_atr_multiple']
        base_sell_multiple = grid_params['sell_atr_multiple']
        
        # 计算ATR
        atr = calculate_atr(context, security)
        
        # 根据趋势调整买卖网格间距
        if adx > 25:
            if plus_di > minus_di * 1.1:  # 上升趋势
                buy_multiple = base_buy_multiple * 0.8  # 买入网格较窄
                sell_multiple = base_sell_multiple * 1.2  # 卖出网格较宽
            elif minus_di > plus_di * 1.1:  # 下降趋势
                buy_multiple = base_buy_multiple * 1.2  # 买入网格较宽
                sell_multiple = base_sell_multiple * 0.8  # 卖出网格较窄
            else:
                buy_multiple = base_buy_multiple
                sell_multiple = base_sell_multiple
        else:
            buy_multiple = base_buy_multiple
            sell_multiple = base_sell_multiple
        
        # 计算网格层数
        layers = grid_params['layers']
        
        # 计算动态网格价格
        buy_prices = []
        sell_prices = []
        amounts = []
        
        # 计算基础网格金额
        total_value = context.portfolio.total_value
        base_grid_value = total_value * context.total_position_pct * weight
        base_amount = base_grid_value / layers / current_price  # 保留浮点数
        
        for i in range(layers):
            # 买入价格递减
            buy_price = current_price * (1 - buy_multiple * atr * (i + 1))
            # 卖出价格递增
            sell_price = current_price * (1 + sell_multiple * atr * (i + 1))
            
            # 确保网格间距不小于最小间距
            if i > 0 and len(buy_prices) > 0 and len(sell_prices) > 0:
                last_buy = buy_prices[-1]
                last_sell = sell_prices[-1]
                min_spacing = current_price * context.min_grid_spacing_pct
                
                if abs(buy_price - last_buy) < min_spacing:
                    buy_price = last_buy - min_spacing
                if abs(sell_price - last_sell) < min_spacing:
                    sell_price = last_sell + min_spacing
            
            # 计算每层的交易数量
            dynamic_amount = int(base_amount * calculate_dynamic_grid_weight(context, security, i) / 100) * 100
            dynamic_amount = max(dynamic_amount, context.min_trade_amount)
            
            buy_prices.append(buy_price)
            sell_prices.append(sell_price)
            amounts.append(dynamic_amount)
        
        # 更新网格状态
        grid_status['dynamic_buy_prices'] = buy_prices
        grid_status['dynamic_sell_prices'] = sell_prices
        grid_status['dynamic_amounts'] = amounts
        
    except Exception as e:
        log.error(f"计算动态网格价格时出错: {str(e)}")

def check_risk_limits(context, security):
    """
    检查风险限制
    """
    try:
        # 获取当前持仓
        position = context.portfolio.positions.get(security)
        if position is None:
            return True
            
        current_amount = position.total_amount
        if current_amount == 0:
            return True
            
        # 获取当前市场状态
        market_state = context.market_states[security]
        
        # 获取该市场状态下的持仓限制
        position_limits = context.position_limits[market_state]
        
        # 计算当前持仓比例
        current_value = position.total_amount * position.price
        total_value = context.portfolio.total_value
        current_position_pct = current_value / total_value
        
        # 检查是否超出限制
        if current_position_pct < position_limits['lower']:
            # log.info(f"{security} 持仓比例 {current_position_pct:.2%} 低于下限 {position_limits['lower']:.2%}")
            return True
        elif current_position_pct > position_limits['upper']:
            # log.info(f"{security} 持仓比例 {current_position_pct:.2%} 超过上限 {position_limits['upper']:.2%}")
            return False
            
        return True
        
    except Exception as e:
        log.error(f"检查风险限制时出错: {str(e)}")
        return False

# 新增：动态计算网格触发阈值
import numpy as np

def get_recent_win_rate(context, security, window=10):
    grid_status = context.grid_status.get(security, {})
    win_list = grid_status.get('win_list', [])
    if len(win_list) < window:
        return 0.5
    return np.mean(win_list[-window:])

def calculate_adaptive_trigger(context, security):
    """综合波动率、胜率、成本计算动态触发阈值"""
    win_rate = get_recent_win_rate(context, security, window=20)
    atr = calculate_atr(context, security)
    # 取60日ATR为历史波动率
    try:
        hist_data = attribute_history(security, 60, '1d', ['close'])
        closes = hist_data['close']
        historical_atr = np.mean(np.abs(closes.diff().dropna()))
    except:
        historical_atr = atr
    volatility_factor = atr / (historical_atr if historical_atr > 0 else atr)
    base_ratio = np.clip(0.001 * (1 + (0.5 - win_rate)*2), 0.0008, 0.0015)
    adaptive_ratio = base_ratio * volatility_factor
    return max(adaptive_ratio, context.min_grid_spacing_pct)

def calculate_dynamic_grid_weight(context, security, layer_index):
    market_state = context.market_states[security]
    grid_status = context.grid_status[security]
    buy_prices = grid_status.get('dynamic_buy_prices', [])
    # 波动率权重
    volatility_ratio = calculate_volatility(context, security) / 0.02
    vol_weight = np.clip(0.8 + (volatility_ratio - 1)*0.3, 0.8, 1.5)
    # 胜率权重
    win_rate = grid_status.get('layer_win_rate', {}).get(layer_index, 0.5)
    win_weight = 0.7 + (win_rate - 0.5)*2
    # 流动性补偿
    current_volume = context.cache['technical_indicators'][security].get('volume_ratio', 1.0)
    liquidity_factor = np.clip(current_volume / 1.2, 0.8, 1.5)
    # 趋势权重
    if market_state == 'uptrend':
        trend_weight = 1.5 - 0.4 * (layer_index / max(1, len(buy_prices)))
    elif market_state == 'downtrend':
        trend_weight = 0.6 + 0.4 * layer_index
    else:
        trend_weight = 1.0
    dynamic_weight = vol_weight * win_weight * trend_weight * liquidity_factor
    return np.clip(dynamic_weight, 0.5, 3.0)

def recalculate_grid_fund(context, security, current_price):
    """
    资金再平衡：动态调整每层网格的资金分配
    """
    try:
        market_state = context.market_states[security]
        grid_params = context.grid_params[security][market_state]
        layers, spacing, weight = adaptive_grid_params(context, security)
        atr = calculate_atr(context, security)
        total_value = context.portfolio.total_value
        base_grid_value = total_value * context.total_position_pct * weight * 0.5

        buy_prices = context.daily_grid_prices[security]['buy_prices']
        if not buy_prices or len(buy_prices) != layers:
            # 若网格层数有变化，建议重新生成网格
            calculate_daily_grid_prices(context)
            buy_prices = context.daily_grid_prices[security]['buy_prices']

        base_amount = int(base_grid_value / layers / current_price / 100) * 100
        dynamic_amounts = []
        for i in range(len(buy_prices)):
            weight = calculate_dynamic_grid_weight(context, security, i)
            layer_amount = int(base_amount * weight / 100) * 100
            dynamic_amounts.append(max(layer_amount, context.min_trade_amount))
        context.daily_grid_prices[security]['amounts'] = dynamic_amounts
        # 移除冗余日志
        # log.info(f"{security} 再平衡动态资金分配: {[f'{a//100}手' for a in dynamic_amounts]}")
    except Exception as e:
        log.error(f"recalculate_grid_fund出错: {str(e)}")

def check_trend_strength(context, security):
    """检查趋势强度"""
    try:
        indicators = context.cache['technical_indicators'][security]
        adx = indicators.get('adx', 0)
        plus_di = indicators.get('plus_di', 0)
        minus_di = indicators.get('minus_di', 0)
        
        if adx > 25:
            if plus_di > minus_di:
                return 'strong_uptrend'
            elif minus_di > plus_di:
                return 'strong_downtrend'
        return 'weak_trend'
    except Exception as e:
        log.error(f"检查趋势强度时出错: {str(e)}")
        return 'weak_trend'

def check_bias_extreme(context, security):
    """检查BIAS极端值"""
    try:
        bias = context.cache['technical_indicators'][security].get('bias', 0)
        bias_threshold = context.bias_threshold[security]
        
        if abs(bias) > bias_threshold * 1.5:
            return True
        return False
    except Exception as e:
        log.error(f"检查BIAS极端值时出错: {str(e)}")
        return False

def should_pause_grid_trading(context, security):
    """检查是否需要暂停网格交易"""
    try:
        # 获取技术指标
        indicators = context.cache['technical_indicators'].get(security, {})
        adx = indicators.get('adx', 25)
        plus_di = indicators.get('plus_di', 20)
        minus_di = indicators.get('minus_di', 20)
        trend_strength = indicators.get('trend_strength', 20)
        
        # 加强趋势判断条件
        if adx > 30:  # 从25提高到30
            if plus_di > minus_di * 1.2:  # 从1.1提高到1.2
                return True  # 强上升趋势
            elif minus_di > plus_di * 1.2:  # 从1.1提高到1.2
                return True  # 强下降趋势
        
        # 增加趋势强度判断
        if trend_strength > 30:  # 从25提高到30
            return True
            
        return False
        
    except Exception as e:
        log.error(f"检查是否需要暂停网格交易时出错: {str(e)}")
        return False

def dynamic_grid_allocation(context, security):
    """动态分配网格交易资金"""
    try:
        market_state = context.market_states[security]
        trend_strength = get_trend_strength(context, security)
        volatility = calculate_volatility(context, security)
        
        # 基础分配比例
        if market_state == 'oscillating':
            base_allocation = 0.5  # 震荡市分配50%资金
        else:
            base_allocation = 0.2  # 趋势市分配20%资金
            
        # 根据趋势强度调整
        if abs(trend_strength) > 30:
            base_allocation *= 0.8  # 强趋势时降低分配比例
            
        # 根据波动率调整
        if volatility > 0.02:
            base_allocation *= 1.1  # 高波动时增加分配比例
        else:
            base_allocation *= 0.9  # 低波动时降低分配比例
            
        # 确保分配比例在合理范围内
        base_allocation = max(0.1, min(0.7, base_allocation))
        
        return base_allocation
        
    except Exception as e:
        log.error(f"动态分配网格资金时出错: {str(e)}")
        return 0.2  # 出错时返回默认值

def update_grid_allocation(context):
    """更新所有标的的网格资金分配"""
    try:
        total_value = context.portfolio.total_value
        for security in context.stock_list:
            if security in context.grid_status:
                # 计算新的分配比例
                allocation_ratio = dynamic_grid_allocation(context, security)
                
                # 计算新的网格资金
                new_grid_fund = total_value * allocation_ratio
                
                # 更新网格资金
                context.grid_status[security]['grid_fund'] = new_grid_fund
                
                # 重新计算网格参数
                optimize_grid_by_market_state(context, security)
                
    except Exception as e:
        log.error(f"更新网格资金分配时出错: {str(e)}")

def adaptive_grid_params(context, security):
    """计算自适应网格参数"""
    try:
        # 获取当前市场状态
        market_state = context.market_states[security]
        
        # 获取基础网格参数
        base_params = context.grid_params[security][market_state]
        base_layers = base_params['layers']
        base_spacing = base_params['buy_atr_multiple']
        base_weight = base_params['grid_value_pct']
        
        # 计算当前波动率
        volatility = calculate_volatility(context, security)
        
        # 计算历史波动率（60日）
        try:
            hist_data = attribute_history(security, 60, '1d', ['close'])
            hist_returns = hist_data['close'].pct_change().dropna()
            hist_volatility = hist_returns.std()
        except:
            hist_volatility = volatility
        
        # 波动率自适应调整
        volatility_ratio = volatility / (hist_volatility if hist_volatility > 0 else volatility)
        
        # 调整网格层数
        adaptive_layers = max(8, int(base_layers * (1 - (volatility_ratio - 1) * 0.3)))
        
        # 调整网格间距
        adaptive_spacing = base_spacing * (1 + (volatility_ratio - 1) * 0.5)
        adaptive_spacing = max(0.1, min(0.5, adaptive_spacing))
        
        # 调整资金权重
        adaptive_weight = base_weight * (1 + (volatility_ratio - 1) * 0.2)
        adaptive_weight = max(0.02, min(0.1, adaptive_weight))
        
        return adaptive_layers, adaptive_spacing, adaptive_weight
        
    except Exception as e:
        log.error(f"计算自适应网格参数时出错: {str(e)}")
        return base_layers, base_spacing, base_weight

def execute_rebalance(context):
    """执行投资组合再平衡"""
    try:
        # 计算风险平价权重
        weights = calculate_risk_parity_weights(context)
        
        # 计算每个标的的目标市值
        total_value = context.portfolio.total_value
        available_cash = context.portfolio.available_cash
        
        # 执行再平衡
        for security in context.stock_list:
            try:
                # 计算目标市值
                target_value = total_value * context.total_position_pct * weights.get(security, 0)
                
                # 获取当前价格
                current_data = get_current_data()[security]
                if current_data.paused:
                    log.warning(f"{security} 当前暂停交易，跳过再平衡")
                    continue
                    
                current_price = current_data.last_price
                
                # 计算目标数量（确保是100的整数倍）
                target_amount = int(target_value / current_price / 100) * 100
                
                # 获取当前持仓
                position = context.portfolio.positions.get(security)
                current_amount = position.total_amount if position else 0
                
                # 计算需要调整的数量
                adjust_amount = target_amount - current_amount
                
                if abs(adjust_amount) >= 100:  # 确保调整数量至少为100股
                    order_value = abs(adjust_amount) * current_price
                    if order_value <= available_cash:  # 确保资金充足
                        order(security, adjust_amount)
                        log.info(f"再平衡 {security}: 调整数量={adjust_amount}, 价格={current_price:.3f}, 金额={order_value:.2f}")
                    else:
                        log.warning(f"再平衡 {security}: 资金不足，需要{order_value:.2f}，可用{available_cash:.2f}")
                else:
                    log.info(f"再平衡 {security}: 调整数量小于100股，跳过调整")
                    
            except Exception as e:
                log.error(f"处理标的 {security} 再平衡时发生错误: {str(e)}")
                continue
                
        log.info("再平衡完成")
        context.last_rebalance_date = context.current_dt.date()
        
    except Exception as e:
        log.error(f"执行再平衡时发生错误: {str(e)}")
        raise
