# 导入所需的库
import numpy as np
import pandas as pd
import datetime
import math
import talib
import pickle  # 导入pickle模块用于持久化处理


# 创建全局对象g，用于存储全局变量
class GlobalVars:
    def __init__(self):
        pass

g = GlobalVars()

# 持久化相关常量
PICKLE_FILE_NAME = 'strategy_M1_state.pkl'

# 保存策略状态到文件
def save_strategy_state():
    """保存策略状态到文件，用于持久化"""
    try:
        # 获取研究路径
        research_path = get_research_path()
        pickle_path = research_path + PICKLE_FILE_NAME

        # 创建要保存的状态字典，排除以__开头的私有变量
        state_dict = {}
        for key, value in vars(g).items():
            if not key.startswith('__'):
                try:
                    # 尝试序列化，如果失败则跳过该变量
                    pickle.dumps(value)
                    state_dict[key] = value
                except:
                    log.warning(f"变量 {key} 无法序列化，跳过保存")

        # 保存状态到文件
        with open(pickle_path, 'wb') as f:
            pickle.dump(state_dict, f, -1)
        log.info(f"策略状态已保存到 {pickle_path}")
    except Exception as e:
        log.error(f"保存策略状态时出错: {str(e)}")

# 从文件加载策略状态
def load_strategy_state():
    """从文件加载策略状态，用于持久化恢复"""
    try:
        # 获取研究路径
        research_path = get_research_path()
        pickle_path = research_path + PICKLE_FILE_NAME

        # 检查文件是否存在
        try:
            with open(pickle_path, 'rb') as f:
                state_dict = pickle.load(f)

            # 恢复状态到全局变量g
            for key, value in state_dict.items():
                setattr(g, key, value)

            log.info(f"已从 {pickle_path} 恢复策略状态")
            return True
        except FileNotFoundError:
            log.info(f"未找到策略状态文件 {pickle_path}，将使用初始化设置")
            return False
        except Exception as e:
            log.error(f"加载策略状态时出错: {str(e)}")
            return False
    except Exception as e:
        log.error(f"加载策略状态时出错: {str(e)}")
        return False

# 初始化函数
def initialize(context):
    """初始化函数"""
    # 尝试加载之前保存的策略状态
    state_loaded = load_strategy_state()

    # 设置基准
    set_benchmark('000300.XSHG')  # 沪深300作为基准

    # 注意：Ptrade实盘环境不支持set_fixed_slippage和set_commission函数
    # 滑点和手续费由平台统一设置，无需在策略中指定

    # 设置交易标的（必须最先定义）
    if not state_loaded or not hasattr(g, 'stock_list'):
        g.stock_list = ['513100.XSHG',  # 纳斯达克ETF
                       '510300.XSHG',  # 沪深300ETF
                       '518880.XSHG']  # 黄金ETF
        set_universe(g.stock_list)

    # 如果没有加载到状态或者需要重新初始化某些变量，设置技术指标参数
    if not hasattr(g, 'bias_period'):
        g.bias_period = 24  # BIAS周期
    if not hasattr(g, 'adx_period'):
        g.adx_period = 14   # ADX周期
    if not hasattr(g, 'atr_period'):
        g.atr_period = 12   # ATR周期

    # 添加动量策略参数
    if not hasattr(g, 'momentum_boost_factor'):
        g.momentum_boost_factor = 1.06  # 动量增强系数
    if not hasattr(g, 'max_momentum_allocation_pct'):
        g.max_momentum_allocation_pct = 0.199    # 最大动量分配比例
    if not hasattr(g, 'momentum_threshold'):
        g.momentum_threshold = 0.03    # 动量信号阈值
    if not hasattr(g, 'momentum_M1_lookback'):
        g.momentum_M1_lookback = 5     # 定义过去1个月的回溯期 (约21个交易日)

    # 添加缓存字典
    if not hasattr(g, 'cache'):
        g.cache = {
            'technical_indicators': {},
            'last_update_date': None,
            'price_data': {},
            'last_minute': -1,
            'last_money_fund_trade_date': None,
            'last_volatility_calc': {},  # 新增：缓存波动率计算
            'last_trend_calc': {},       # 新增：缓存趋势计算
            'last_rebalance_check': None, # 新增：缓存再平衡检查
            'last_momentum_check': None   # 新增：缓存动量检查
        }

    # 初始化市场状态字典
    if not hasattr(g, 'market_states'):
        g.market_states = {security: 'oscillating' for security in g.stock_list}

    # 初始化初始买入标志
    if not hasattr(g, 'initial_buy_needed'):
        g.initial_buy_needed = True

    # 初始化数据加载标志
    if not hasattr(g, 'data_loaded'):
        g.data_loaded = False

    # 注意：Ptrade实盘环境不支持在initialize阶段调用get_history
    # 历史数据将在第一次market_open时加载

    # 设置参数 (更新自 JSON)
    if not hasattr(g, 'total_position_pct'):
        g.total_position_pct = 0.891  # 总仓位
    if not hasattr(g, 'reserve_position_pct'):
        g.reserve_position_pct = 1 - g.total_position_pct  # 备用资金

    # 设置BIAS阈值
    if not hasattr(g, 'bias_threshold'):
        g.bias_threshold = {
            '513100.XSHG': 0.1,   # 纳斯达克ETF阈值±10%
            '510300.XSHG': 0.1,  # 沪深300ETF阈值±10%
            '518880.XSHG': 0.1   # 黄金ETF阈值±10%
        }

    # 设置风控参数 (更新自 JSON)
    if not hasattr(g, 'single_stock_stop_loss'):
        g.single_stock_stop_loss = 0.105  # 单个标的止损
    if not hasattr(g, 'portfolio_stop_loss'):
        g.portfolio_stop_loss = 0.06  # 组合止损

    # 设置因子权重 (更新自 JSON)
    if not hasattr(g, 'weight_trend'):
        g.weight_trend = 0.302
    if not hasattr(g, 'weight_direction'):
        g.weight_direction = 0.498
    if not hasattr(g, 'weight_volatility'):
        g.weight_volatility = 0.056
    if not hasattr(g, 'weight_volume'):
        g.weight_volume = 0.058
    if not hasattr(g, 'weight_bias'):
        g.weight_bias = 0.086

    # 设置趋势调整参数 (更新自 JSON)
    if not hasattr(g, 'uptrend_base_extra'):
        g.uptrend_base_extra = 0.198
    if not hasattr(g, 'downtrend_base_reduction'):
        g.downtrend_base_reduction = 0.193
    if not hasattr(g, 'downtrend_pct_limit'):
        g.downtrend_pct_limit = 0.309

    # 设置调整系数 (更新自 JSON)
    if not hasattr(g, 'reserve_scale_down_factor'):
        g.reserve_scale_down_factor = 0.5
    if not hasattr(g, 'reserve_scale_up_factor'):
        g.reserve_scale_up_factor = 1.25
    if not hasattr(g, 'strong_trend_boost'):
        g.strong_trend_boost = 1.53
    if not hasattr(g, 'oversold_reduction_factor'):
        g.oversold_reduction_factor = 0.930
    if not hasattr(g, 'momentum_strength_factor_multiplier'):
        g.momentum_strength_factor_multiplier = 1.95

    # BIAS震荡判断放大系数
    if not hasattr(g, 'bias_oscillation_scale_factor'):
        g.bias_oscillation_scale_factor = 1.445

    # 动态配置: 趋势持续性计算的ADX基准
    if not hasattr(g, 'alloc_persistence_adx_base'):
        g.alloc_persistence_adx_base = 35

    # 置信度计算参数
    if not hasattr(g, 'conf_trend_base'):
        g.conf_trend_base = 40
    if not hasattr(g, 'conf_trend_weight'):
        g.conf_trend_weight = 0.4
    if not hasattr(g, 'conf_bias_mult'):
        g.conf_bias_mult = 2
    if not hasattr(g, 'conf_bias_weight'):
        g.conf_bias_weight = 0.3
    if not hasattr(g, 'conf_adx_base'):
        g.conf_adx_base = 40
    if not hasattr(g, 'conf_adx_weight'):
        g.conf_adx_weight = 0.3
    if not hasattr(g, 'conf_vol_offset'):
        g.conf_vol_offset = 0.5
    if not hasattr(g, 'conf_vol_weight'):
        g.conf_vol_weight = 0.2

    # 运行时需要的变量
    if not hasattr(g, 'last_rebalance_date'):
        g.last_rebalance_date = None  # 上次再平衡时间
    if not hasattr(g, 'stop_trading'):
        g.stop_trading = False  # 是否停止交易的标志

    # 添加国债逆回购相关参数
    if not hasattr(g, 'reverse_repo_codes'):
        # 国债逆回购代码：上海1天、深圳1天
        g.reverse_repo_codes = {
            'SH_1D': '204001.XSHG',  # 上海1天期国债逆回购
            'SZ_1D': '131810.XSHE'   # 深圳1天期国债逆回购
        }
    if not hasattr(g, 'min_reverse_repo_amount'):
        g.min_reverse_repo_amount = 1000  # 最小逆回购金额（1000元）
    if not hasattr(g, 'reverse_repo_reserve'):
        g.reverse_repo_reserve = 1010  # 保留资金（用于新债中签等）

    # 设置定时任务
    # 注意：如果Ptrade回测是以日线级别运行，则所有run_daily设置的早于15:00的时间点，
    # 其实际执行都会被推迟到15:00。这可能会改变策略行为（例如，原先设想的盘前操作变为盘后）。
    # 如果Ptrade回测支持并运行在分钟级别，则时间设置会按预期工作。
    run_daily(context, market_open, time='9:30')
    run_daily(context, check_stop_loss, time='9:31')
    run_daily(context, reverse_repurchase, time='15:10')  # 盘后进行国债逆回购
    run_daily(context, monitor_strategy_performance, time='15:00')

    # 设置每周一动量调整
    run_daily(context, daily_momentum_check, time='9:35')

    # 设置最小交易数量
    if not hasattr(g, 'min_trade_amount'):
        g.min_trade_amount = 100

    # 设置是否已经完成初始建仓的标志
    if not hasattr(g, 'initial_position_established'):
        g.initial_position_established = False

# 每日检查是否是周一，如果是则执行动量叠加策略
def daily_momentum_check(context):
    """
    模拟JoinQuant的run_weekly功能，在每周一执行动量叠加策略

    注意：Ptrade平台没有直接的run_weekly函数，所以我们通过run_daily配合日期检查来实现
    与JQ版本的区别：
    1. JQ版本使用run_weekly(apply_momentum_overlay, weekday=1)直接设置每周一执行
    2. Ptrade版本通过检查当天是否为周一(weekday==0)来实现相同功能
    """
    try:
        current_date = context.blotter.current_dt.date()

        # 检查是否已经执行过本周的动量调整
        if g.cache['last_momentum_check'] == current_date:
            return

        # 检查当天是否为周一
        if context.blotter.current_dt.weekday() == 0:  # 0代表周一
            log.info("执行周一动量策略调整")
            apply_momentum_overlay(context)
            # 更新最后检查日期
            g.cache['last_momentum_check'] = current_date
            # 执行持久化保存
            save_strategy_state()

        # 处理节假日后的第一个交易日情况
        # 如果上次检查日期不为空，且与当前日期相差超过7天，也执行动量调整
        elif g.cache['last_momentum_check'] is not None:
            days_since_last_check = (current_date - g.cache['last_momentum_check']).days
            if days_since_last_check > 7:
                log.info(f"距离上次动量调整已过{days_since_last_check}天，可能是节假日后首个交易日，执行动量策略调整")
                apply_momentum_overlay(context)
                # 更新最后检查日期
                g.cache['last_momentum_check'] = current_date
                # 执行持久化保存
                save_strategy_state()
    except Exception as e:
        log.error(f"执行周一动量检查时出错: {str(e)}")

def preload_historical_data(context):
    """预加载历史数据"""
    try:
        # 获取所需的最长周期
        max_period = max(60, g.bias_period, g.adx_period*2, g.atr_period*2)

        # 获取当前日期
        current_dt = context.blotter.current_dt

        # 计算开始日期（向前推max_period个交易日）
        # 注意：在实盘环境中，我们使用一个足够长的时间范围来确保获取足够的数据
        # 将日期转换为字符串格式，符合Ptrade API要求
        end_date_str = current_dt.strftime('%Y-%m-%d')
        start_date_str = (current_dt - datetime.timedelta(days=max_period * 2)).strftime('%Y-%m-%d')

        # 一次性获取所有需要的数据
        for security in g.stock_list:
            try:
                # 使用get_price替代get_history
                # 根据Ptrade API文档，get_price可以在任何阶段使用
                hist_data = get_price(security,
                                     start_date=start_date_str,
                                     end_date=end_date_str,
                                     frequency='daily',
                                     fields=['high', 'low', 'close', 'volume'],
                                     fq='pre')

                # 确保获取到足够的数据
                if isinstance(hist_data, pd.DataFrame) and len(hist_data) < max_period:
                    log.warning(f"获取的{security}历史数据不足，仅获取到{len(hist_data)}条记录，需要{max_period}条")

                # 存储到缓存
                g.cache['price_data'][security] = hist_data

                # 计算并缓存初始技术指标
                calculate_initial_indicators(context, security, hist_data)

                log.info(f"成功加载{security}的历史数据，共{len(hist_data) if isinstance(hist_data, pd.DataFrame) else 0}条记录")

            except Exception as e:
                log.error(f"预加载 {security} 历史数据时出错: {str(e)}")
                continue

    except Exception as e:
        log.error(f"预加载历史数据时出错: {str(e)}")

def calculate_initial_indicators(context, security, hist_data):
    """
    计算并缓存初始技术指标

    注意：Ptrade的get_history在设置fq='pre'时返回的价格已经是前复权后的价格，
    不需要再使用factor进行复权处理
    """
    try:
        # 直接使用前复权后的价格数据
        real_closes = hist_data['close']
        real_highs = hist_data['high']
        real_lows = hist_data['low']

        # 使用talib计算ADX
        adx = talib.ADX(real_highs.values, real_lows.values, real_closes.values, timeperiod=g.adx_period)
        plus_di = talib.PLUS_DI(real_highs.values, real_lows.values, real_closes.values, timeperiod=g.adx_period)
        minus_di = talib.MINUS_DI(real_highs.values, real_lows.values, real_closes.values, timeperiod=g.adx_period)

        # 使用talib计算ATR
        atr = talib.ATR(real_highs.values, real_lows.values, real_closes.values, timeperiod=g.atr_period)
        new_atr_value = atr[-1]

        # 计算波动率（当前和历史）
        returns = real_closes.pct_change().dropna()
        current_volatility = returns[-20:].std()
        historical_volatility = returns.std()

        # 计算其他指标
        ma20 = real_closes[-20:].mean()
        ma60 = real_closes.mean()
        latest_price = real_closes[-1]
        bias = (latest_price - ma20) / ma20

        # 计算平均成交量
        avg_volume = hist_data['volume'][-20:].mean()

        # 计算量比
        current_volume = hist_data['volume'][-1]
        volume_5d_avg = hist_data['volume'][-5:].mean()
        volume_ratio = current_volume / volume_5d_avg if volume_5d_avg > 0 else 1.0

        # 计算趋势强度
        trend_strength = adx[-20:].mean()

        # 计算M1动量因子
        try:
            if len(real_closes) >= g.momentum_M1_lookback:
                momentum_M1_value = (real_closes.iloc[-1] / real_closes.iloc[-g.momentum_M1_lookback]) - 1
            else:
                momentum_M1_value = 0.0  # 数据不足时返回0
        except Exception as e:
            log.error(f"计算{security}的M1动量因子时出错: {str(e)}")
            momentum_M1_value = 0.0  # 出错时返回0

        # 存储计算结果
        g.cache['technical_indicators'][security] = {
            'bias': bias,
            'adx': adx[-1] if not np.isnan(adx[-1]) else 25,
            'plus_di': plus_di[-1] if not np.isnan(plus_di[-1]) else 20,
            'minus_di': minus_di[-1] if not np.isnan(minus_di[-1]) else 20,
            'atr': new_atr_value if not np.isnan(new_atr_value) else 0.0,
            'ma20': ma20,
            'ma60': ma60,
            'volatility': current_volatility,
            'historical_volatility': historical_volatility,
            'avg_volume': avg_volume,
            'trend_strength': trend_strength,
            'latest_price': latest_price,
            'real_closes': real_closes,
            'volume_ratio': volume_ratio,
            'momentum_M1': momentum_M1_value
        }

    except Exception as e:
        log.error(f"计算初始技术指标时出错: {str(e)}")

def should_rebalance(context):
    """判断是否需要再平衡"""
    try:
        current_date = context.blotter.current_dt.date()

        # 使用缓存检查再平衡
        if g.cache['last_rebalance_check'] == current_date:
            return False

        # 修改为每月最后一个交易日进行再平衡
        current_month = current_date.month
        next_month = current_month + 1 if current_month < 12 else 1
        next_month_first_day = datetime.date(current_date.year + (current_month == 12), next_month, 1)
        current_month_first_day = datetime.date(current_date.year, current_date.month, 1)
        month_trading_days = get_trade_days(start_date=current_month_first_day, end_date=next_month_first_day - datetime.timedelta(days=1))

        if len(month_trading_days) == 0:
            return False

        last_trading_day = month_trading_days[-1]
        if current_date == last_trading_day:
            if g.last_rebalance_date is not None:
                if g.last_rebalance_date.year == current_date.year and g.last_rebalance_date.month == current_date.month:
                    return False
            g.cache['last_rebalance_check'] = current_date
            return True
        return False
    except Exception as e:
        log.error(f"判断再平衡时出错: {str(e)}")
        return False

def calculate_volatility(context, security, period=20):
    """计算波动率，使用缓存优化"""
    try:
        # 检查缓存
        cache_key = f"{security}_{period}"
        if cache_key in g.cache['last_volatility_calc']:
            last_calc = g.cache['last_volatility_calc'][cache_key]
            if last_calc['date'] == context.blotter.current_dt.date():
                return last_calc['value']

        # 计算波动率
        prices = g.cache['technical_indicators'][security]['real_closes']
        returns = prices.pct_change().dropna()
        volatility = returns.rolling(period).std().iloc[-1]

        # 更新缓存
        g.cache['last_volatility_calc'][cache_key] = {
            'date': context.blotter.current_dt.date(),
            'value': volatility
        }

        return volatility
    except Exception as e:
        log.error(f"计算波动率时出错: {str(e)}")
        return 0.02  # 默认波动率

def get_trend_strength(context, security, period=20):
    """计算趋势强度，使用缓存优化"""
    try:
        # 检查缓存
        cache_key = f"{security}_{period}"
        if cache_key in g.cache['last_trend_calc']:
            last_calc = g.cache['last_trend_calc'][cache_key]
            if last_calc['date'] == context.blotter.current_dt.date():
                return last_calc['value']

        # 使用缓存的趋势强度
        indicators = g.cache['technical_indicators'].get(security)
        if indicators and 'trend_strength' in indicators:
            trend_strength = indicators['trend_strength']
        else:
            update_technical_indicators(context, security)
            indicators = g.cache['technical_indicators'].get(security)
            trend_strength = indicators.get('trend_strength', 20)

        # 更新缓存
        g.cache['last_trend_calc'][cache_key] = {
            'date': context.blotter.current_dt.date(),
            'value': trend_strength
        }

        return trend_strength
    except Exception as e:
        log.error(f"计算趋势强度时出错: {str(e)}")
        return 20  # 默认中等趋势

# 优化技术指标计算
def update_technical_indicators(context, security):
    """
    批量更新技术指标，增加缓存机制和调试日志

    注意：Ptrade的get_price在设置fq='pre'时返回的价格已经是前复权后的价格，
    不需要再使用factor进行复权处理
    """
    current_dt = context.blotter.current_dt
    current_date = current_dt.date()

    # 如果不是新的交易日，且已有缓存，直接返回
    if (g.cache.get('last_update_date') == current_date and
        security in g.cache.get('technical_indicators', {})):
        return g.cache['technical_indicators'][security]

    try:
        # 获取所需的最长周期
        max_period = max(60, g.bias_period, g.adx_period*2, g.atr_period*2)

        # 计算开始日期（向前推max_period个交易日）
        # 将日期转换为字符串格式，符合Ptrade API要求
        end_date_str = current_dt.strftime('%Y-%m-%d')
        start_date_str = (current_dt - datetime.timedelta(days=max_period * 2)).strftime('%Y-%m-%d')

        # 一次性获取所有需要的数据
        # 使用get_price替代get_history
        prices = get_price(security,
                         start_date=start_date_str,
                         end_date=end_date_str,
                         frequency='daily',
                         fields=['high', 'low', 'close', 'volume'],
                         fq='pre')

        # 直接使用前复权后的价格数据
        real_closes = prices['close']
        real_highs = prices['high']
        real_lows = prices['low']

        # 使用talib计算ADX
        adx = talib.ADX(real_highs.values, real_lows.values, real_closes.values, timeperiod=g.adx_period)
        plus_di = talib.PLUS_DI(real_highs.values, real_lows.values, real_closes.values, timeperiod=g.adx_period)
        minus_di = talib.MINUS_DI(real_highs.values, real_lows.values, real_closes.values, timeperiod=g.adx_period)

        # 使用talib计算ATR
        atr = talib.ATR(real_highs.values, real_lows.values, real_closes.values, timeperiod=g.atr_period)
        new_atr_value = atr[-1]

        # 计算波动率（当前和历史）
        returns = real_closes.pct_change().dropna()
        current_volatility = returns[-20:].std()
        historical_volatility = returns.std()

        # 计算其他指标
        ma20 = real_closes[-20:].mean()
        ma60 = real_closes.mean()
        latest_price = real_closes[-1]
        bias = (latest_price - ma20) / ma20

        # 计算平均成交量
        avg_volume = prices['volume'][-20:].mean()

        # 计算量比
        current_volume = prices['volume'][-1]
        volume_5d_avg = prices['volume'][-5:].mean()
        volume_ratio = current_volume / volume_5d_avg if volume_5d_avg > 0 else 1.0

        # 计算趋势强度
        trend_strength = adx[-20:].mean()

        # 计算M1动量因子
        try:
            if len(real_closes) >= g.momentum_M1_lookback:
                momentum_M1_value = (real_closes.iloc[-1] / real_closes.iloc[-g.momentum_M1_lookback]) - 1
            else:
                momentum_M1_value = 0.0  # 数据不足时返回0
        except Exception as e:
            log.error(f"计算{security}的M1动量因子时出错: {str(e)}")
            momentum_M1_value = 0.0  # 出错时返回0

        # 存储计算结果
        if 'technical_indicators' not in g.cache:
            g.cache['technical_indicators'] = {}

        indicators_to_update = {
            'bias': bias,
            'adx': adx[-1] if not np.isnan(adx[-1]) else 25,
            'plus_di': plus_di[-1] if not np.isnan(plus_di[-1]) else 20,
            'minus_di': minus_di[-1] if not np.isnan(minus_di[-1]) else 20,
            'atr': new_atr_value if not np.isnan(new_atr_value) else 0.0,
            'ma20': ma20,
            'ma60': ma60,
            'volatility': current_volatility,
            'historical_volatility': historical_volatility,
            'avg_volume': avg_volume,
            'trend_strength': trend_strength,
            'latest_price': latest_price,
            'real_closes': real_closes,
            'volume_ratio': volume_ratio,
            'momentum_M1': momentum_M1_value
        }

        if security not in g.cache['technical_indicators']:
            g.cache['technical_indicators'][security] = {}
        g.cache['technical_indicators'][security].update(indicators_to_update)
        g.cache['last_update_date'] = current_date

        return g.cache['technical_indicators'][security]

    except Exception as e:
        log.error(f"更新 {security} 技术指标时出错: {str(e)}")
        default_indicators = {
            'bias': 0, 'adx': 25, 'plus_di': 20, 'minus_di': 20, 'atr': 0.0,
            'ma20': 0, 'ma60': 0, 'volatility': 0.02, 'historical_volatility': 0.02,
            'avg_volume': 1e6, 'trend_strength': 20, 'latest_price': 0,
            'volume_ratio': 1.0, 'real_closes': pd.Series([])
        }
        return g.cache.get('technical_indicators', {}).get(security, default_indicators)

# 修改原有的技术指标计算函数
def calculate_bias(context, security, n=24):
    """使用缓存的技术指标"""
    try:
        return g.cache['technical_indicators'][security]['bias']
    except Exception as e:
        log.error(f"计算BIAS时出错: {str(e)}")
        return 0.0

def calculate_adx(context, security, n=14):
    """使用缓存的技术指标"""
    try:
        indicators = g.cache['technical_indicators'][security]
        return indicators['adx'], indicators['plus_di'], indicators['minus_di']
    except Exception as e:
        log.error(f"计算ADX时出错: {str(e)}")
        return 0.0, 0.0, 0.0

def calculate_atr(context, security, n=14):
    """使用缓存的技术指标"""
    try:
        return g.cache['technical_indicators'][security]['atr']
    except Exception as e:
        log.error(f"计算ATR时出错: {str(e)}")
        return 0.0

# 修改market_open函数
def execute_initial_buy(context):
    """
    执行初始建仓，加入市场状态判断和动态调整

    与JQ版本保持一致，实现更复杂的初始建仓逻辑，包括：
    1. 根据市场状态动态调整备用资金比例
    2. 根据技术指标动态调整各个标的的配置
    3. 考虑标的是否停牌等交易限制
    """
    try:
        log.info("开始执行初始建仓")

        # 计算风险平价权重
        weights = calculate_risk_parity_weights(context)

        # 记录原始备用资金比例
        original_reserve_pct = g.reserve_position_pct
        current_reserve_pct = original_reserve_pct

        # 分析整体市场状况
        up_count = 0
        down_count = 0
        for security in g.stock_list:
            market_state = get_market_state(context, security)
            if market_state == 'uptrend':
                up_count += 1
            elif market_state == 'downtrend':
                down_count += 1

        # 根据整体市场状况调整备用资金
        if up_count == len(g.stock_list):
            # 全面上涨行情，降低备用资金
            current_reserve_pct = max(0.1, original_reserve_pct * g.reserve_scale_down_factor)
            log.info(f'初始建仓：全面上涨行情，备用资金调整为{current_reserve_pct:.1%}')
        elif down_count == len(g.stock_list):
            # 全面下跌行情，提高备用资金
            current_reserve_pct = min(0.5, original_reserve_pct * g.reserve_scale_up_factor)
            log.info(f'初始建仓：全面下跌行情，备用资金调整为{current_reserve_pct:.1%}')

        # 动态调整各个标的的配置
        total_adjustment = 0  # 记录总调整幅度
        adjustments = {}  # 记录每个标的的调整幅度

        # 计算理论调整幅度
        for security in g.stock_list:
            try:
                market_state = g.market_states[security]
                adjustment_factor, confidence = calculate_dynamic_allocation_factors(context, security)

                if market_state == 'uptrend':
                    # 上涨趋势的动态超配
                    base_extra = min(g.uptrend_base_extra, current_reserve_pct)
                    extra_weight = base_extra * adjustment_factor * confidence

                    # 添加趋势加速度判断
                    if g.cache['technical_indicators'][security]['trend_strength'] > 30:
                        extra_weight *= g.strong_trend_boost

                    adjustments[security] = extra_weight
                    total_adjustment += extra_weight

                elif market_state == 'downtrend':
                    # 下降趋势的动态减配
                    base_reduction = min(g.downtrend_base_reduction, weights[security] * g.downtrend_pct_limit)
                    reduction = base_reduction * adjustment_factor * confidence

                    # 添加反转信号判断
                    if g.cache['technical_indicators'][security]['bias'] < -g.bias_threshold[security]:
                        reduction *= g.oversold_reduction_factor

                    adjustments[security] = -reduction
                    total_adjustment -= reduction

                log.info(f"初始建仓 {security} 调整系数: {adjustment_factor:.2f}, 置信度: {confidence:.2f}")

            except Exception as e:
                log.error(f"计算{security}配置调整时出错: {str(e)}")
                adjustments[security] = 0

        # 实际调整权重
        available_reserve = current_reserve_pct - 0.05  # 保留5%最小备用资金
        if total_adjustment > 0:
            # 等比例缩放调整幅度，确保不超过可用备用资金
            scale_factor = min(1.0, available_reserve / total_adjustment)
            for security in g.stock_list:
                if security in adjustments:
                    actual_adjustment = adjustments[security] * scale_factor
                    weights[security] += actual_adjustment
                    current_reserve_pct -= actual_adjustment

                    if abs(actual_adjustment) >= 0.001:  # 仅记录显著调整
                        log.info(f"初始建仓 {security} {'超配' if actual_adjustment > 0 else '减配'} {abs(actual_adjustment):.1%}")

        # 确保权重非负且总和不超过1
        weights = {s: max(0.0, w) for s, w in weights.items()}
        weight_sum = sum(float(w) for w in weights.values())

        if weight_sum > 1:
            scale = (1 - current_reserve_pct) / weight_sum
            weights = {s: w * scale for s, w in weights.items()}
            log.info(f"初始建仓：权重总和超过1，进行等比例缩减，缩减系数: {scale:.3f}")

        # 更新备用资金比例
        g.reserve_position_pct = current_reserve_pct
        log.info(f"初始建仓：调整后备用资金比例: {current_reserve_pct:.1%} (原始: {original_reserve_pct:.1%})")

        # 执行建仓
        total_value = context.portfolio.total_value
        # 根据Ptrade API文档，使用cash替代available_cash
        available_cash = context.portfolio.cash

        for security in g.stock_list:
            try:
                # 计算目标金额
                target_value = total_value * (1 - current_reserve_pct) * weights.get(security, 0)

                # 获取当前价格和交易状态
                snapshot = get_snapshot(security)
                if not snapshot or security not in snapshot or snapshot[security]['trade_status'] == 'HALT':
                    log.warning(f"{security} 当前暂停交易，跳过建仓")
                    continue

                current_price = snapshot[security]['last_px']
                if current_price <= 0:
                    log.warning(f"{security} 当前价格异常: {current_price}，跳过建仓")
                    continue

                # 计算购买数量（确保是100的整数倍）
                amount = int(target_value / current_price / 100) * 100

                if amount >= 100:  # 确保购买数量至少为100股
                    order_value = amount * current_price
                    if order_value <= available_cash:  # 确保资金充足
                        order(security, amount)
                        log.info(f"初始建仓 {security}: 数量={amount}, 价格={current_price:.3f}, 金额={order_value:.2f}, 权重={weights[security]:.1%}")
                        available_cash -= order_value  # 更新可用资金
                    else:
                        log.warning(f"初始建仓 {security}: 资金不足，需要{order_value:.2f}，可用{available_cash:.2f}")
                else:
                    log.warning(f"初始建仓 {security}: 计算购买数量小于100股，跳过购买")
            except Exception as e:
                log.error(f"处理标的 {security} 时发生错误: {str(e)}")
                continue

        log.info("初始建仓完成")
        g.initial_position_established = True
    except Exception as e:
        log.error(f"执行初始建仓时发生错误: {str(e)}")
        raise  # 重新抛出异常，确保错误不被静默处理

def rebalance_portfolio(context):
    """
    执行投资组合再平衡

    与JQ版本保持一致，实现更复杂的再平衡逻辑，包括：
    1. 根据市场状态动态调整备用资金比例
    2. 根据技术指标动态调整各个标的的配置
    3. 闲置资金通过国债逆回购管理
    """
    try:
        log.info("开始执行投资组合再平衡")

        # 计算风险平价权重
        weights = calculate_risk_parity_weights(context)

        # 记录原始备用资金比例
        original_reserve_pct = g.reserve_position_pct
        current_reserve_pct = original_reserve_pct

        # 优化：动态调整备用资金比例
        up_count = sum(1 for s in g.stock_list if g.market_states[s] == 'uptrend')
        down_count = sum(1 for s in g.stock_list if g.market_states[s] == 'downtrend')

        # 根据整体市场状况调整备用资金
        if up_count == len(g.stock_list):
            current_reserve_pct = max(0.1, original_reserve_pct * g.reserve_scale_down_factor)
            log.info(f'全面上涨行情，备用资金调整为{current_reserve_pct:.1%}')
        elif down_count == len(g.stock_list):
            current_reserve_pct = min(0.5, original_reserve_pct * g.reserve_scale_up_factor)
            log.info(f'全面下跌行情，备用资金调整为{current_reserve_pct:.1%}')

        # 动态调整各个标的的配置
        total_adjustment = 0  # 记录总调整幅度
        adjustments = {}  # 记录每个标的的调整幅度

        # 第一轮：计算理论调整幅度
        for security in g.stock_list:
            try:
                market_state = g.market_states[security]
                adjustment_factor, confidence = calculate_dynamic_allocation_factors(context, security)

                if market_state == 'uptrend':
                    # 上涨趋势的动态超配
                    base_extra = min(g.uptrend_base_extra, current_reserve_pct)
                    extra_weight = base_extra * adjustment_factor * confidence

                    # 添加趋势加速度判断
                    if g.cache['technical_indicators'][security]['trend_strength'] > 30:
                        extra_weight *= g.strong_trend_boost

                    adjustments[security] = extra_weight
                    total_adjustment += extra_weight

                elif market_state == 'downtrend':
                    # 下降趋势的动态减配
                    base_reduction = min(g.downtrend_base_reduction, weights[security] * g.downtrend_pct_limit)
                    reduction = base_reduction * adjustment_factor * confidence

                    # 添加反转信号判断
                    if g.cache['technical_indicators'][security]['bias'] < -g.bias_threshold[security]:
                        reduction *= g.oversold_reduction_factor

                    adjustments[security] = -reduction
                    total_adjustment -= reduction

                log.info(f"{security} 调整系数: {adjustment_factor:.2f}, 置信度: {confidence:.2f}")

            except Exception as e:
                log.error(f"计算{security}配置调整时出错: {str(e)}")
                adjustments[security] = 0

        # 第二轮：实际调整权重
        available_reserve = current_reserve_pct - 0.05  # 保留5%最小备用资金
        if total_adjustment > 0:
            # 等比例缩放调整幅度，确保不超过可用备用资金
            scale_factor = min(1.0, available_reserve / total_adjustment)
            for security in g.stock_list:
                if security in adjustments:
                    actual_adjustment = adjustments[security] * scale_factor
                    weights[security] += actual_adjustment
                    current_reserve_pct -= actual_adjustment

                    if abs(actual_adjustment) >= 0.001:  # 仅记录显著调整
                        log.info(f"{security} {'超配' if actual_adjustment > 0 else '减配'} {abs(actual_adjustment):.1%}")

        # 确保权重非负且总和不超过1
        weights = {s: max(0.0, w) for s, w in weights.items()}
        weight_sum = sum(float(w) for w in weights.values())

        if weight_sum > 1:
            scale = (1 - current_reserve_pct) / weight_sum
            weights = {s: w * scale for s, w in weights.items()}
            log.info(f"权重总和超过1，进行等比例缩减，缩减系数: {scale:.3f}")

            # 再次验证调整后的权重总和
            final_weight_sum = sum(float(w) for w in weights.values())
            log.info(f"调整后权重总和: {final_weight_sum:.3f}")

        # 更新备用资金比例
        g.reserve_position_pct = current_reserve_pct
        log.info(f"调整后备用资金比例: {current_reserve_pct:.1%} (原始: {original_reserve_pct:.1%})")

        # 不再处理货币基金持仓，改为国债逆回购

        # 使用优化的先卖后买交易执行
        total_position_value = context.portfolio.total_value * (1 - current_reserve_pct)
        target_values = {security: total_position_value * weights[security] for security in g.stock_list}
        execute_sell_then_buy_orders_ptrade(context, target_values)

        # 更新最后再平衡时间
        g.last_rebalance_date = context.blotter.current_dt.date()
        log.info("投资组合再平衡完成")

    except Exception as e:
        log.error(f"执行再平衡时发生错误: {str(e)}")
        raise  # 重新抛出异常，确保错误不被静默处理

def market_open(context):
    """开盘时运行"""
    try:
        # 首次运行时加载历史数据
        if not g.data_loaded:
            log.info("首次运行，开始加载历史数据...")
            preload_historical_data(context)
            g.data_loaded = True
            log.info("历史数据加载完成")
            # 执行持久化保存
            save_strategy_state()
            return  # 首日仅加载数据，不执行交易

        # 更新技术指标
        for security in g.stock_list:
            update_technical_indicators(context, security)

        # 检查是否需要建立初始仓位
        if g.initial_buy_needed and not g.initial_position_established:
            log.info("准备进行初始买入")
            execute_initial_buy(context)
            g.initial_buy_needed = False

        # 取消所有未完成订单
        cancel_all_orders(context)

        # 检查是否需要月度再平衡
        if should_rebalance(context):
            log.info("执行月度再平衡")
            rebalance_portfolio(context)  # 使用更复杂的再平衡函数

        # 更新市场状态
        for security in g.stock_list:
            old_state = g.market_states[security]
            new_state = get_market_state(context, security)
            if old_state != new_state:
                # 只在状态发生显著变化时记录
                if (old_state == 'oscillating' or new_state == 'oscillating') and abs(get_trend_strength(context, security)) > 20:
                    log.info(f"{security} 市场状态从 {old_state} 变为 {new_state}")
                g.market_states[security] = new_state

    except Exception as e:
        log.error(f"market_open出错: {str(e)}")

# 计算证券收益率
def get_security_returns(context, security):
    """
    计算证券的日收益率，使用真实价格

    与JQ版本的区别：
    1. JQ版本使用get_current_data()[security].last_price获取当前价格
    2. Ptrade版本使用get_snapshot(security)[security]['last_px']获取当前价格
    3. JQ版本使用attribute_history获取历史数据
    4. Ptrade版本使用get_history获取历史数据
    5. Ptrade的get_history在设置fq='pre'时返回的价格已经是前复权后的价格，不需要再使用factor进行复权处理

    注意：此函数通过run_daily调用，使用get_snapshot获取即时价格。
    如果Ptrade在盘中执行此函数时无法提供有效的当日行情，可能需要将此逻辑移至handle_data函数，
    并使用data[security].close等属性获取K线数据。

    参数:
        context: Context对象
        security: 证券代码
    返回:
        float: 当日收益率
    """
    try:
        # 获取当前价格
        snapshot = get_snapshot(security)
        if not snapshot or security not in snapshot:
            log.error(f"获取{security}快照数据失败")
            return 0.0

        current_price = snapshot[security]['last_px']
        if current_price <= 0:
            log.error(f"{security}当前价格异常: {current_price}")
            return 0.0

        # 获取当前日期
        current_dt = context.blotter.current_dt

        # 获取昨日收盘价 - 使用前复权价格
        # 计算昨日日期（向前推5天以确保获取到交易日数据）
        # 将日期转换为字符串格式，符合Ptrade API要求
        end_date_str = current_dt.strftime('%Y-%m-%d')
        start_date_str = (current_dt - datetime.timedelta(days=5)).strftime('%Y-%m-%d')

        # 使用get_price替代get_history
        hist = get_price(security,
                       start_date=start_date_str,
                       end_date=end_date_str,
                       frequency='daily',
                       fields=['close'],
                       fq='pre')

        if hist.empty or 'close' not in hist or len(hist) < 2:
            log.error(f"获取{security}历史数据失败")
            return 0.0

        # 直接使用前复权价格 - 获取倒数第二个价格（昨日价格）
        yesterday_price = hist['close'].iloc[-2]
        if yesterday_price <= 0:
            log.error(f"{security}昨日价格异常: {yesterday_price}")
            return 0.0

        # 计算收益率
        returns = (current_price - yesterday_price) / yesterday_price
        return returns

    except Exception as e:
        log.error(f"计算{security}收益率时出错: {str(e)}")
        return 0.0

# 检查止损
def check_stop_loss(context):
    """检查止损条件"""
    try:
        for security in g.stock_list:
            # 获取当日收益率
            returns = get_security_returns(context, security)
            # 计算动态止损线
            stop_loss_line = calculate_dynamic_stop_loss(context, security)

            # 如果收益率低于止损线，触发止损
            if returns <= -stop_loss_line:
                log.info(f"触发止损 - {security}: 收益率={returns:.2%}, 止损线={stop_loss_line:.2%}")
                # 获取当前持仓
                position = context.portfolio.positions.get(security)
                # 注意：Ptrade中使用position.amount替代JQ中的position.total_amount
                if position and position.amount > 0:
                    # 计算卖出数量（半仓止损）
                    sell_amount = int(position.amount * 0.5)
                    if sell_amount >= 100:
                        # 分批止损
                        order(security, -sell_amount)
                        log.info(f"分批止损 - {security}: 卖出{sell_amount}股")
                    else:
                        # 完全止损
                        order_target(security, 0)
                        log.info(f"完全止损 - {security}: 清仓")
                # 设置停止交易标志
                g.stop_trading = True
                # 执行持久化保存
                save_strategy_state()
                return

        # 检查组合止损
        # 在Ptrade中，需要计算组合收益率
        # 计算当前总资产与初始资产的比例
        # 注意：Ptrade中使用context.capital_base替代JQ中的context.portfolio.starting_cash
        current_value = context.portfolio.total_value
        initial_value = context.capital_base
        portfolio_returns = (current_value / initial_value) - 1

        # 如果组合收益率低于止损线，触发组合止损
        if portfolio_returns <= -g.portfolio_stop_loss:
            log.info(f"触发组合止损: 收益率={portfolio_returns:.2%}")
            g.stop_trading = True
            # 执行持久化保存
            save_strategy_state()

    except Exception as e:
        log.error(f"检查止损时出错: {str(e)}")

def calculate_dynamic_allocation_factors(context, security):
    """
    计算动态配置调整因子
    返回: (float, float) - (调整系数, 置信度)

    与JQ版本保持一致，根据各种技术指标计算动态配置调整因子
    """
    try:
        indicators = g.cache['technical_indicators'].get(security)
        if not indicators:
            update_technical_indicators(context, security)
            indicators = g.cache['technical_indicators'].get(security)

        # 获取关键指标，使用get方法安全获取
        trend_strength = indicators.get('trend_strength', 20)  # 默认中等趋势
        volatility = indicators.get('volatility', 0.02)  # 默认波动率
        volume_ratio = indicators.get('volume_ratio', 1.0)  # 默认量比
        bias = indicators.get('bias', 0)  # 默认BIAS
        adx = indicators.get('adx', 25)  # 默认ADX
        plus_di = indicators.get('plus_di', 20)  # 默认+DI
        minus_di = indicators.get('minus_di', 20)  # 默认-DI

        # 计算趋势强度因子 (0.5 ~ 2.0)
        trend_factor = min(2.0, max(0.5, trend_strength / 20))

        # 计算趋势方向因子 (0.8 ~ 1.5)
        direction_factor = 1.0
        if plus_di > minus_di:
            direction_factor = min(1.5, max(1.0, 1 + (plus_di - minus_di) / 100))
        else:
            direction_factor = min(1.0, max(0.8, 1 - (minus_di - plus_di) / 100))

        # 计算波动率因子 (0.6 ~ 1.5)
        vol_base = 0.02  # 基准波动率
        if volatility < vol_base:
            volatility_factor = min(1.5, vol_base / volatility)
        else:
            volatility_factor = max(0.6, vol_base / volatility)

        # 计算成交量因子 (0.7 ~ 1.8)
        if volume_ratio > 1:
            volume_factor = min(1.8, 1 + (volume_ratio - 1) * 0.5)
        else:
            volume_factor = max(0.7, 1 - (1 - volume_ratio) * 0.5)

        # 计算BIAS影响因子 (0.6 ~ 1.6)
        bias_threshold = g.bias_threshold[security]
        if abs(bias) < bias_threshold * 0.5:  # BIAS在合理范围内，更积极调整
            bias_factor = 1.6 - abs(bias) / bias_threshold
        else:  # BIAS偏离较大，谨慎调整
            bias_factor = max(0.6, 1 - abs(bias) / bias_threshold)

        # 计算市场趋势持续性
        trend_persistence = min(1.5, adx / g.alloc_persistence_adx_base)

        # 综合计算调整系数 - 使用优化后的权重
        adjustment_factor = (
            trend_factor * g.weight_trend +
            direction_factor * g.weight_direction +
            volatility_factor * g.weight_volatility +
            volume_factor * g.weight_volume +
            bias_factor * g.weight_bias
        ) * trend_persistence  # 使用趋势持续性作为整体调节因子

        # 计算置信度 (0 ~ 1)
        confidence = min(1.0, (
            (trend_strength / g.conf_trend_base) * g.conf_trend_weight +
            (1 - abs(bias) / (bias_threshold * g.conf_bias_mult)) * g.conf_bias_weight +
            (adx / g.conf_adx_base) * g.conf_adx_weight +
            (volume_ratio - g.conf_vol_offset) * g.conf_vol_weight
        ))

        # 根据市场状态调整最终系数
        market_state = g.market_states[security]
        if market_state == 'uptrend':
            # 上涨趋势时，如果指标都支持，可以更激进
            if confidence > 0.7 and trend_factor > 1.2:
                adjustment_factor *= g.strong_trend_boost
        elif market_state == 'downtrend':
            # 下跌趋势时，如果指标显示反转可能，保持灵活
            if confidence > 0.6 and bias_factor > 1.2:
                adjustment_factor *= g.oversold_reduction_factor

        # 确保最终调整系数在合理范围内
        adjustment_factor = min(2.5, max(0.5, adjustment_factor))

        # 记录详细的调整因子信息
        if adjustment_factor > 1.5 or adjustment_factor < 0.7:
            log.info(f"{security} 调整因子详情:")
            log.info(f"趋势因子: {trend_factor:.2f}, 方向因子: {direction_factor:.2f}")
            log.info(f"波动率因子: {volatility_factor:.2f}, 成交量因子: {volume_factor:.2f}")
            log.info(f"BIAS因子: {bias_factor:.2f}, 趋势持续性: {trend_persistence:.2f}")

        return adjustment_factor, confidence

    except Exception as e:
        log.error(f"计算动态配置因子时出错: {str(e)}")
        return 1.0, 0.5  # 出错时返回默认值

def calculate_dynamic_stop_loss(context, security):
    """计算动态止损线"""
    try:
        # 获取市场状态和波动率
        market_state = g.market_states[security]
        volatility = calculate_volatility(context, security)

        # 基础止损线
        base_stop_loss = g.single_stock_stop_loss

        # 根据市场状态调整
        if market_state == 'uptrend':
            stop_loss_factor = 1.2  # 上涨趋势放宽止损
        elif market_state == 'downtrend':
            stop_loss_factor = 0.8  # 下跌趋势收紧止损
        else:
            stop_loss_factor = 1.0  # 震荡市保持基础止损

        # 根据波动率调整
        volatility_factor = 1.0 + (volatility - 0.02) * 2  # 波动率每增加1%，止损放宽2%
        volatility_factor = max(0.8, min(1.5, volatility_factor))  # 限制调整范围

        # 计算最终止损线
        final_stop_loss = base_stop_loss * stop_loss_factor * volatility_factor

        return max(0.03, min(0.08, final_stop_loss))  # 限制止损线在3%-8%之间

    except Exception as e:
        log.error(f"计算动态止损线时出错: {str(e)}")
        return g.single_stock_stop_loss

def cancel_all_orders(context):
    """
    取消所有未完成的订单

    与JQ版本的区别：
    1. JQ版本中get_open_orders()返回字典，键为order_id，值为order对象
    2. Ptrade版本中get_open_orders()直接返回order对象列表
    3. JQ版本中直接传入order对象到cancel_order函数
    4. Ptrade版本中需要传入order.id到cancel_order函数

    注意：此函数假设Ptrade的Order对象具有以下属性：
    - id: 订单ID
    - symbol: 证券代码
    - limit: 委托价格
    - amount: 委托数量
    根据Ptrade API文档（第275页），这些属性应该是存在的。

    同时，假设cancel_order()函数接受订单ID作为参数，而不是Order对象本身。
    文档示例显示cancel_order(_id)，暗示接受订单ID。
    """
    try:
        open_orders = get_open_orders() # Ptrade 返回 Order 对象列表
        if open_orders:
            log.info(f"准备取消 {len(open_orders)} 个未完成订单")
            for o in open_orders:
                cancel_order(o.id) # 使用订单ID进行取消
                log.debug(f"取消未完成订单: {o.symbol}, 委托价格: {o.limit}, 委托数量: {o.amount}")
    except Exception as e:
        log.error(f"取消订单时出错: {str(e)}")

def get_market_state(context, security):
    """
    根据技术指标判断市场状态
    返回: 'uptrend', 'downtrend', 或 'oscillating'
    """
    try:
        # 更新技术指标
        indicators = update_technical_indicators(context, security)

        # 获取BIAS和ADX值
        bias = indicators.get('bias', 0)  # 使用get方法安全获取bias值
        adx = indicators.get('adx', 0)    # 使用get方法安全获取adx值
        plus_di = indicators.get('plus_di', 0)  # 使用get方法安全获取plus_di值
        minus_di = indicators.get('minus_di', 0)  # 使用get方法安全获取minus_di值

        # 获取该证券的BIAS阈值
        bias_threshold = g.bias_threshold[security]

        # 趋势判断标准
        TREND_THRESHOLD = 35  # ADX高于35即为趋势
        # 震荡市判定标准放宽
        if adx < TREND_THRESHOLD or abs(bias) < bias_threshold * g.bias_oscillation_scale_factor:
            return 'oscillating'

        # 判断市场状态
        if adx > TREND_THRESHOLD:  # 存在明显趋势
            if plus_di > minus_di:  # 上升趋势
                if bias > bias_threshold:  # BIAS过高，可能超买
                    return 'oscillating'
                return 'uptrend'
            else:  # 下降趋势
                if bias < -bias_threshold:  # BIAS过低，可能超卖
                    return 'oscillating'
                return 'downtrend'
        else:  # ADX较低，无明显趋势
            if abs(bias) > bias_threshold:  # BIAS超出阈值
                if bias > bias_threshold:  # 可能超买
                    return 'downtrend'
                else:  # 可能超卖
                    return 'uptrend'
            return 'oscillating'  # 震荡市场

    except Exception as e:
        log.error(f"判断市场状态时出错: {str(e)}")
        return 'oscillating'  # 发生错误时默认返回震荡状态

def handle_data(context, data):
    """
    每个交易日的主要处理函数

    与JQ版本保持一致，仅在触发止损时停止交易

    参数:
        context: Context对象
        data: 当前行情数据，在Ptrade中可能不使用
    """
    try:
        # 如果已经触发止损，则不再交易
        if g.stop_trading:
            return

        # 获取当前日期
        current_date = context.blotter.current_dt.date()

        # 更新技术指标
        for security in g.stock_list:
            update_technical_indicators(context, security)

        # 检查是否需要再平衡
        if should_rebalance(context):
            log.info(f"执行再平衡 - {current_date}")

            # 取消所有未完成的订单
            cancel_all_orders(context)

            # 计算目标权重
            target_weights = calculate_target_weights(context)

            # 执行交易
            execute_trades(context, target_weights)

            # 更新最后再平衡日期
            g.last_rebalance_date = current_date

            # 记录当前持仓
            log_portfolio(context)

            # 执行持久化保存
            save_strategy_state()
    except Exception as e:
        log.error(f"handle_data出错: {str(e)}")

def reverse_repurchase(context):
    """
    盘后国债逆回购

    根据Ptrade API官方案例，盘后进行逆回购，只能使用run_daily设定在3点之后执行
    参考官方案例：保留1000元用于新债中签等，其余资金进行逆回购
    """
    try:
        # 获取当前可用现金
        cash = context.portfolio.cash

        # 保留资金，用于新债中签等
        cash = cash - g.reverse_repo_reserve

        # 检查是否有足够资金进行逆回购
        if cash < g.min_reverse_repo_amount:
            log.info(f"可用资金不足，跳过逆回购: 可用资金={cash:.2f}, 最小金额={g.min_reverse_repo_amount}")
            return

        # 计算逆回购数量（以1000元为单位，对应10手）
        amount = int(cash / 1000) * 10

        if amount <= 0:
            log.info(f"计算逆回购数量为0，跳过逆回购: 可用资金={cash:.2f}")
            return

        # 选择逆回购品种（优先选择上海1天期）
        reverse_repo_code = g.reverse_repo_codes['SH_1D']  # 默认使用上海1天期

        # 检查上海逆回购是否可用，如果不可用则使用深圳
        try:
            snapshot = get_snapshot(reverse_repo_code)
            if not snapshot or reverse_repo_code not in snapshot or snapshot[reverse_repo_code]['trade_status'] == 'HALT':
                # 上海逆回购不可用，尝试深圳
                reverse_repo_code = g.reverse_repo_codes['SZ_1D']
                log.info("上海逆回购不可用，切换到深圳逆回购")
        except:
            # 获取快照失败，使用深圳逆回购
            reverse_repo_code = g.reverse_repo_codes['SZ_1D']
            log.info("获取上海逆回购快照失败，切换到深圳逆回购")

        # 执行逆回购（卖出操作）
        # 根据官方案例，逆回购是卖出操作，所以使用负数
        order(reverse_repo_code, -amount)

        log.info(f"执行国债逆回购: 代码={reverse_repo_code}, 数量={amount}手, 金额={amount*100:.2f}元")

    except Exception as e:
        log.error(f"执行国债逆回购时发生错误: {str(e)}")

# 删除货币基金收益检查函数，因为已改为国债逆回购
# 国债逆回购是T+1到期，无需每日检查收益

def monitor_strategy_performance(context):
    """
    监控策略性能

    注意：此函数通过run_daily调用，使用get_snapshot获取即时价格。
    如果Ptrade在盘中执行此函数时无法提供有效的当日行情，可能需要将此逻辑移至handle_data函数，
    并使用data[security].close等属性获取K线数据。

    同时，此函数会在每日收盘后执行持久化保存，确保策略状态能够在重启后恢复。
    """
    try:
        # 计算当日收益
        # 在Ptrade中，需要计算当前总资产与初始资产的比例
        # 注意：Ptrade中使用context.capital_base替代JQ中的context.portfolio.starting_cash
        current_value = context.portfolio.total_value
        initial_value = context.capital_base
        daily_return = (current_value / initial_value) - 1

        # 计算波动率
        portfolio_volatility = calculate_portfolio_volatility(context)

        # 计算夏普比率
        sharpe_ratio = daily_return / portfolio_volatility if portfolio_volatility > 0 else 0

        # 检查风险指标
        if daily_return < -0.02:  # 单日亏损超过2%
            log.warning(f"策略单日亏损较大: {daily_return:.2%}")

        if portfolio_volatility > 0.03:  # 波动率超过3%
            log.warning(f"策略波动率较高: {portfolio_volatility:.2%}")

        if sharpe_ratio < 0.5:  # 夏普比率低于0.5
            log.warning(f"策略夏普比率较低: {sharpe_ratio:.2f}")

        # 记录性能指标
        log.info(f"策略性能指标 - 日收益: {daily_return:.2%}, 波动率: {portfolio_volatility:.2%}, 夏普比率: {sharpe_ratio:.2f}")

        # 记录当前持仓情况
        positions_info = []
        for security, position in context.portfolio.positions.items():
            # 注意：Ptrade中使用position.amount替代JQ中的position.total_amount
            if position.amount > 0:
                # 获取当前价格
                snapshot = get_snapshot(security)
                if snapshot and security in snapshot:
                    current_price = snapshot[security]['last_px']
                    # 计算持仓收益率
                    # 注意：Ptrade中使用position.cost_basis替代JQ中的position.avg_cost
                    position_return = (current_price / position.cost_basis) - 1
                    positions_info.append(f"{security}: {position_return:.2%}")

        if positions_info:
            log.info(f"当前持仓收益: {', '.join(positions_info)}")

        # 执行持久化保存
        save_strategy_state()

    except Exception as e:
        log.error(f"监控策略性能时出错: {str(e)}")

def calculate_portfolio_volatility(context):
    """
    计算投资组合的波动率

    注意：此函数通过run_daily调用，使用get_snapshot获取即时价格。
    如果Ptrade在盘中执行此函数时无法提供有效的当日行情，可能需要将此逻辑移至handle_data函数，
    并使用data[security].close等属性获取K线数据。
    """
    try:
        # 获取每个标的的波动率
        volatilities = []
        weights = []
        total_position_value = 0

        # 计算每个标的的持仓市值
        for security in g.stock_list:
            position = context.portfolio.positions.get(security)
            # 注意：Ptrade中使用position.amount替代JQ中的position.total_amount
            if position and position.amount > 0:
                # 获取当前价格
                snapshot = get_snapshot(security)
                if snapshot and security in snapshot:
                    current_price = snapshot[security]['last_px']
                    position_value = position.amount * current_price
                    total_position_value += position_value

                    # 获取波动率
                    indicators = g.cache['technical_indicators'].get(security)
                    if indicators and 'volatility' in indicators:
                        volatilities.append(indicators['volatility'])
                        weights.append(position_value)

        # 如果有持仓，计算加权平均波动率
        if volatilities and weights and sum(weights) > 0:
            # 归一化权重
            normalized_weights = [w / sum(weights) for w in weights]
            # 计算加权平均波动率
            weighted_volatility = sum(v * w for v, w in zip(volatilities, normalized_weights))
            return weighted_volatility

        # 如果没有持仓或计算失败，使用简单平均波动率
        if volatilities:
            return sum(volatilities) / len(volatilities)

        # 如果没有有效的波动率数据，返回默认值
        return 0.02  # 默认波动率

    except Exception as e:
        log.error(f"计算投资组合波动率时出错: {str(e)}")
        return 0.02  # 默认波动率

def apply_momentum_overlay(context):
    """
    应用动量叠加策略

    注意：此函数通过run_daily调用，使用get_snapshot获取即时价格。
    如果Ptrade在盘中执行此函数时无法提供有效的当日行情，可能需要将此逻辑移至handle_data函数，
    并使用data[security].close等属性获取K线数据。
    """
    try:
        # 检查是否已经执行过本周的动量调整
        current_date = context.blotter.current_dt.date()
        if g.cache['last_momentum_check'] == current_date:
            return

        # 更新技术指标
        for security in g.stock_list:
            update_technical_indicators(context, security)

        # 获取基础风险平价权重
        base_weights = calculate_risk_parity_weights(context)

        # 计算实际可用的备用金
        total_value = context.portfolio.total_value
        # 根据Ptrade API文档，使用cash替代available_cash
        available_cash = context.portfolio.cash

        # 计算实际可用的备用金总额（不再考虑货币基金，改为国债逆回购）
        actual_reserve_value = available_cash
        actual_reserve_pct = actual_reserve_value / total_value if total_value > 0 else 0

        # 保存当前备用金比例，供后续使用
        current_reserve_pct = actual_reserve_pct

        # 设置最大可用备用金（使用实际值的一半）
        max_total_boost = min(actual_reserve_pct / 2, g.max_momentum_allocation_pct)

        # 识别动量资产 - 使用M1动量因子
        momentum_assets = []
        for security in g.stock_list:
            try:
                indicators = g.cache['technical_indicators'].get(security)
                if not indicators:
                    log.warning(f"{security} 没有技术指标数据，跳过动量判断")
                    continue

                # 检查必要的指标是否存在
                if 'latest_price' not in indicators or 'momentum_M1' not in indicators:
                    log.warning(f"{security} 缺少必要的技术指标数据，跳过动量判断")
                    continue

                # 使用M1动量因子判断动量信号
                momentum_strength = indicators.get('momentum_M1', 0.0)
                if momentum_strength > g.momentum_threshold:
                    momentum_assets.append((security, momentum_strength))
                    log.info(f"{security} 满足M1动量条件: 价格={indicators['latest_price']:.2f}, M1动量={momentum_strength:.2%}")
            except Exception as e:
                log.error(f"处理{security}的动量判断时出错: {str(e)}")
                continue

        # 按动量强度排序
        momentum_assets.sort(key=lambda x: x[1], reverse=True)

        # 初始化最终权重
        final_weights = base_weights.copy()
        total_boost_weight = 0.0
        boost_allocations = {}  # 记录每个资产的增强分配

        # 计算动量调整
        for security, momentum_strength in momentum_assets:
            # 计算该资产的权重增量
            base_weight = float(base_weights[security])
            boost = base_weight * (g.momentum_boost_factor - 1)

            # 根据动量强度调整增强系数
            strength_factor = min(1.5, 1 + momentum_strength * 2)
            adjusted_boost = boost * strength_factor

            # 检查是否超过最大增强限制
            if total_boost_weight + adjusted_boost <= max_total_boost:
                final_weights[security] = base_weight + adjusted_boost
                total_boost_weight += adjusted_boost
                boost_allocations[security] = adjusted_boost
                log.info(f"动量增强 {security}: 基础权重 {base_weight:.1%} -> {final_weights[security]:.1%}, M1动量 {momentum_strength:.2%}")
            else:
                # 如果备用金不足，按比例缩减
                remaining_boost = max_total_boost - total_boost_weight
                if remaining_boost > 0:
                    scale = remaining_boost / adjusted_boost
                    final_weights[security] = base_weight + (adjusted_boost * scale)
                    total_boost_weight = max_total_boost
                    boost_allocations[security] = adjusted_boost * scale
                    log.info(f"动量增强 {security}: 基础权重 {base_weight:.1%} -> {final_weights[security]:.1%}, M1动量 {momentum_strength:.2%} (已缩减)")
                break

        # 计算目标总市值（不包括备用金）
        # 注意：这个变量实际上没有被使用，因为我们直接使用了total_position_value

        # 权重归一化
        current_total_weight = sum(float(w) for w in final_weights.values())
        if abs(current_total_weight - 1.0) > 0.001:
            scale = 1.0 / current_total_weight
            final_weights = {sec: float(w) * scale for sec, w in final_weights.items()}
            log.info(f"权重归一化: 缩放系数 {scale:.3f}")

            # 再次验证调整后的权重总和
            final_weight_sum = sum(float(w) for w in final_weights.values())
            log.info(f"调整后权重总和: {final_weight_sum:.3f}")

        # 记录当前备用资金比例，但不更新全局设置
        log.info(f"当前备用资金比例: {current_reserve_pct:.1%} (原始: {g.reserve_position_pct:.1%})")

        # 不再处理货币基金持仓，改为国债逆回购

        # 使用优化的先卖后买交易执行
        total_position_value = context.portfolio.total_value * (1 - current_reserve_pct)
        target_values = {security: total_position_value * final_weights[security] for security in g.stock_list}
        execute_sell_then_buy_orders_ptrade(context, target_values)

        # 更新动量检查时间
        g.cache['last_momentum_check'] = current_date

    except Exception as e:
        log.error(f"应用动量叠加策略时出错: {str(e)}")

def calculate_risk_parity_weights(context):
    """
    计算风险平价权重

    与JQ版本的区别：
    1. JQ版本使用get_price获取历史数据
    2. Ptrade版本使用get_history获取历史数据
    3. 其他逻辑保持一致
    """
    try:
        # 初始化波动率字典
        volatilities = {}

        # 计算每个ETF的60日波动率
        for security in g.stock_list:
            try:
                # 获取当前日期
                current_dt = context.blotter.current_dt

                # 获取过去60个交易日的收盘价
                # 计算开始日期（向前推120天以确保获取到足够的交易日数据）
                # 将日期转换为字符串格式，符合Ptrade API要求
                end_date_str = current_dt.strftime('%Y-%m-%d')
                start_date_str = (current_dt - datetime.timedelta(days=120)).strftime('%Y-%m-%d')

                # 使用get_price替代get_history
                price_data = get_price(security,
                                     start_date=start_date_str,
                                     end_date=end_date_str,
                                     frequency='daily',
                                     fields=['close'],
                                     fq='pre')

                # 检查数据是否有效
                if price_data.empty or 'close' not in price_data:
                    log.warning(f"获取{security}历史数据失败，使用默认波动率")
                    volatilities[security] = 0.02
                    continue

                close = price_data['close']

                # 计算日收益率
                returns = close.pct_change().dropna()

                # 计算波动率（标准差）
                volatility = returns.std()
                # 检查波动率是否有效
                if np.isnan(volatility) or volatility <= 0:
                    log.warning(f"{security}波动率计算结果无效: {volatility}，使用默认波动率")
                    volatility = 0.02

                volatilities[security] = volatility

            except Exception as e:
                log.error(f"计算 {security} 波动率时出错: {str(e)}")
                volatilities[security] = 0.02  # 使用默认波动率

        # 计算权重：1/波动率 / 所有(1/波动率)之和
        inv_vol_sum = sum(1.0 / vol for vol in volatilities.values())
        weights = {security: (1.0 / vol) / inv_vol_sum for security, vol in volatilities.items()}

        # 优化：将权重转换为整数百分比
        total_pct = 100
        int_weights = {}
        remaining = total_pct

        # 先将权重转换为整数百分比（向下取整）
        for security, weight in weights.items():
            int_weight = int(weight * 100)
            int_weights[security] = int_weight
            remaining -= int_weight

        # 将剩余的百分比按原权重比例分配
        if remaining > 0:
            # 按原始权重排序，权重大的优先获得剩余百分比
            sorted_securities = sorted(weights.keys(), key=lambda x: weights[x], reverse=True)
            for i in range(remaining):
                security = sorted_securities[i % len(sorted_securities)]
                int_weights[security] += 1

        # 转换回小数形式
        rounded_weights = {security: weight / 100.0 for security, weight in int_weights.items()}

        # 输出取整后的权重信息（改为debug级别）
        weight_str = ', '.join([f"{security}: {weight:.0%}" for security, weight in rounded_weights.items()])
        log.debug(f"风险平价权重计算结果: {weight_str}")

        return rounded_weights

    except Exception as e:
        log.error(f"计算风险平价权重时出错: {str(e)}")
        # 出错时返回等权重
        equal_weight = 1.0 / len(g.stock_list)
        return {security: equal_weight for security in g.stock_list}

# 添加after_trading_end函数，用于每日收盘后执行持久化保存
def after_trading_end(context, data):
    """
    每日收盘后处理函数，用于执行收盘后的操作
    """
    log.info("执行盘后处理...")

    # 记录当日持仓情况
    positions_info = []
    for security, position in context.portfolio.positions.items():
        if position.amount > 0:
            positions_info.append(f"{security}: {position.amount}股, 成本: {position.cost_basis:.2f}")

    if positions_info:
        log.info("当日收盘持仓情况:")
        for info in positions_info:
            log.info(info)
    else:
        log.info("当日收盘无持仓")

    # 执行持久化保存
    save_strategy_state()

    log.info("盘后处理完成")

def execute_sell_then_buy_orders_ptrade(context, target_values):
    """
    优化的交易执行函数：先卖后买，避免资金不足问题
    适配Ptrade API的特点

    参数:
        context: 策略上下文
        target_values: 字典，包含每个标的的目标市值 {security: target_value}
    """
    try:
        log.info("开始执行先卖后买交易策略（Ptrade版本）")

        # 第一步：计算当前持仓和目标持仓的差异
        sell_orders = []  # 需要卖出的订单
        buy_orders = []   # 需要买入的订单

        for security in g.stock_list:
            try:
                # 获取当前持仓
                current_position = context.portfolio.positions.get(security)
                current_value = 0
                if current_position and current_position.amount > 0:
                    # 使用get_snapshot获取当前价格（Ptrade API）
                    snapshot = get_snapshot(security)
                    if snapshot and security in snapshot and snapshot[security]['trade_status'] != 'HALT':
                        current_value = current_position.amount * snapshot[security]['last_px']

                # 获取目标市值
                target_value = target_values.get(security, 0)

                # 计算差异
                value_diff = target_value - current_value

                if value_diff < -g.min_trade_amount:  # 需要卖出
                    sell_orders.append({
                        'security': security,
                        'target_value': target_value,
                        'current_value': current_value,
                        'value_diff': value_diff
                    })
                    log.info(f"计划卖出 {security}: 当前市值 {current_value:.2f} -> 目标市值 {target_value:.2f}")

                elif value_diff > g.min_trade_amount:  # 需要买入
                    buy_orders.append({
                        'security': security,
                        'target_value': target_value,
                        'current_value': current_value,
                        'value_diff': value_diff
                    })
                    log.info(f"计划买入 {security}: 当前市值 {current_value:.2f} -> 目标市值 {target_value:.2f}")

            except Exception as e:
                log.error(f"计算 {security} 交易差异时出错: {str(e)}")
                continue

        # 第二步：执行所有卖出订单
        total_sell_value = 0
        if sell_orders:
            log.info(f"开始执行 {len(sell_orders)} 个卖出订单")
            for order_info in sell_orders:
                try:
                    security = order_info['security']
                    target_value = order_info['target_value']

                    # 检查标的是否暂停交易（Ptrade API）
                    snapshot = get_snapshot(security)
                    if not snapshot or security not in snapshot:
                        log.warning(f"{security} 无法获取快照数据，跳过卖出")
                        continue

                    if snapshot[security]['trade_status'] == 'HALT':
                        log.warning(f"{security} 暂停交易，跳过卖出")
                        continue

                    # 获取当前持仓信息
                    current_position = context.portfolio.positions.get(security)
                    if not current_position or current_position.amount <= 0:
                        log.warning(f"{security} 当前无持仓，跳过卖出")
                        continue

                    current_price = snapshot[security]['last_px']
                    current_amount = current_position.amount
                    current_market_value = current_amount * current_price

                    # 处理目标市值为负数或需要清仓的情况
                    if target_value <= 0:
                        # 目标市值为负数或零，直接清仓
                        order_target(security, 0)
                        total_sell_value += current_market_value
                        log.info(f"执行清仓 {security}: 当前持仓 {current_amount}股, 市值 {current_market_value:.2f}")
                    else:
                        # 计算目标持仓数量
                        target_amount = int(target_value / current_price / 100) * 100  # 确保是100的整数倍

                        if target_amount >= current_amount:
                            # 目标数量大于等于当前数量，不需要卖出
                            log.info(f"{security} 目标数量({target_amount})大于等于当前数量({current_amount})，跳过卖出")
                            continue

                        # 正常卖出到目标数量
                        sell_amount = current_amount - target_amount
                        sell_value = sell_amount * current_price

                        order_target_value(security, target_value)
                        total_sell_value += sell_value
                        log.info(f"执行卖出 {security}: 目标市值 {target_value:.2f}, 卖出 {sell_amount}股")

                except Exception as e:
                    log.error(f"执行 {security} 卖出订单时出错: {str(e)}")
                    continue

            log.info(f"卖出订单执行完成，预计释放资金: {total_sell_value:.2f}")

        # 第三步：等待卖出订单处理（在实盘中，这里可以添加订单状态检查）
        # 注意：在回测中，订单是立即执行的，但在实盘中可能需要等待

        # 第四步：执行所有买入订单
        if buy_orders:
            log.info(f"开始执行 {len(buy_orders)} 个买入订单")

            # 按照买入金额排序，优先执行小额买入
            buy_orders.sort(key=lambda x: x['value_diff'])

            for order_info in buy_orders:
                try:
                    security = order_info['security']
                    target_value = order_info['target_value']

                    # 检查标的是否暂停交易（Ptrade API）
                    snapshot = get_snapshot(security)
                    if not snapshot or security not in snapshot:
                        log.warning(f"{security} 无法获取快照数据，跳过买入")
                        continue

                    if snapshot[security]['trade_status'] == 'HALT':
                        log.warning(f"{security} 暂停交易，跳过买入")
                        continue

                    # 检查可用资金（Ptrade API使用cash）
                    available_cash = context.portfolio.cash
                    required_cash = order_info['value_diff']

                    if available_cash >= required_cash:
                        # 执行买入订单
                        order_target_value(security, target_value)
                        log.info(f"执行买入 {security}: 目标市值 {target_value:.2f}, 需要资金 {required_cash:.2f}")
                    else:
                        log.warning(f"{security} 资金不足: 需要 {required_cash:.2f}, 可用 {available_cash:.2f}")
                        # 可以选择部分买入或跳过
                        if available_cash > g.min_trade_amount:
                            # 部分买入
                            partial_target = order_info['current_value'] + available_cash
                            order_target_value(security, partial_target)
                            log.info(f"部分买入 {security}: 目标市值 {partial_target:.2f} (原计划 {target_value:.2f})")
                        else:
                            log.warning(f"跳过买入 {security}: 可用资金不足")

                except Exception as e:
                    log.error(f"执行 {security} 买入订单时出错: {str(e)}")
                    continue

            log.info("买入订单执行完成")

        log.info("先卖后买交易策略执行完成")

    except Exception as e:
        log.error(f"执行先卖后买交易策略时出错: {str(e)}")
        # 如果优化的交易执行失败，回退到原始方法
        log.info("回退到原始交易方法")
        for security, target_value in target_values.items():
            try:
                order_target_value(security, target_value)
                log.info(f"回退执行 {security}: 目标市值 {target_value:.2f}")
            except Exception as e2:
                log.error(f"回退执行 {security} 时出错: {str(e2)}")
                continue

# ==================== Ptrade 优化说明 ====================
#
# 本次优化主要解决了策略中同时进行买卖导致资金不足的问题：
#
# 1. 问题分析：
#    - 原策略使用 order_target_value 同时调整所有标的
#    - 当多个标的需要增加仓位时，可能因资金不足导致买入失败
#    - 卖出订单释放的资金无法及时用于买入订单
#
# 2. 解决方案：
#    - 新增 execute_sell_then_buy_orders_ptrade 函数，实现先卖后买逻辑
#    - 分离买卖操作：先执行所有卖出订单，再执行买入订单
#    - 优化资金利用：确保卖出释放的资金可用于后续买入
#
# 3. Ptrade API 适配：
#    - 使用 context.portfolio.cash 替代 available_cash
#    - 使用 position.amount 替代 position.total_amount
#    - 使用 get_snapshot() 获取实时行情和交易状态
#    - 使用 trade_status 检查标的是否暂停交易
#
# 4. 优化的函数：
#    - rebalance_portfolio: 月度再平衡使用先卖后买
#    - apply_momentum_overlay: 周度动量调整使用先卖后买
#    - execute_initial_buy: 初始建仓保持原逻辑（只涉及买入）
#
# 5. 新增功能：
#    - 智能订单排序：按金额大小优化执行顺序
#    - 资金检查：确保每笔买入都有足够资金
#    - 部分买入：资金不足时支持部分买入
#    - 错误回退：失败时自动回退到原始交易方法
#    - Ptrade 特有的交易状态检查和快照数据验证
#
# 6. 卖出数量大于持仓数量的处理逻辑修复：
#    - 问题：当动量增强策略将权重调整为负数时，计算出的卖出数量可能超过实际持仓
#    - 原逻辑：选择不进行交易（错误的处理方式）
#    - 新逻辑：当目标市值为负数或零时，直接清仓该标的
#    - 实现：在execute_sell_then_buy_orders_ptrade函数中添加特殊处理
#      * 检查target_value <= 0的情况
#      * 使用order_target(security, 0)直接清仓
#      * 记录清仓操作的详细日志
#    - 适用场景：动量策略权重调整、风控止损、策略退出等
#
# 7. 日志优化：
#    - 详细记录每步交易计划和执行情况
#    - 区分卖出和买入阶段的日志
#    - 记录资金使用情况和潜在问题
#    - 标识为 Ptrade 版本的先卖后买策略
#    - 新增清仓操作的专门日志记录
#
# 8. 与 JoinQuant 版本的区别：
#    - API 调用方式适配 Ptrade 平台
#    - 数据结构和字段名称的差异处理
#    - 交易状态检查方式的调整
#    - 错误处理机制的平台化适配
#
# ==================== 优化完成 ====================
