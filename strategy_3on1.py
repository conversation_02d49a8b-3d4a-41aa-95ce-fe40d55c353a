# 克隆自聚宽文章：https://www.joinquant.com/post/49474
# 标题：集合竞价三合一，今年收益1067%，年化198%
# 作者：jevon y

# 克隆自聚宽文章：https://www.joinquant.com/post/44901
# 标题：首板低开策略
# 作者：wywy1995

# 克隆自聚宽文章：https://www.joinquant.com/post/48523
# 标题：一进二集合竞价策略
# 作者：十足的小市值迷

# 克隆自聚宽文章：https://www.joinquant.com/post/49364
# 标题：一种弱转强的选股策略，年化100%以上
# 作者：紫露薇霜

# 2024/08/01  止损卖出修改为跌破5日均线

from jqdata import *
from jqfactor import *
from jqlib.technical_analysis import *
import datetime as dt
import pandas as pd
from datetime import datetime
from datetime import timedelta

# 全局缓存变量
g.cache = {
    'price_data': None,  # 存储价格数据
    'valuation_data': None,  # 存储估值数据
    'auction_data': None,  # 存储集合竞价数据
    'last_update_date': None,  # 上次更新数据的日期
    'industry_data': None,  # 存储行业数据
}

def update_cache(context, stock_list, date):
    """更新全局缓存数据"""
    if g.cache['last_update_date'] == date:
        return
        
    # 获取价格数据（包含3天的数据）
    price_fields = ['open', 'close', 'high', 'low', 'volume', 'money', 'high_limit']
    g.cache['price_data'] = get_price(stock_list, 
                                    end_date=date, 
                                    frequency='daily', 
                                    fields=price_fields, 
                                    count=3, 
                                    panel=False, 
                                    fill_paused=False, 
                                    skip_paused=False)
    
    # 获取估值数据
    g.cache['valuation_data'] = get_valuation(stock_list, 
                                            start_date=date, 
                                            end_date=date, 
                                            fields=['turnover_ratio', 'market_cap', 'circulating_market_cap'])
    
    # 获取集合竞价数据
    date_str = date.strftime("%Y-%m-%d")
    start = date_str + " 09:15:00"
    end = date_str + " 09:26:00"
    g.cache['auction_data'] = get_call_auction(stock_list, 
                                             start_date=start, 
                                             end_date=end, 
                                             fields=['time', 'volume', 'current'])
    
    g.cache['last_update_date'] = date

def get_cached_price_data(stock, field, lookback=1):
    """从缓存中获取价格数据"""
    if g.cache['price_data'] is None:
        return None
    stock_data = g.cache['price_data'][g.cache['price_data']['code'] == stock]
    if len(stock_data) < lookback:
        return None
    return stock_data[field].iloc[-lookback:]

def get_cached_valuation_data(stock, field):
    """从缓存中获取估值数据"""
    if g.cache['valuation_data'] is None:
        return None
    stock_data = g.cache['valuation_data'][g.cache['valuation_data']['code'] == stock]
    if len(stock_data) == 0:
        return None
    return stock_data[field].iloc[0]

def get_cached_auction_data(stock, field):
    """从缓存中获取集合竞价数据"""
    if g.cache['auction_data'] is None:
        return None
    stock_data = g.cache['auction_data'][g.cache['auction_data']['code'] == stock]
    if len(stock_data) == 0:
        return None
    return stock_data[field].iloc[0]

def calculate_zyts_vectorized(price_data, lookback=101):
    """向量化计算左压天数"""
    if price_data is None or len(price_data) < lookback:
        return 100
    
    high_prices = price_data['high'].iloc[-lookback:]
    prev_high = high_prices.iloc[-1]
    
    # 使用向量化操作计算左压天数
    mask = high_prices >= prev_high
    if not mask.any():
        return 100
    
    zyts_0 = len(high_prices) - mask[::-1].idxmax() - 1
    return zyts_0 + 5

def calculate_ma5_vectorized(price_data):
    """向量化计算MA5"""
    if price_data is None or len(price_data) < 4:
        return None
    
    close_prices = price_data['close']
    m4 = close_prices.iloc[-4:].mean()
    ma5 = (m4 * 4 + close_prices.iloc[-1]) / 5
    return ma5

def initialize(context):
    set_option('use_real_price', True)
    log.set_level('system', 'error')
    set_option('avoid_future_data', True)
    # 一进二
    run_daily(get_stock_list, '9:01')
    run_daily(buy, '09:26')
    run_daily(sell, time='11:25', reference_security='000300.XSHG')
    run_daily(sell, time='14:50', reference_security='000300.XSHG')


    # 首版低开
    # run_daily(buy2, '09:27') #9:25分知道开盘价后可以提前下单


# 选股
def get_stock_list(context): 
    # 文本日期
    date = context.previous_date
    date = transform_date(date, 'str')
    date_1 = get_shifted_date(date, -1, 'T')
    date_2 = get_shifted_date(date, -2, 'T')

    # 初始列表
    initial_list = prepare_stock_list(date)
    
    # 更新缓存数据
    update_cache(context, initial_list, context.previous_date)
    
    # 获取价格数据
    price_data = g.cache['price_data']
    if price_data is None:
        return
    
    # 将日期转换为 pd.Timestamp
    date_ts = pd.Timestamp(date)
    date_1_ts = pd.Timestamp(date_1)
    date_2_ts = pd.Timestamp(date_2)
    
    # 使用向量化操作筛选涨停股票
    hl_mask = (price_data['close'] == price_data['high_limit']) & (price_data['time'] == date_ts)
    hl_list = price_data[hl_mask]['code'].tolist()
    
    # 使用向量化操作筛选曾涨停股票
    hl1_mask = (price_data['high'] == price_data['high_limit']) & (price_data['time'] == date_1_ts)
    hl1_list = price_data[hl1_mask]['code'].tolist()
    
    hl2_mask = (price_data['high'] == price_data['high_limit']) & (price_data['time'] == date_2_ts)
    hl2_list = price_data[hl2_mask]['code'].tolist()
    
    # 使用集合操作剔除重复股票
    elements_to_remove = set(hl1_list + hl2_list)
    hl_list = [stock for stock in hl_list if stock not in elements_to_remove]
    
    g.target_list = hl_list
    
    # 使用向量化操作筛选曾涨停但未封板股票
    h1_mask = (price_data['high'] == price_data['high_limit']) & (price_data['close'] != price_data['high_limit']) & (price_data['time'] == date_ts)
    h1_list = price_data[h1_mask]['code'].tolist()
    
    # 过滤上上个交易日涨停
    elements_to_remove = hl_list
    all_list = [stock for stock in h1_list if stock not in elements_to_remove]
    
    g.target_list2 = all_list




# 交易
def check_bank_industry_leading(context):
    """检查银行行业是否领涨"""
    try:
        # 获取申万一级行业列表
        industries = get_industries(name='sw_l1')
        industry_code_list_str = industries.index.tolist()
        
        # 确定银行行业代码
        bank_industry_code_str = '801780'  # 银行I
        
        # 获取查询日期
        date_T_minus_1_obj = context.previous_date
        date_T_minus_2_obj = get_trade_days(end_date=date_T_minus_1_obj, count=2)[0]
        date_T_minus_1_str = date_T_minus_1_obj.strftime('%Y-%m-%d')
        date_T_minus_2_str = date_T_minus_2_obj.strftime('%Y-%m-%d')
        
        # 将行业代码转换为整数列表
        industry_code_list_int = [int(c) for c in industry_code_list_str]
        
        # 构建单个查询获取所有行业数据
        q = query(
            finance.SW1_DAILY_PRICE.code,
            finance.SW1_DAILY_PRICE.date,
            finance.SW1_DAILY_PRICE.close
        ).filter(
            finance.SW1_DAILY_PRICE.code.in_(industry_code_list_int),
            finance.SW1_DAILY_PRICE.date.in_([date_T_minus_2_str, date_T_minus_1_str])
        )
        
        # 执行批量查询
        all_industries_prices_df = finance.run_query(q)
        
        # 初始化行业涨幅字典
        industry_returns = {}
        
        # 处理查询结果并计算涨幅
        if not all_industries_prices_df.empty:
            # 确保数据按行业代码和日期排序
            all_industries_prices_df = all_industries_prices_df.sort_values(by=['code', 'date'])
            
            # 按行业代码分组处理
            for code_int, group_df in all_industries_prices_df.groupby('code'):
                if len(group_df) == 2:  # 确保有T-2和T-1两天的数据
                    close_T_minus_2 = group_df['close'].iloc[0]
                    close_T_minus_1 = group_df['close'].iloc[1]
                    
                    # 确保分母不为零
                    if close_T_minus_2 != 0:
                        pct_change = (close_T_minus_1 - close_T_minus_2) / close_T_minus_2
                        industry_returns[str(code_int)] = pct_change
        
        # 找出领涨行业并判断
        if industry_returns:
            leading_industry_code_str = max(industry_returns, key=industry_returns.get)
            
            if leading_industry_code_str == bank_industry_code_str:
                print('银行行业昨日领涨，今日不进行交易')
                return True
                
    except Exception as e:
        pass
    
    return False

def calculate_position_size(context, num_selected):
    """计算每只股票的买入金额和仓位百分比，考虑单股30%上限"""
    total_portfolio_value = context.portfolio.total_value
    available_cash = context.portfolio.available_cash

    if num_selected == 0 or total_portfolio_value == 0:  # 增加对 total_portfolio_value 的检查
        return 0, 0

    # 1. 根据选股数量确定目标总投入仓位比例
    if num_selected == 1:
        target_capital_percentage = 0.60
    elif num_selected == 2:
        target_capital_percentage = 0.70
    elif num_selected == 3:
        target_capital_percentage = 0.80
    else:  # num_selected >= 4
        target_capital_percentage = 1.00
    
    # 2. 计算计划投入的总资金 和 实际可投入的总资金
    planned_total_investment = total_portfolio_value * target_capital_percentage
    actual_total_investment_for_all_stocks = min(planned_total_investment, available_cash)
    
    # 3. 初步平均分配到每只股票
    initial_value_per_stock = actual_total_investment_for_all_stocks / num_selected
    
    # 4. 应用单只股票不超过总资产30%的硬顶限制
    max_value_allowed_by_30_cap = total_portfolio_value * 0.30
    final_value_per_stock = min(initial_value_per_stock, max_value_allowed_by_30_cap)
    
    # 5. 计算最终的每股仓位百分比 (相对于总资产)
    final_percentage_per_stock = (final_value_per_stock / total_portfolio_value) * 100
            
    return final_value_per_stock, final_percentage_per_stock

def buy(context):
    # 检查银行行业是否领涨
    if check_bank_industry_leading(context):
        return
        
    qualified_stocks = []
    gk_stocks = []
    dk_stocks = []
    rzq_stocks = []
    current_data = get_current_data()
    date_now = context.current_dt.strftime("%Y-%m-%d")
    
    # 合并所有候选股票
    all_candidates = list(set(g.target_list + g.target_list2))
    if not all_candidates:
        return
        
    # 更新缓存数据
    update_cache(context, all_candidates, context.previous_date)
    
    # 获取缓存数据
    price_data = g.cache['price_data']
    valuation_data = g.cache['valuation_data']
    auction_data = g.cache['auction_data']
    
    if price_data is None or valuation_data is None or auction_data is None:
        return
        
    # 将日期转换为 pd.Timestamp
    prev_date_ts = pd.Timestamp(context.previous_date)
        
    # 高开策略
    if g.target_list:
        # 计算均价涨幅
        prev_day_data = price_data[price_data['time'] == prev_date_ts]
        avg_price_increase = (prev_day_data['money'] / prev_day_data['volume'] / prev_day_data['close'] * 1.1 - 1)
        
        # 市值和换手率过滤
        market_cap_mask = (valuation_data['market_cap'] >= 70) & (valuation_data['circulating_market_cap'] <= 520)
        
        # 合并条件
        gk_mask = (avg_price_increase >= 0.07) & (prev_day_data['money'] >= 5.5e8) & (prev_day_data['money'] <= 20e8)
        
        # 获取符合条件的股票代码
        qualified_codes = prev_day_data[gk_mask]['code'].tolist()
        
        # 计算左压天数
        for stock in qualified_codes:
            # 检查市值条件
            stock_valuation = valuation_data[valuation_data['code'] == stock]
            if len(stock_valuation) == 0 or not market_cap_mask[stock_valuation.index[0]]:
                continue
                
            stock_price_data = price_data[price_data['code'] == stock]
            zyts = calculate_zyts_vectorized(stock_price_data)
            
            # 获取集合竞价数据
            stock_auction = auction_data[auction_data['code'] == stock]
            if len(stock_auction) == 0:
                continue
                
            # 计算量比和开盘价比例
            volume_ratio = stock_auction['volume'].iloc[0] / stock_price_data['volume'].iloc[-1]
            current_ratio = stock_auction['current'].iloc[0] / (current_data[stock].high_limit/1.1)
            
            if volume_ratio >= 0.03 and 1 < current_ratio < 1.06:
                gk_stocks.append(stock)
                qualified_stocks.append(stock)
    
    # 低开策略
    if g.target_list2:
        # 获取非连板涨停股票
        hl_list = price_data[(price_data['close'] == price_data['high_limit']) & (price_data['time'] == prev_date_ts)]['code'].tolist()
        
        # 计算相对位置
        rpd = get_relative_position_df(hl_list, context.previous_date, 60)
        rpd = rpd[rpd['rp'] <= 0.5]
        stock_list = list(rpd.index)
        
        if stock_list:
            # 计算开盘价比例
            open_pct = pd.Series([current_data[s].day_open/price_data[price_data['code'] == s]['close'].iloc[-1] for s in stock_list], index=stock_list)
            stock_list = list(open_pct[(0.955 <= open_pct) & (open_pct <= 0.97)].index)
            
            # 金额过滤
            for s in stock_list:
                prev_day_data = price_data[(price_data['code'] == s) & (price_data['time'] == prev_date_ts)]
                if prev_day_data['money'].iloc[0] >= 1e8:
                    dk_stocks.append(s)
                    qualified_stocks.append(s)
    
    # 弱转强策略
    if g.target_list2:
        for s in g.target_list2:
            # 获取历史数据
            stock_price_data = price_data[price_data['code'] == s]
            if len(stock_price_data) < 4:
                continue
                
            # 计算涨幅
            increase_ratio = (stock_price_data['close'].iloc[-1] - stock_price_data['close'].iloc[0]) / stock_price_data['close'].iloc[0]
            if increase_ratio > 0.28:
                continue
                
            # 计算开盘收盘比例
            prev_day_data = stock_price_data[stock_price_data['time'] == prev_date_ts]
            if len(prev_day_data) == 0:
                continue
                
            open_close_ratio = (prev_day_data['close'].iloc[0] - prev_day_data['open'].iloc[0]) / prev_day_data['open'].iloc[0]
            if open_close_ratio < -0.05:
                continue
                
            # 计算均价涨幅
            avg_price_increase = prev_day_data['money'].iloc[0] / prev_day_data['volume'].iloc[0] / prev_day_data['close'].iloc[0] - 1
            if avg_price_increase < -0.04 or prev_day_data['money'].iloc[0] < 3e8 or prev_day_data['money'].iloc[0] > 19e8:
                continue
                
            # 市值过滤
            stock_valuation = valuation_data[valuation_data['code'] == s]
            if len(stock_valuation) == 0 or stock_valuation['market_cap'].iloc[0] < 70 or stock_valuation['circulating_market_cap'].iloc[0] > 520:
                continue
                
            # 计算左压天数
            zyts = calculate_zyts_vectorized(stock_price_data)
            
            # 获取集合竞价数据
            stock_auction = auction_data[auction_data['code'] == s]
            if len(stock_auction) == 0:
                continue
                
            # 计算量比和开盘价比例
            volume_ratio = stock_auction['volume'].iloc[0] / stock_price_data['volume'].iloc[-1]
            current_ratio = stock_auction['current'].iloc[0] / (current_data[s].high_limit/1.1)
            
            if volume_ratio >= 0.03 and 0.98 < current_ratio < 1.09:
                rzq_stocks.append(s)
                qualified_stocks.append(s)
    
    # 输出选股结果
    if qualified_stocks:
        print('———————————————————————————————————')
        send_message('今日选股：'+','.join(qualified_stocks))
        print('一进二：'+','.join(gk_stocks))
        print('首板低开：'+','.join(dk_stocks))
        print('弱转强：'+','.join(rzq_stocks))
        print('今日选股：'+','.join(qualified_stocks))
        print('———————————————————————————————————')
    else:
        send_message('今日无目标个股')
        print('今日无目标个股')
    
    # 执行交易
    if qualified_stocks and context.portfolio.available_cash/context.portfolio.total_value > 0.3:
        # 计算每只股票的买入金额和仓位百分比
        value_per_stock, percentage_per_stock = calculate_position_size(context, len(qualified_stocks))
        
        for s in qualified_stocks:
            # 检查最小购买单位
            if context.portfolio.available_cash/current_data[s].last_price > 100:
                order_value(s, value_per_stock, MarketOrderStyle(current_data[s].day_open))
                print(f"买入 {s} {percentage_per_stock:.2f}%仓位")
                print('———————————————————————————————————')

# 处理日期相关函数
def transform_date(date, date_type):
    if type(date) == str:
        str_date = date
        dt_date = dt.datetime.strptime(date, '%Y-%m-%d')
        d_date = dt_date.date()
    elif type(date) == dt.datetime:
        str_date = date.strftime('%Y-%m-%d')
        dt_date = date
        d_date = dt_date.date()
    elif type(date) == dt.date:
        str_date = date.strftime('%Y-%m-%d')
        dt_date = dt.datetime.strptime(str_date, '%Y-%m-%d')
        d_date = date
    dct = {'str':str_date, 'dt':dt_date, 'd':d_date}
    return dct[date_type]

def get_shifted_date(date, days, days_type='T'):
    #获取上一个自然日
    d_date = transform_date(date, 'd')
    yesterday = d_date + dt.timedelta(-1)
    #移动days个自然日
    if days_type == 'N':
        shifted_date = yesterday + dt.timedelta(days+1)
    #移动days个交易日
    if days_type == 'T':
        all_trade_days = [i.strftime('%Y-%m-%d') for i in list(get_all_trade_days())]
        #如果上一个自然日是交易日，根据其在交易日列表中的index计算平移后的交易日        
        if str(yesterday) in all_trade_days:
            shifted_date = all_trade_days[all_trade_days.index(str(yesterday)) + days + 1]
        #否则，从上一个自然日向前数，先找到最近一个交易日，再开始平移
        else:
            for i in range(100):
                last_trade_date = yesterday - dt.timedelta(i)
                if str(last_trade_date) in all_trade_days:
                    shifted_date = all_trade_days[all_trade_days.index(str(last_trade_date)) + days + 1]
                    break
    return str(shifted_date)



# 过滤函数
def filter_new_stock(initial_list, date, days=50):
    d_date = transform_date(date, 'd')
    return [stock for stock in initial_list if d_date - get_security_info(stock).start_date > dt.timedelta(days=days)]



def filter_st_stock(initial_list, date):
    str_date = transform_date(date, 'str')
    if get_shifted_date(str_date, 0, 'N') != get_shifted_date(str_date, 0, 'T'):
        str_date = get_shifted_date(str_date, -1, 'T')
    df = get_extras('is_st', initial_list, start_date=str_date, end_date=str_date, df=True)
    df = df.T
    df.columns = ['is_st']
    df = df[df['is_st'] == False]
    filter_list = list(df.index)
    return filter_list

def filter_kcbj_stock(initial_list):
    return [stock for stock in initial_list 
    if stock[0] != '4' 
    and stock[0] != '8' 
    # and stock[0] != '3' 
    and stock[:2] != '68']

def filter_paused_stock(initial_list, date):
    df = get_price(initial_list, end_date=date, frequency='daily', fields=['paused'], count=1, panel=False, fill_paused=True)
    df = df[df['paused'] == 0]
    paused_list = list(df.code)
    return paused_list

# 一字
def filter_extreme_limit_stock(context, stock_list, date):
    tmp = []
    for stock in stock_list:
        df = get_price(stock, end_date=date, frequency='daily', fields=['low','high_limit'], count=1, panel=False)
        if df.iloc[0,0] < df.iloc[0,1]:
            tmp.append(stock)
    return tmp



# 每日初始股票池
def prepare_stock_list(date): 
    initial_list = get_all_securities('stock', date).index.tolist()
    initial_list = filter_kcbj_stock(initial_list)
    initial_list = filter_new_stock(initial_list, date)
    initial_list = filter_st_stock(initial_list, date)
    initial_list = filter_paused_stock(initial_list, date)
    return initial_list


# 计算左压天数
def calculate_zyts(s, context):
    high_prices = attribute_history(s, 101, '1d', fields=['high'], skip_paused=True)['high']
    prev_high = high_prices.iloc[-1]
    zyts_0 = next((i-1 for i, high in enumerate(high_prices[-3::-1], 2) if high >= prev_high), 100)
    zyts = zyts_0 + 5
    return zyts


# 筛选出某一日涨停的股票
def get_hl_stock(initial_list, date):
    df = get_price(initial_list, end_date=date, frequency='daily', fields=['close','high_limit'], count=1, panel=False, fill_paused=False, skip_paused=False)
    df = df.dropna() #去除停牌
    df = df[df['close'] == df['high_limit']]
    hl_list = list(df.code)
    return hl_list
    
# 筛选曾涨停
def get_ever_hl_stock(initial_list, date):
    df = get_price(initial_list, end_date=date, frequency='daily', fields=['high','high_limit'], count=1, panel=False, fill_paused=False, skip_paused=False)
    df = df.dropna() #去除停牌
    df = df[df['high'] == df['high_limit']]
    hl_list = list(df.code)
    return hl_list

# 筛选曾涨停
def get_ever_hl_stock2(initial_list, date):
    df = get_price(initial_list, end_date=date, frequency='daily', fields=['close','high','high_limit'], count=1, panel=False, fill_paused=False, skip_paused=False)
    df = df.dropna() #去除停牌
    cd1 = df['high'] == df['high_limit'] 
    cd2 = df['close']!= df['high_limit']
    df = df[cd1 & cd2]
    hl_list = list(df.code)
    return hl_list

# 计算涨停数
def get_hl_count_df(hl_list, date, watch_days):
    # 获取watch_days的数据
    df = get_price(hl_list, end_date=date, frequency='daily', fields=['close','high_limit','low'], count=watch_days, panel=False, fill_paused=False, skip_paused=False)
    df.index = df.code
    #计算涨停与一字涨停数，一字涨停定义为最低价等于涨停价
    hl_count_list = []
    extreme_hl_count_list = []
    for stock in hl_list:
        df_sub = df.loc[stock]
        hl_days = df_sub[df_sub.close==df_sub.high_limit].high_limit.count()
        extreme_hl_days = df_sub[df_sub.low==df_sub.high_limit].high_limit.count()
        hl_count_list.append(hl_days)
        extreme_hl_count_list.append(extreme_hl_days)
    #创建df记录
    df = pd.DataFrame(index=hl_list, data={'count':hl_count_list, 'extreme_count':extreme_hl_count_list})
    return df

# 计算连板数
def get_continue_count_df(hl_list, date, watch_days):
    df = pd.DataFrame()
    for d in range(2, watch_days+1):
        HLC = get_hl_count_df(hl_list, date, d)
        CHLC = HLC[HLC['count'] == d]
        df = df.append(CHLC)
    stock_list = list(set(df.index))
    ccd = pd.DataFrame()
    for s in stock_list:
        tmp = df.loc[[s]]
        if len(tmp) > 1:
            M = tmp['count'].max()
            tmp = tmp[tmp['count'] == M]
        ccd = ccd.append(tmp)
    if len(ccd) != 0:
        ccd = ccd.sort_values(by='count', ascending=False)    
    return ccd

# 计算昨涨幅
def get_index_increase_ratio(index_code, context):
    # 获取指数昨天和前天的收盘价
    close_prices = attribute_history(index_code, 2, '1d', fields=['close'], skip_paused=True)
    if len(close_prices) < 2:
        return 0  # 如果数据不足，返回0
    day_before_yesterday_close = close_prices['close'][0]
    yesterday_close = close_prices['close'][1]
    
    # 计算涨幅
    increase_ratio = (yesterday_close - day_before_yesterday_close) / day_before_yesterday_close
    return increase_ratio

#上午有利润就跑
def sell(context):
    # 基础信息
    date = transform_date(context.previous_date, 'str')
    current_data = get_current_data()
    
    # 获取持仓股票
    positions = list(context.portfolio.positions)
    if not positions:
        return
        
    # 更新缓存数据
    update_cache(context, positions, context.previous_date)
    
    # 获取价格数据
    price_data = g.cache['price_data']
    if price_data is None:
        return
        
    # 根据时间执行不同的卖出策略
    current_time = context.current_dt.strftime('%H:%M:%S')
    
    if current_time == '11:25:00':
        # 上午止盈
        for s in positions:
            if (context.portfolio.positions[s].closeable_amount != 0 and 
                current_data[s].last_price < current_data[s].high_limit and 
                current_data[s].last_price > context.portfolio.positions[s].avg_cost):
                order_target_value(s, 0)
                print('止盈卖出', [s, get_security_info(s, date).display_name])
                print('———————————————————————————————————')
    
    elif current_time == '14:50:00':
        # 下午止盈止损
        for s in positions:
            if context.portfolio.positions[s].closeable_amount == 0:
                continue
                
            # 计算MA5
            stock_price_data = price_data[price_data['code'] == s]
            if len(stock_price_data) < 4:
                continue
                
            ma5 = calculate_ma5_vectorized(stock_price_data)
            if ma5 is None:
                continue
                
            # 止盈条件
            if (current_data[s].last_price < current_data[s].high_limit and 
                current_data[s].last_price > context.portfolio.positions[s].avg_cost):
                order_target_value(s, 0)
                print('止盈卖出', [s, get_security_info(s, date).display_name])
                print('———————————————————————————————————')
            # 止损条件
            elif current_data[s].last_price < ma5:
                order_target_value(s, 0)
                print('止损卖出', [s, get_security_info(s, date).display_name])
                print('———————————————————————————————————')

# 首版低开策略代码                
def filter_new_stock2(initial_list, date, days=250):
    d_date = transform_date(date, 'd')
    return [stock for stock in initial_list if d_date - get_security_info(stock).start_date > dt.timedelta(days=days)]
    
    
# 每日初始股票池
def prepare_stock_list2(date): 
    initial_list = get_all_securities('stock', date).index.tolist()
    initial_list = filter_kcbj_stock(initial_list)
    initial_list = filter_new_stock2(initial_list, date)
    initial_list = filter_st_stock(initial_list, date)
    initial_list = filter_paused_stock(initial_list, date)
    return initial_list    
    
# 计算股票处于一段时间内相对位置
def get_relative_position_df(stock_list, date, watch_days):
    if len(stock_list) != 0:
        df = get_price(stock_list, end_date=date, fields=['high', 'low', 'close'], count=watch_days, fill_paused=False, skip_paused=False, panel=False).dropna()
        close = df.groupby('code').apply(lambda df: df.iloc[-1,-1])
        high = df.groupby('code').apply(lambda df: df['high'].max())
        low = df.groupby('code').apply(lambda df: df['low'].min())
        result = pd.DataFrame()
        result['rp'] = (close-low) / (high-low)
        return result
    else:
        return pd.DataFrame(columns=['rp'])    
    




