import backtrader as bt
import numpy as np
import pandas as pd
import talib
from datetime import datetime, timedelta

class Momentum31Indicator(bt.Indicator):
    """M(3-1)动量指标"""
    lines = ('momentum',)
    params = (('lookback', 63), ('skip', 21))
    
    def __init__(self):
        super(Momentum31Indicator, self).__init__()
        self.lookback = self.p.lookback
        self.skip = self.p.skip
        
    def next(self):
        if len(self.data) < self.lookback + self.skip + 1:
            self.lines.momentum[0] = 0.0
            return
            
        price_3m_ago = self.data[-self.lookback-self.skip]
        price_1m_ago = self.data[-self.skip]
        self.lines.momentum[0] = (price_1m_ago / price_3m_ago) - 1.0

class RiskParityMomentumStrategy(bt.Strategy):
    """风险平价动量策略"""
    
    params = (
        # 仓位控制参数
        ('total_position_pct', 0.923),  # 总仓位92.3%
        ('reserve_position_pct', 0.077),  # 备用资金7.7%
        
        # 技术指标参数
        ('bias_period', 24),  # BIAS周期
        ('adx_period', 14),   # ADX周期
        ('atr_period', 12),   # ATR周期
        
        # 动量策略参数
        ('momentum_boost_factor', 0.44),  # 动量增强系数
        ('max_momentum_allocation_pct', 0.162),  # 最大动量分配比例
        ('momentum_ma_period', 20),  # 动量判断的均线周期
        ('momentum_threshold', 0.017),  # 动量信号阈值
        
        # M(3-1)动量因子参数
        ('momentum_3_1_lookback', 63),  # 3个月回溯期
        ('momentum_3_1_skip', 21),  # 跳过最近1个月
        ('momentum_3_1_threshold', 0.01),  # M(3-1)动量信号阈值
        
        # 风控参数
        ('single_stock_stop_loss', 0.079),  # 单个标的7.9%止损
        ('portfolio_stop_loss', 0.129),  # 组合12.9%止损
        
        # 因子权重
        ('weight_trend', 0.302),
        ('weight_direction', 0.21),
        ('weight_volatility', 0.122),
        ('weight_volume', 0.223),
        ('weight_bias', 0.101),
        
        # 趋势调整参数
        ('uptrend_base_extra', 0.199),
        ('downtrend_base_reduction', 0.124),
        ('downtrend_pct_limit', 0.6),
        
        # 调整系数
        ('reserve_scale_down_factor', 0.4),
        ('reserve_scale_up_factor', 1.4),
        ('strong_trend_boost', 1.39),
        ('oversold_reduction_factor', 0.82),
        ('momentum_strength_factor_multiplier', 1.75),
        
        # 市场状态参数
        ('adx_trend_threshold', 35),  # ADX趋势阈值
        ('volatility_base', 0.02),  # 基准波动率
        ('min_reserve_pct', 0.05),  # 最小备用金比例
        ('bias_scale_factor', 1.0),  # BIAS缩放因子
        
        # BIAS阈值字典
        ('bias_threshold_dict', {
            '513100.XSHG': 0.1,
            '510300.XSHG': 0.1,
            '518880.XSHG': 0.1
        }),
    )
    
    def __init__(self):
        """初始化策略"""
        # 创建数据字典，方便通过名称访问数据
        self.dnames = {d._name: d for d in self.datas}
        
        # 初始化市场状态字典
        self.market_states = {d._name: 'oscillating' for d in self.datas}
        
        # 初始化上次再平衡和动量检查日期
        self.last_rebalance_date = None
        self.last_momentum_check = None
        
        # 初始化止损标志和日期
        self.stop_trading = False
        self.last_stop_date = None
        
        # 为每个数据源计算技术指标
        for d in self.datas:
            # 计算MA20和MA60
            self.ma20 = {d._name: bt.indicators.SimpleMovingAverage(d.close, period=20)}
            self.ma60 = {d._name: bt.indicators.SimpleMovingAverage(d.close, period=60)}
            
            # 计算BIAS
            self.bias = {d._name: (d.close - self.ma20[d._name]) / self.ma20[d._name]}
            
            # 计算ADX和DI+/DI-
            self.adx = {d._name: bt.indicators.ADX(d, period=self.params.adx_period)}
            self.plus_di = {d._name: bt.indicators.PlusDI(d, period=self.params.adx_period)}
            self.minus_di = {d._name: bt.indicators.MinusDI(d, period=self.params.adx_period)}
            
            # 计算ATR
            self.atr = {d._name: bt.indicators.ATR(d, period=self.params.atr_period)}
            
            # 计算M(3-1)动量因子
            self.momentum31 = {d._name: Momentum31Indicator(d.close, lookback=self.params.momentum_3_1_lookback, skip=self.params.momentum_3_1_skip)}
            
            # 计算波动率
            self.volatility = {d._name: bt.indicators.StdDev(d.close.pct_change(), period=20)}  # 短期波动率
            self.volatility_rp = {d._name: bt.indicators.StdDev(d.close.pct_change(), period=60)}  # 风险平价用长期波动率
            
            # 计算成交量指标
            self.volume_avg5 = {d._name: bt.indicators.SimpleMovingAverage(d.volume, period=5)}
            
    def get_market_state(self, data):
        """根据技术指标判断市场状态"""
        name = data._name
        bias = self.bias[name][0]
        adx = self.adx[name][0]
        plus_di = self.plus_di[name][0]
        minus_di = self.minus_di[name][0]
        
        # 趋势判断标准
        TREND_THRESHOLD = self.params.adx_trend_threshold
        
        # 震荡市判定标准放宽
        if adx < TREND_THRESHOLD or abs(bias) < self.params.bias_threshold_dict.get(name, 0.1) * self.params.bias_scale_factor:
            return 'oscillating'
        
        # 判断市场状态
        if adx > TREND_THRESHOLD:  # 存在明显趋势
            if plus_di > minus_di:  # 上升趋势
                if bias > self.params.bias_threshold_dict.get(name, 0.1):  # BIAS过高，可能超买
                    return 'oscillating'
                return 'uptrend'
            else:  # 下降趋势
                if bias < -self.params.bias_threshold_dict.get(name, 0.1):  # BIAS过低，可能超卖
                    return 'oscillating'
                return 'downtrend'
        else:  # ADX较低，无明显趋势
            if abs(bias) > self.params.bias_threshold_dict.get(name, 0.1):  # BIAS超出阈值
                if bias > self.params.bias_threshold_dict.get(name, 0.1):  # 可能超买
                    return 'downtrend'
                else:  # 可能超卖
                    return 'uptrend'
            return 'oscillating'  # 震荡市场
            
    def calculate_risk_parity_weights(self):
        """计算风险平价权重"""
        weights = {}
        total_inv_vol = 0.0
        
        # 计算每个ETF的波动率倒数之和
        for d in self.datas:
            vol = self.volatility_rp[d._name][0]
            if vol > 0:  # 避免除零
                inv_vol = 1.0 / vol
                weights[d._name] = inv_vol
                total_inv_vol += inv_vol
            else:
                weights[d._name] = 0.0
        
        # 归一化权重
        if total_inv_vol > 0:
            weights = {k: v/total_inv_vol for k, v in weights.items()}
        
        return weights
        
    def is_last_trading_day_of_month(self):
        """判断当前日期是否为当月最后一个交易日"""
        current_date = self.datetime.date(0)
        next_day_date = self.datetime.date(1)  # 获取下一个交易日的日期
        # 如果下一个交易日的月份与当前月份不同，则今天是最后一个交易日
        return next_day_date.month != current_date.month
        
    def calculate_dynamic_allocation_factors(self, data):
        """计算动态配置调整因子"""
        name = data._name
        indicators = {
            'trend_strength': self.adx[name][0],
            'volatility': self.volatility[name][0],
            'volume_ratio': data.volume[0] / self.volume_avg5[name][0] if self.volume_avg5[name][0] > 0 else 1.0,
            'bias': self.bias[name][0],
            'plus_di': self.plus_di[name][0],
            'minus_di': self.minus_di[name][0]
        }
        
        # 计算趋势强度因子 (0.5 ~ 2.0)
        trend_factor = min(2.0, max(0.5, indicators['trend_strength'] / 20))
        
        # 计算趋势方向因子 (0.8 ~ 1.5)
        direction_factor = 1.0
        if indicators['plus_di'] > indicators['minus_di']:
            direction_factor = min(1.5, max(1.0, 1 + (indicators['plus_di'] - indicators['minus_di']) / 100))
        else:
            direction_factor = min(1.0, max(0.8, 1 - (indicators['minus_di'] - indicators['plus_di']) / 100))
        
        # 计算波动率因子 (0.6 ~ 1.5)
        if indicators['volatility'] < self.params.volatility_base:
            volatility_factor = min(1.5, self.params.volatility_base / indicators['volatility'])
        else:
            volatility_factor = max(0.6, self.params.volatility_base / indicators['volatility'])
        
        # 计算成交量因子 (0.7 ~ 1.8)
        if indicators['volume_ratio'] > 1:
            volume_factor = min(1.8, 1 + (indicators['volume_ratio'] - 1) * 0.5)
        else:
            volume_factor = max(0.7, 1 - (1 - indicators['volume_ratio']) * 0.5)
        
        # 计算BIAS影响因子 (0.6 ~ 1.6)
        if abs(indicators['bias']) < self.params.bias_threshold_dict.get(name, 0.1) * 0.5:
            bias_factor = 1.6 - abs(indicators['bias']) / self.params.bias_threshold_dict.get(name, 0.1)
        else:
            bias_factor = max(0.6, 1 - abs(indicators['bias']) / self.params.bias_threshold_dict.get(name, 0.1))
        
        # 计算市场趋势持续性
        trend_persistence = min(1.5, indicators['trend_strength'] / 30)
        
        # 综合计算调整系数
        adjustment_factor = (
            trend_factor * self.params.weight_trend +
            direction_factor * self.params.weight_direction +
            volatility_factor * self.params.weight_volatility +
            volume_factor * self.params.weight_volume +
            bias_factor * self.params.weight_bias
        ) * trend_persistence
        
        # 计算置信度 (0 ~ 1)
        confidence = min(1.0, (
            (indicators['trend_strength'] / 40) * 0.4 +
            (1 - abs(indicators['bias']) / (self.params.bias_threshold_dict.get(name, 0.1) * 2)) * 0.3 +
            (indicators['trend_strength'] / 40) * 0.3 +
            (indicators['volume_ratio'] - 0.5) * 0.2
        ))
        
        return adjustment_factor, confidence
        
    def apply_momentum_overlay(self):
        """应用动量叠加策略"""
        # 计算风险平价权重
        base_weights = self.calculate_risk_parity_weights()
        
        # 计算实际可用的备用金
        total_value = self.broker.getvalue()
        available_cash = self.broker.getcash()
        actual_reserve_pct = available_cash / total_value if total_value > 0 else 0
        
        # 设置最大可用备用金
        max_total_boost = min(actual_reserve_pct / 2, self.params.max_momentum_allocation_pct)
        
        # 识别动量资产
        momentum_assets = []
        for d in self.datas:
            momentum_3_1 = self.momentum31[d._name][0]
            if momentum_3_1 > self.params.momentum_3_1_threshold:
                momentum_strength = momentum_3_1
                if momentum_strength > self.params.momentum_threshold:
                    momentum_assets.append((d, momentum_strength))
        
        # 按动量强度排序
        momentum_assets.sort(key=lambda x: x[1], reverse=True)
        
        # 初始化最终权重
        final_weights = base_weights.copy()
        total_boost_weight = 0.0
        
        # 计算动量调整
        for d, momentum_strength in momentum_assets:
            # 计算该资产的权重增量
            base_weight = float(base_weights[d._name])
            boost = base_weight * (self.params.momentum_boost_factor - 1)
            
            # 根据动量强度调整增强系数
            strength_factor = min(1.5, 1 + momentum_strength * 2)
            adjusted_boost = boost * strength_factor
            
            # 检查是否超过最大增强限制
            if total_boost_weight + adjusted_boost <= max_total_boost:
                final_weights[d._name] = base_weight + adjusted_boost
                total_boost_weight += adjusted_boost
                self.log(f"动量增强 {d._name}: 基础权重 {base_weight:.1%} -> {final_weights[d._name]:.1%}, M(3-1)动量 {momentum_strength:.2%}")
            else:
                # 如果备用金不足，按比例缩减
                remaining_boost = max_total_boost - total_boost_weight
                if remaining_boost > 0:
                    scale = remaining_boost / adjusted_boost
                    final_weights[d._name] = base_weight + (adjusted_boost * scale)
                    total_boost_weight = max_total_boost
                    self.log(f"动量增强 {d._name}: 基础权重 {base_weight:.1%} -> {final_weights[d._name]:.1%}, M(3-1)动量 {momentum_strength:.2%} (已缩减)")
                break
        
        # 计算目标总权益权重
        target_total_equity_weight = sum(base_weights.values()) + total_boost_weight
        max_allowed_weight = 1.0 - self.params.min_reserve_pct
        
        # 确保不超过最大允许权重
        if target_total_equity_weight > max_allowed_weight:
            scale = max_allowed_weight / target_total_equity_weight
            final_weights = {k: v * scale for k, v in final_weights.items()}
            self.log(f"权重总和超过限制，进行等比例缩减，缩减系数: {scale:.3f}")
        
        # 执行调仓
        for d in self.datas:
            target_weight = final_weights[d._name]
            self.order_target_percent(data=d, target=target_weight)
            
    def rebalance_portfolio(self):
        """月度再平衡"""
        # 获取基础风险平价权重
        base_weights = self.calculate_risk_parity_weights()
        
        # 计算动态备用金比例
        current_reserve_pct = self.params.reserve_position_pct
        market_states = {d._name: self.get_market_state(d) for d in self.datas}
        
        # 根据市场状态调整备用金比例
        if any(state == 'downtrend' for state in market_states.values()):
            current_reserve_pct *= self.params.reserve_scale_up_factor
        elif all(state == 'uptrend' for state in market_states.values()):
            current_reserve_pct *= self.params.reserve_scale_down_factor
        
        # 确保备用金比例在合理范围内
        current_reserve_pct = max(self.params.min_reserve_pct, min(current_reserve_pct, 0.3))
        
        # 计算调整因子和置信度
        adjustments = {}
        
        for d in self.datas:
            name = d._name
            market_state = market_states[name]
            adjustment_factor, confidence = self.calculate_dynamic_allocation_factors(d)
            
            # 根据市场状态计算理论调整权重
            if market_state == 'uptrend':
                extra_weight = self.params.uptrend_base_extra * adjustment_factor * confidence
                if self.adx[name][0] > self.params.adx_trend_threshold:
                    extra_weight *= self.params.strong_trend_boost
                adjustments[name] = extra_weight
            elif market_state == 'downtrend':
                reduction = self.params.downtrend_base_reduction * adjustment_factor * confidence
                if self.adx[name][0] > self.params.adx_trend_threshold:
                    reduction *= self.params.oversold_reduction_factor
                adjustments[name] = -reduction
            else:
                adjustments[name] = 0.0
        
        # 计算净调整值
        total_adjustment = sum(adjustments.values())
        
        # 计算可用备用金比例
        available_reserve = max(0.0, current_reserve_pct - self.params.min_reserve_pct)
        
        # 计算缩放因子
        if total_adjustment > 0:
            if total_adjustment > available_reserve:
                scale_factor = available_reserve / total_adjustment
            else:
                scale_factor = 1.0
        else:
            scale_factor = 1.0
        
        # 计算最终权重
        final_weights = {}
        for d in self.datas:
            name = d._name
            base_weight = base_weights[name]
            actual_adjustment = adjustments[name] * scale_factor
            final_weight = max(0.0, base_weight + actual_adjustment)
            final_weights[name] = final_weight
        
        # 计算目标总权益权重
        target_equity_weight = sum(final_weights.values())
        
        # 计算最终备用金比例
        final_reserve_pct = 1.0 - target_equity_weight
        
        # 归一化权重
        total_weight = sum(final_weights.values())
        if total_weight > 0 and abs(total_weight - target_equity_weight) > 1e-6:
            scale = target_equity_weight / total_weight
            final_weights = {k: v * scale for k, v in final_weights.items()}
        
        # 记录日志
        self.log(f"月度再平衡 - 备用金比例: {final_reserve_pct:.1%}")
        for d in self.datas:
            name = d._name
            self.log(f"{name} - 基础权重: {base_weights[name]:.1%}, 最终权重: {final_weights[name]:.1%}, 市场状态: {market_states[name]}")
        
        # 执行调仓
        for d in self.datas:
            self.order_target_percent(data=d, target=final_weights[d._name])
            
    def check_stop_loss(self):
        """检查止损条件"""
        if self.stop_trading:
            return
            
        # 检查组合止损
        portfolio_value = self.broker.getvalue()
        initial_value = self.broker.startingcash
        portfolio_loss = (initial_value - portfolio_value) / initial_value
        
        if portfolio_loss > self.params.portfolio_stop_loss:
            self.log(f"触发组合止损: 损失 {portfolio_loss:.1%}")
            self.stop_trading = True
            self.last_stop_date = self.datetime.date(0)
            # 清空所有持仓
            for d in self.datas:
                self.order_target_percent(data=d, target=0.0)
            return
        
        # 检查个股止损
        for d in self.datas:
            position = self.getposition(d)
            if position.size > 0:
                cost_price = position.price
                current_price = d.close[0]
                loss = (cost_price - current_price) / cost_price
                
                if loss > self.params.single_stock_stop_loss:
                    self.log(f"{d._name} 触发个股止损: 损失 {loss:.1%}")
                    self.stop_trading = True
                    self.last_stop_date = self.datetime.date(0)
                    self.order_target_percent(data=d, target=0.0)
    
    def next(self):
        """策略核心逻辑"""
        # 如果处于止损状态，直接返回
        if self.stop_trading:
            # 检查是否可以恢复交易（例如：第二天）
            if self.datetime.date(0) > self.last_stop_date:
                self.stop_trading = False
                self.log("恢复交易")
            return
            
        # 更新市场状态
        for d in self.datas:
            old_state = self.market_states[d._name]
            new_state = self.get_market_state(d)
            if old_state != new_state:
                self.log(f"{d._name} 市场状态从 {old_state} 变为 {new_state}")
            self.market_states[d._name] = new_state
        
        # 检查止损
        self.check_stop_loss()
        
        # 执行周度动量调整
        if (self.datetime.date(0).weekday() == 0 and  # 周一
            not self.is_last_trading_day_of_month() and  # 不是月末
            (self.last_momentum_check is None or  # 第一次执行
             self.datetime.date(0) > self.last_momentum_check)):  # 新的一周
            self.apply_momentum_overlay()
            self.last_momentum_check = self.datetime.date(0)
        
        # 执行月度再平衡
        if (self.is_last_trading_day_of_month() and
            (self.last_rebalance_date is None or
             self.datetime.date(0) > self.last_rebalance_date)):
            self.rebalance_portfolio()
            self.last_rebalance_date = self.datetime.date(0)
            
    def log(self, txt, dt=None):
        """日志记录"""
        dt = dt or self.datas[0].datetime.date(0)
        print(f'{dt.isoformat()} {txt}') 