import optuna
import jqdatasdk as jq
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings
import matplotlib.pyplot as plt
from matplotlib import font_manager
import logging
import json
import os
import random
from dateutil.relativedelta import relativedelta
import backtrader as bt
from risk_parity_momentum_strategy import RiskParityMomentumStrategy
warnings.filterwarnings('ignore')

# 设置日志
logging.basicConfig(level=logging.INFO)
log = logging.getLogger(__name__)

# 设置中文字体
plt.rcParams['font.family'] = ['Heiti TC']
plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题

# # 登录聚宽 (注释掉，因为我们从本地文件读取)
# jq.auth('18651868267', '19960808James')

def save_params(params, filename):
    """保存参数到文件"""
    with open(filename, 'w') as f:
        json.dump(params, f, indent=4)

def load_params(filename):
    """从文件加载参数"""
    with open(filename, 'r') as f:
        return json.load(f)

def calculate_sharpe_ratio(returns, risk_free_rate=0.03):
    """计算夏普比率"""
    if len(returns) < 2:
        return 0
    # 处理NaN值
    returns = np.nan_to_num(returns, nan=0.0)
    excess_returns = returns - risk_free_rate/252
    if excess_returns.std() == 0:
        return 0
    return np.sqrt(252) * excess_returns.mean() / excess_returns.std()

def calculate_annual_return(returns):
    if len(returns) < 2:
        return 0
    cumulative = np.cumprod(1 + np.nan_to_num(returns, nan=0.0))
    total_return = cumulative[-1] - 1
    years = len(returns) / 252
    if years <= 0:
        return 0
    return (1 + total_return) ** (1 / years) - 1

def select_random_time_period(df, min_days=756, max_days=1260):
    """从数据中随机选择一个时间段
    
    参数:
    df: 包含时间索引的DataFrame
    min_days: 最小天数（默认756个交易日，约3年）
    max_days: 最大天数（默认1260个交易日，约5年）
    
    返回:
    选择的时间段的DataFrame
    """
    # 确保数据足够长
    if len(df) < min_days:
        log.warning(f"数据长度不足 {min_days} 天，使用全部可用数据")
        return df
    
    # 随机选择时间段长度
    period_length = random.randint(min_days, min(max_days, len(df)))
    
    # 随机选择开始位置
    max_start_idx = len(df) - period_length
    start_idx = random.randint(0, max_start_idx)
    
    # 获取对应的日期范围
    start_date = df.index[start_idx]
    end_date = df.index[start_idx + period_length - 1]
    
    log.info(f"随机选择的时间段: {start_date.strftime('%Y-%m-%d')} 至 {end_date.strftime('%Y-%m-%d')} (共 {period_length} 个交易日)")
    
    # 返回选定时间段的数据
    return df.iloc[start_idx:start_idx + period_length]

def run_backtest(params, prices_dict, stock_list):
    """运行Backtrader回测"""
    try:
        # 创建 Cerebro 实例
        cerebro = bt.Cerebro()
        
        # 添加数据源
        for stock in stock_list:
            data = bt.feeds.PandasData(
                dataname=prices_dict[stock],
                name=stock
            )
            cerebro.adddata(data)
        
        # 添加策略
        cerebro.addstrategy(RiskParityMomentumStrategy, **params)
        
        # 设置初始资金
        cerebro.broker.setcash(1000000.0)
        
        # 设置佣金和滑点
        cerebro.broker.setcommission(commission=0.0003)  # 0.03% 佣金
        cerebro.broker.set_slippage_fixed(0.0001)  # 0.01% 固定滑点
        
        # 添加分析器
        cerebro.addanalyzer(bt.analyzers.SharpeRatio_A, _name='sharpe')
        cerebro.addanalyzer(bt.analyzers.AnnualReturn, _name='annual_return')
        cerebro.addanalyzer(bt.analyzers.DrawDown, _name='drawdown')
        
        # 运行回测
        results = cerebro.run()
        
        # 获取结果
        sharpe_ratio = results[0].analyzers.sharpe.get_analysis()['sharperatio']
        annual_return = results[0].analyzers.annual_return.get_analysis()['annreturn']
        
        return annual_return, sharpe_ratio
        
    except Exception as e:
        log.error(f"回测过程出错: {str(e)}")
        import traceback
        log.error(traceback.format_exc())
        return float('-inf'), float('-inf')

def convert_params_to_numeric(params):
    """将参数字典中的字符串转换为数值类型"""
    converted = {}
    for key, value in params.items():
        if isinstance(value, dict):
            converted[key] = convert_params_to_numeric(value)
        elif isinstance(value, str):
            try:
                # 尝试转换为整数
                converted[key] = int(value)
            except ValueError:
                try:
                    # 尝试转换为浮点数
                    converted[key] = float(value)
                except ValueError:
                    # 如果无法转换，保持原样
                    converted[key] = value
        else:
            converted[key] = value
    return converted

def objective_stage1(trial, prices_dict, stock_list):
    """第一阶段优化目标函数"""
    try:
        # 定义固定参数
        default_or_fixed_params = {
            # 技术指标参数
            'bias_period': 24,
            'adx_period': 14,
            'atr_period': 12,
            
            # 动量策略参数
            'momentum_ma_period': 20,
            'momentum_threshold': 0.017,
            
            # M(3-1)动量因子参数
            'momentum_3_1_lookback': 63,
            'momentum_3_1_skip': 21,
            'momentum_3_1_threshold': 0.01,
            
            # 风控参数
            'single_stock_stop_loss': 0.079,
            'portfolio_stop_loss': 0.129,
            
            # 因子权重
            'weight_volatility': 0.122,
            'weight_volume': 0.223,
            'weight_bias': 0.101,
            
            # 调整系数
            'reserve_scale_down_factor': 0.4,
            'reserve_scale_up_factor': 1.4,
            'strong_trend_boost': 1.39,
            'oversold_reduction_factor': 0.82,
            'momentum_strength_factor_multiplier': 1.75,
            
            # 市场状态参数
            'adx_trend_threshold': 35,
            'volatility_base': 0.02,
            'min_reserve_pct': 0.05,
            'bias_scale_factor': 1.0,
            
            # BIAS阈值字典
            'bias_threshold_dict': {
                '513100.XSHG': 0.1,
                '510300.XSHG': 0.1,
                '518880.XSHG': 0.1
            }
        }
        
        # 定义需要优化的参数
        trial_params = {
            # 仓位控制参数
            'total_position_pct': trial.suggest_float('total_position_pct', 0.5, 1.0, step=0.001),
            'reserve_position_pct': trial.suggest_float('reserve_position_pct', 0.05, 0.2, step=0.001),
            
            # 动量策略参数
            'momentum_boost_factor': trial.suggest_float('momentum_boost_factor', 0.1, 0.5, step=0.01),
            'max_momentum_allocation_pct': trial.suggest_float('max_momentum_allocation_pct', 0.1, 0.4, step=0.001),
            
            # 因子权重
            'weight_trend': trial.suggest_float('weight_trend', 0.2, 0.5, step=0.001),
            'weight_direction': trial.suggest_float('weight_direction', 0.2, 0.5, step=0.001),
            
            # 趋势调整参数
            'uptrend_base_extra': trial.suggest_float('uptrend_base_extra', 0.05, 0.2, step=0.001),
            'downtrend_base_reduction': trial.suggest_float('downtrend_base_reduction', 0.05, 0.2, step=0.001),
            'downtrend_pct_limit': trial.suggest_float('downtrend_pct_limit', 0.4, 0.8, step=0.001)
        }
        
        # 合并参数
        params = default_or_fixed_params.copy()
        params.update(trial_params)
        
        # 处理因子权重归一化
        factor_weights = ['weight_trend', 'weight_direction', 'weight_volatility', 'weight_volume', 'weight_bias']
        total_weight = sum(params[w] for w in factor_weights)
        if total_weight > 0:
            for w in factor_weights:
                params[w] = params[w] / total_weight
        
        # 运行回测
        annual_return, sharpe_ratio = run_backtest(params, prices_dict, stock_list)
        
        # 检查结果有效性
        if np.isnan(sharpe_ratio) or np.isnan(annual_return) or np.isinf(sharpe_ratio) or np.isinf(annual_return):
            return (float('-inf'), float('-inf'))
        
        return (annual_return, sharpe_ratio)
        
    except Exception as e:
        log.error(f"第一阶段优化过程出错: {str(e)}")
        import traceback
        log.error(traceback.format_exc())
        return (float('-inf'), float('-inf'))

def objective_stage2(trial, stage1_params, prices_dict, stock_list):
    """第二阶段优化目标函数"""
    try:
        # 定义固定参数
        default_or_fixed_params = {
            # 技术指标参数
            'bias_period': 24,
            'adx_period': 14,
            'atr_period': 12,
            
            # 动量策略参数
            'momentum_ma_period': 20,
            'momentum_threshold': 0.017,
            
            # M(3-1)动量因子参数
            'momentum_3_1_lookback': 63,
            'momentum_3_1_skip': 21,
            'momentum_3_1_threshold': 0.01,
            
            # 市场状态参数
            'adx_trend_threshold': 35,
            'volatility_base': 0.02,
            'min_reserve_pct': 0.05,
            'bias_scale_factor': 1.0,
            
            # BIAS阈值字典
            'bias_threshold_dict': {
                '513100.XSHG': 0.1,
                '510300.XSHG': 0.1,
                '518880.XSHG': 0.1
            }
        }
        
        # 定义需要优化的参数
        trial_params = {
            # 风控参数
            'single_stock_stop_loss': trial.suggest_float('single_stock_stop_loss', 0.03, 0.08, step=0.001),
            'portfolio_stop_loss': trial.suggest_float('portfolio_stop_loss', 0.05, 0.15, step=0.001),
            
            # 因子权重
            'weight_volatility': trial.suggest_float('weight_volatility', 0.03, 0.15, step=0.001),
            'weight_volume': trial.suggest_float('weight_volume', 0.15, 0.40, step=0.001),
            'weight_bias': trial.suggest_float('weight_bias', 0.05, 0.20, step=0.001),
            
            # 调整系数
            'reserve_scale_down_factor': trial.suggest_float('reserve_scale_down_factor', 0.4, 0.8, step=0.01),
            'reserve_scale_up_factor': trial.suggest_float('reserve_scale_up_factor', 1.1, 1.6, step=0.01),
            'strong_trend_boost': trial.suggest_float('strong_trend_boost', 1.1, 1.5, step=0.01),
            'oversold_reduction_factor': trial.suggest_float('oversold_reduction_factor', 0.6, 0.9, step=0.01),
            'momentum_strength_factor_multiplier': trial.suggest_float('momentum_strength_factor_multiplier', 1.0, 3.0, step=0.01)
        }
        
        # 合并参数
        params = default_or_fixed_params.copy()
        params.update(stage1_params)  # 使用第一阶段优化的参数
        params.update(trial_params)   # 使用第二阶段优化的参数
        
        # 处理因子权重归一化
        factor_weights = ['weight_trend', 'weight_direction', 'weight_volatility', 'weight_volume', 'weight_bias']
        total_weight = sum(params[w] for w in factor_weights)
        if total_weight > 0:
            for w in factor_weights:
                params[w] = params[w] / total_weight
        
        # 运行回测
        annual_return, sharpe_ratio = run_backtest(params, prices_dict, stock_list)
        
        # 检查结果有效性
        if np.isnan(sharpe_ratio) or np.isnan(annual_return) or np.isinf(sharpe_ratio) or np.isinf(annual_return):
            return (float('-inf'), float('-inf'))
        
        return (annual_return, sharpe_ratio)
        
    except Exception as e:
        log.error(f"第二阶段优化过程出错: {str(e)}")
        import traceback
        log.error(traceback.format_exc())
        return (float('-inf'), float('-inf'))

def main():
    """主函数"""
    try:
        # 获取历史数据
        stock_list = ['513100.XSHG', '510300.XSHG', '518880.XSHG']
        stock_file_map = {
            '513100.XSHG': '513100.XSHG_daily_data_20150101_20250430_qfq.csv',
            '510300.XSHG': '510300.XSHG_daily_data_20150101_20250430_qfq.csv',
            '518880.XSHG': '518880.XSHG_daily_data_20150101_20250430_qfq.csv'
        }
        log.info("加载本地历史数据...")
        
        # 存储原始数据和处理后的价格数据
        raw_data_dict = {}
        full_prices_dict = {}
        
        # 定义列名映射
        column_mapping = {
            '日期': 'date',
            '开盘': 'open',
            '收盘': 'close',
            '最高': 'high',
            '最低': 'low',
            '成交量': 'volume'
        }
        
        for stock in stock_list:
            file_path = stock_file_map.get(stock)
            if not file_path or not os.path.exists(file_path):
                log.error(f"未找到 {stock} 对应的CSV文件: {file_path}")
                return
            try:
                # 读取CSV文件
                df = pd.read_csv(file_path, parse_dates=['日期'], index_col='日期')
                if df.empty:
                    log.error(f"无法从 {file_path} 加载 {stock} 的价格数据。")
                    return
                
                # 确保索引是 DatetimeIndex
                if not isinstance(df.index, pd.DatetimeIndex):
                    log.error(f"{file_path} 中的日期列未能正确解析为日期时间索引。")
                    return
                
                # 重命名列
                df = df.rename(columns=column_mapping)
                
                # 添加 openinterest 列
                if 'openinterest' not in df.columns:
                    df['openinterest'] = 0
                
                # 保存原始数据和价格
                raw_data_dict[stock] = df
                full_prices_dict[stock] = df
                
            except Exception as e:
                log.error(f"加载 {stock} 数据时出错 ({file_path}): {e}")
                import traceback
                log.error(traceback.format_exc())
                return
        log.info("本地历史数据加载完成。")
        
        # 随机选择时间段进行优化
        log.info("随机选择时间段进行优化...")
        # 获取所有股票数据的共同日期范围
        common_dates = set(full_prices_dict[stock_list[0]].index)
        for stock in stock_list[1:]:
            common_dates = common_dates.intersection(set(full_prices_dict[stock].index))
        
        common_dates = sorted(list(common_dates))
        common_df = pd.DataFrame(index=common_dates)
        
        # 随机选择时间段
        selected_period = select_random_time_period(common_df)
        selected_dates = selected_period.index
        
        # 筛选选定时间段的数据
        optimized_prices_dict = {}
        for stock in stock_list:
            optimized_prices_dict[stock] = full_prices_dict[stock].loc[selected_dates]
        
        # 计算动态基准
        log.info("计算动态基准...")
        try:
            # 尝试加载基准参数
            if os.path.exists('final_optimized_params.json'):
                baseline_params = load_params('final_optimized_params.json')
                # 转换参数类型
                baseline_params = convert_params_to_numeric(baseline_params)
            else:
                # 使用默认基准参数
                baseline_params = {
                    'total_position_pct': 0.923,
                    'reserve_position_pct': 0.077,
                    'bias_period': 24,
                    'adx_period': 14,
                    'atr_period': 12,
                    'momentum_boost_factor': 0.44,
                    'max_momentum_allocation_pct': 0.162,
                    'momentum_ma_period': 20,
                    'momentum_threshold': 0.017,
                    'momentum_3_1_lookback': 63,
                    'momentum_3_1_skip': 21,
                    'momentum_3_1_threshold': 0.01,
                    'single_stock_stop_loss': 0.079,
                    'portfolio_stop_loss': 0.129,
                    'weight_trend': 0.302,
                    'weight_direction': 0.21,
                    'weight_volatility': 0.122,
                    'weight_volume': 0.223,
                    'weight_bias': 0.101,
                    'uptrend_base_extra': 0.199,
                    'downtrend_base_reduction': 0.124,
                    'downtrend_pct_limit': 0.6,
                    'reserve_scale_down_factor': 0.4,
                    'reserve_scale_up_factor': 1.4,
                    'strong_trend_boost': 1.39,
                    'oversold_reduction_factor': 0.82,
                    'momentum_strength_factor_multiplier': 1.75,
                    'bias_threshold': 0.1,
                    'adx_trend_threshold': 35,
                    'volatility_base': 0.02,
                    'min_reserve_pct': 0.05,
                    'bias_scale_factor': 1.0,
                    'bias_threshold_dict': {
                        '513100.XSHG': 0.1,
                        '510300.XSHG': 0.1,
                        '518880.XSHG': 0.1
                    }
                }
            
            # 运行基准回测
            benchmark_annual_return, benchmark_sharpe = run_backtest(baseline_params, optimized_prices_dict, stock_list)
            
            if benchmark_annual_return == float('-inf') or benchmark_sharpe == float('-inf'):
                log.error("动态基准计算失败")
                return
                
            log.info(f"动态基准年化收益: {benchmark_annual_return:.3f}")
            log.info(f"动态基准夏普比率: {benchmark_sharpe:.3f}")
            
        except Exception as e:
            log.error(f"计算动态基准时出错: {str(e)}")
            import traceback
            log.error(traceback.format_exc())
            return
        
        # 第一阶段优化
        log.info("开始第一阶段优化...")
        study1 = optuna.create_study(directions=['maximize', 'maximize'])
        study1.optimize(lambda trial: objective_stage1(trial, optimized_prices_dict, stock_list), n_trials=100)
        
        if len(study1.best_trials) == 0:
            log.error("第一阶段没有成功的优化试验")
            return
        
        # 选择第一阶段的最佳参数
        best_stage1 = max(study1.best_trials, key=lambda t: t.values[1])
        stage1_params = best_stage1.params
        
        if best_stage1.values[0] <= float('-inf') or best_stage1.values[1] <= float('-inf'):
            log.error("第一阶段优化未找到有效解")
            return
        
        # 输出第一阶段结果
        log.info(f"第一阶段最佳参数: {stage1_params}")
        log.info(f"第一阶段最佳年化收益: {best_stage1.values[0]:.3f}")
        log.info(f"第一阶段最佳夏普比率: {best_stage1.values[1]:.3f}")
        log.info(f"相比基准夏普比率提升: {(best_stage1.values[1] - benchmark_sharpe) / benchmark_sharpe * 100:.2f}%")
        log.info(f"相比基准年化收益提升: {(best_stage1.values[0] - benchmark_annual_return) / benchmark_annual_return * 100:.2f}%")
        
        # 保存第一阶段参数
        save_params(stage1_params, 'stage1_best_params.json')
        
        # 第二阶段优化
        log.info("\n开始第二阶段优化...")
        study2 = optuna.create_study(directions=['maximize', 'maximize'])
        study2.optimize(lambda trial: objective_stage2(trial, stage1_params, optimized_prices_dict, stock_list), n_trials=100)
        
        if len(study2.best_trials) == 0:
            log.error("第二阶段没有成功的优化试验")
            return
        
        # 选择第二阶段的最佳参数
        best_stage2 = max(study2.best_trials, key=lambda t: t.values[1])
        stage2_params = best_stage2.params
        
        if best_stage2.values[0] <= float('-inf') or best_stage2.values[1] <= float('-inf'):
            log.error("第二阶段优化未找到有效解")
            return
            
        # 输出第二阶段结果
        log.info(f"第二阶段最佳参数: {stage2_params}")
        log.info(f"第二阶段最佳年化收益: {best_stage2.values[0]:.3f}")
        log.info(f"第二阶段最佳夏普比率: {best_stage2.values[1]:.3f}")
        log.info(f"相比基准夏普比率提升: {(best_stage2.values[1] - benchmark_sharpe) / benchmark_sharpe * 100:.2f}%")
        log.info(f"相比基准年化收益提升: {(best_stage2.values[0] - benchmark_annual_return) / benchmark_annual_return * 100:.2f}%")
        
        # 合并两个阶段的参数
        final_params = stage1_params.copy()
        final_params.update(stage2_params)
        
        # 添加固定参数
        fixed_params = {
            'bias_period': 24,
            'adx_period': 14,
            'atr_period': 12,
            'bias_scale_factor': 1.0,
            'bias_threshold_dict': {
                '513100.XSHG': 0.1,
                '510300.XSHG': 0.1,
                '518880.XSHG': 0.1
            }
        }
        
        # 确保固定参数不覆盖已优化的参数
        for k, v in fixed_params.items():
            if k not in final_params:
                final_params[k] = v
        
        # 保存最终参数
        save_params(final_params, 'final_optimized_params.json')
        
        # 绘制帕累托前沿
        plot_pareto_front(study1, study2, benchmark_sharpe, benchmark_annual_return)
        
        # 输出优化结果
        log.info("\n优化完成！")
        log.info(f"最终参数已保存到 final_optimized_params.json")
        
    except Exception as e:
        log.error(f"主函数执行出错: {str(e)}")
        import traceback
        log.error(traceback.format_exc())

def plot_pareto_front(study1, study2, benchmark_sharpe, benchmark_annual_return):
    """绘制两个阶段的帕累托前沿"""
    plt.figure(figsize=(15, 10))
    
    # 绘制第一阶段的帕累托前沿
    x1 = [t.values[0] for t in study1.best_trials if t.values[0] != float('-inf')]
    y1 = [t.values[1] for t in study1.best_trials if t.values[1] != float('-inf')]
    if x1 and y1:
        plt.scatter(x1, y1, c='b', label='第一阶段帕累托前沿')
    
    # 绘制第二阶段的帕累托前沿
    x2 = [t.values[0] for t in study2.best_trials if t.values[0] != float('-inf')]
    y2 = [t.values[1] for t in study2.best_trials if t.values[1] != float('-inf')]
    if x2 and y2:
        plt.scatter(x2, y2, c='r', label='第二阶段帕累托前沿')
    
    # 绘制基准点
    plt.scatter([benchmark_annual_return], [benchmark_sharpe], c='g', marker='*', s=200, label='基准值')
    
    plt.xlabel('年化收益率', fontsize=14)
    plt.ylabel('夏普比率', fontsize=14)
    plt.title('两阶段优化帕累托前沿对比', fontsize=16)
    plt.legend()
    plt.grid(True, linestyle='--', alpha=0.7)
    plt.tight_layout()
    plt.savefig('two_stage_pareto_front.png', dpi=300, bbox_inches='tight')
    plt.close()

if __name__ == "__main__":
    main()