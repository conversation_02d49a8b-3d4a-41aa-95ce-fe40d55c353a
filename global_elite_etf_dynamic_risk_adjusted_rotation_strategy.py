# 导入所需的库
from jqdata import *
import numpy as np
import pandas as pd
import datetime
import math

# 初始化函数
def initialize(context):
    """初始化函数，设置基本参数和运行环境"""
    # 设置动态复权模式
    set_option('use_real_price', True)
    # 设置日志级别为 INFO
    log.set_level('strategy', 'info')

    # 设置基准
    set_benchmark('000300.XSHG')  # 沪深300作为基准

    # 设置滑点
    set_slippage(FixedSlippage(0.002))

    # 设置手续费
    set_order_cost(OrderCost(open_tax=0, close_tax=0.001, open_commission=0.0001, close_commission=0.0001, min_commission=0), type='stock')

    # 设置主要ETF池
    context.main_etfs = [
        '510300.XSHG',  # 沪深300ETF
        '513100.XSHG',  # 纳斯达克100ETF
        '513000.XSHG',  # 日经225ETF
        '513730.XSHG',  # 东南亚科技ETF
        '513080.XSHG',  # 法国CAC40ETF
        '513030.XSHG',  # 德国DAX ETF
        '518880.XSHG',  # 黄金ETF
    ]

    # 设置货币基金ETF
    context.money_fund_etf = '511880.XSHG'  # 银华日利ETF

    # 设置动量周期（交易日）
    context.momentum_periods_days = [21, 63, 126]  # 分别近似1个月、3个月、6个月的交易日

    # 设置动量权重
    context.momentum_weights = [0.4, 0.3, 0.3]  # 对应上述周期的动量权重

    # 设置波动率计算周期
    context.volatility_period_days = 60  # 波动率计算周期

    # 设置因子权重
    context.factor_weights = {'momentum': 0.8, 'volatility': 0.2}  # 最终综合评分中，动量和波动率的权重

    # 设置选择ETF数量
    context.top_n_etfs = 3  # 选择排名前N的ETF

    # 设置运行时需要的变量
    context.last_rebalance_date = None  # 上次再平衡时间

    # 设置每月第一个交易日执行调仓
    run_monthly(rebalance_portfolio, 1, time='open')

# 调仓函数
def rebalance_portfolio(context):
    """每月第一个交易日执行调仓逻辑"""
    log.info("开始执行月度调仓...")

    # 获取当前日期
    current_date = context.current_dt.date()

    # 检查是否已经在本月调仓过
    if context.last_rebalance_date is not None:
        if context.last_rebalance_date.year == current_date.year and context.last_rebalance_date.month == current_date.month:
            log.info("本月已经调仓，跳过")
            return

    # 计算各ETF的因子值
    etf_factors = calculate_etf_factors(context, context.main_etfs)

    # 如果没有有效的ETF因子数据，则全部资金买入货币基金
    if etf_factors.empty or len(etf_factors) == 0:
        log.info("没有有效的ETF因子数据，全部资金买入货币基金")
        # 全部资金买入货币基金
        cash = context.portfolio.cash
        order_value(context.money_fund_etf, cash)
        for etf in context.main_etfs:
            if context.portfolio.positions.get(etf, None):
                order_target(etf, 0)
        context.last_rebalance_date = current_date
        return

    # 打印因子值
    print_factor_values(etf_factors)

    # 根据最终评分排序，选择前N个ETF
    top_etfs = etf_factors.sort_values('final_score', ascending=False).head(context.top_n_etfs)

    # 计算总调整分数
    sum_adjusted_scores = top_etfs['adjusted_score'].sum()

    # 如果总调整分数小于等于0，则全部资金买入货币基金
    if sum_adjusted_scores <= 0:
        log.info("所有ETF的调整分数都为0或负数，全部资金买入货币基金")
        # 全部资金买入货币基金
        cash = context.portfolio.cash
        order_value(context.money_fund_etf, cash)
        for etf in context.main_etfs:
            if context.portfolio.positions.get(etf, None):
                order_target(etf, 0)
        context.last_rebalance_date = current_date
        return

    # 计算各ETF的权重
    weights = {}
    for idx, row in top_etfs.iterrows():
        weights[idx] = row['adjusted_score'] / sum_adjusted_scores

    # 执行调仓
    log.info("执行调仓操作:")

    # 先清仓所有不在top_etfs中的ETF
    for etf in context.main_etfs:
        if etf not in weights and context.portfolio.positions.get(etf, None):
            order_target(etf, 0)
            log.info(f"清仓 {etf}")

    # 清仓货币基金（如果持有）
    if context.portfolio.positions.get(context.money_fund_etf, None):
        order_target(context.money_fund_etf, 0)
        log.info(f"清仓货币基金 {context.money_fund_etf}")

    # 买入top_etfs
    portfolio_value = context.portfolio.total_value
    for etf, weight in weights.items():
        target_value = portfolio_value * weight
        order_value(etf, target_value)
        log.info(f"买入 {etf}, 目标权重: {weight:.2%}, 金额: {target_value:.2f}")

    # 更新最后调仓日期
    context.last_rebalance_date = current_date
    log.info("月度调仓完成")

def calculate_etf_factors(context, etf_list):
    """计算ETF的因子值"""
    # 创建一个空的DataFrame来存储因子值
    columns = ['momentum_score', 'volatility_score', 'momentum_rank', 'volatility_rank',
               'final_score', 'adjusted_score']
    etf_factors = pd.DataFrame(index=etf_list, columns=columns)

    # 遍历ETF列表，计算各因子值
    valid_etfs = []
    for etf in etf_list:
        # 检查ETF是否已上市
        security_info = get_security_info(etf)
        # 将context.current_dt转换为date类型进行比较
        current_date = context.current_dt.date()
        if security_info.start_date > current_date:
            log.info(f"{etf} 尚未上市，跳过")
            continue

        # 计算动量因子
        momentum_score = get_momentum_score(etf, context.momentum_periods_days, context.momentum_weights)

        # 如果动量因子无效（数据不足），则跳过该ETF
        if np.isnan(momentum_score):
            log.info(f"{etf} 动量因子计算无效（数据不足），跳过")
            continue

        # 计算波动率因子
        volatility = get_volatility(etf, context.volatility_period_days)

        # 如果波动率因子无效，则跳过该ETF
        if np.isnan(volatility):
            log.info(f"{etf} 波动率因子计算无效（数据不足），跳过")
            continue

        # 存储原始因子值
        etf_factors.loc[etf, 'momentum_score'] = momentum_score
        etf_factors.loc[etf, 'volatility_score'] = volatility

        # 添加到有效ETF列表
        valid_etfs.append(etf)

    # 如果没有有效的ETF，则返回空DataFrame
    if len(valid_etfs) == 0:
        return pd.DataFrame()

    # 只保留有效的ETF
    etf_factors = etf_factors.loc[valid_etfs]

    # 使用排名法替代Z-score
    # 对动量进行排名（越高越好）
    etf_factors['momentum_rank'] = etf_factors['momentum_score'].rank(pct=True)

    # 对波动率进行排名（越低越好，所以用1减去排名）
    etf_factors['volatility_rank'] = 1 - etf_factors['volatility_score'].rank(pct=True)

    # 计算最终评分：加权组合动量排名和波动率排名
    etf_factors['final_score'] = (
        context.factor_weights['momentum'] * etf_factors['momentum_rank'] +
        context.factor_weights['volatility'] * etf_factors['volatility_rank']
    )

    # 打印排名信息
    log.info("ETF排名情况:")
    for etf, row in etf_factors.iterrows():
        log.info(f"{etf} - 动量排名: {row['momentum_rank']:.4f}, 波动率排名: {row['volatility_rank']:.4f}, 最终评分: {row['final_score']:.4f}")

    # 计算调整后的分数（非负）
    etf_factors['adjusted_score'] = etf_factors['final_score'].apply(lambda x: max(0, x))

    return etf_factors

def get_momentum_score(etf, periods, weights):
    """计算单个ETF的动量分数"""
    # 获取最长周期的历史数据
    max_period = max(periods)

    try:
        # 获取历史价格数据
        price_data = get_price(etf, count=max_period+1, frequency='daily', fields=['close'])
        close_prices = price_data['close']

        # 如果数据长度不足最短周期，则返回NaN
        if len(close_prices) < min(periods) + 1:
            log.info(f"{etf} 数据长度不足最短周期 {min(periods)} 天，无法计算动量")
            return np.nan

        # 计算各周期动量
        momentum_values = []
        valid_weights = []

        for i, period in enumerate(periods):
            if len(close_prices) >= period + 1:
                # 计算动量：(当前价格 / N日前价格) - 1
                momentum = (close_prices.iloc[-1] / close_prices.iloc[-period-1]) - 1
                momentum_values.append(momentum)
                valid_weights.append(weights[i])
            else:
                log.info(f"{etf} 数据长度不足 {period} 天，跳过该周期动量计算")

        # 如果没有有效的动量值，则返回NaN
        if len(momentum_values) == 0:
            return np.nan

        # 归一化权重
        valid_weights = [w/sum(valid_weights) for w in valid_weights]

        # 计算加权动量分数
        momentum_score = sum(m * w for m, w in zip(momentum_values, valid_weights))

        return momentum_score

    except Exception as e:
        log.error(f"计算 {etf} 动量分数时出错: {str(e)}")
        return np.nan

def get_volatility(etf, period):
    """计算单个ETF的波动率"""
    try:
        # 获取历史价格数据
        price_data = get_price(etf, count=period+1, frequency='daily', fields=['close'])
        close_prices = price_data['close']

        # 如果数据长度不足，则返回NaN
        if len(close_prices) < period:
            log.info(f"{etf} 数据长度不足 {period} 天，无法计算波动率")
            return np.nan

        # 计算日收益率
        returns = close_prices.pct_change().dropna()

        # 计算波动率（标准差）
        volatility = returns.std()

        return volatility

    except Exception as e:
        log.error(f"计算 {etf} 波动率时出错: {str(e)}")
        return np.nan

# 注意：由于使用了排名法替代Z-score，此函数已不再使用

def print_factor_values(etf_factors):
    """打印ETF因子值，用于调试"""
    log.info("ETF因子值:")
    for etf in etf_factors.index:
        log.info(f"  {etf}:")
        log.info(f"    动量分数: {etf_factors.loc[etf, 'momentum_score']:.4f}")
        log.info(f"    波动率: {etf_factors.loc[etf, 'volatility_score']:.4f}")
        log.info(f"    动量排名: {etf_factors.loc[etf, 'momentum_rank']:.4f}")
        log.info(f"    波动率排名: {etf_factors.loc[etf, 'volatility_rank']:.4f}")
        log.info(f"    最终评分: {etf_factors.loc[etf, 'final_score']:.4f}")
        log.info(f"    调整后评分: {etf_factors.loc[etf, 'adjusted_score']:.4f}")

def handle_data(context, data):
    """
    每个交易日运行，用于处理交易数据
    聚宽要求必须实现此函数，但本策略主要依靠run_monthly进行调仓，此处仅作为占位符
    """
    # 每周一输出当前持仓状态
    if context.current_dt.weekday() == 0 and context.current_dt.hour < 10:
        log.info("当前持仓状态:")
        total_value = context.portfolio.total_value
        for security in context.portfolio.positions:
            position = context.portfolio.positions[security]
            if position.total_amount > 0:
                value_percent = position.value / total_value * 100
                log.info(f"  {security}: 持仓 {position.total_amount} 股, 市值 {position.value:.2f}, 占比 {value_percent:.2f}%")
