import backtrader as bt
import datetime
import numpy as np
import pandas as pd
from collections import defaultdict

class M1MomentumIndicator(bt.Indicator):
    """M1动量指标"""
    lines = ('momentum',)
    params = (('lookback', 21),)
    
    def __init__(self):
        self.lines.momentum = (self.data.close[0] / self.data.close[-self.p.lookback]) - 1

class JQStrategyBT(bt.Strategy):
    """
    基于聚宽策略转换的Backtrader策略
    """
    params = (
        # 技术指标参数
        ('bias_period', 24),
        ('adx_period', 14),
        ('atr_period', 12),
        
        # 动量策略参数
        ('momentum_boost_factor', 0.5),
        ('max_momentum_allocation_pct', 0.176),
        ('momentum_threshold', 0.017),
        ('momentum_M1_lookback', 21),
        
        # 仓位参数
        ('total_position_pct', 0.862),
        ('reserve_position_pct', 0.064),
        
        # BIAS阈值 (按标的设置)
        ('bias_threshold', {
            '513100.XSHG': 0.1,
            '510300.XSHG': 0.1,
            '518880.XSHG': 0.1
        }),
        
        # 风控参数
        ('single_stock_stop_loss', 0.033),
        ('portfolio_stop_loss', 0.081),
        
        # 因子权重
        ('weight_trend', 0.285),
        ('weight_direction', 0.357),
        ('weight_volatility', 0.074),
        ('weight_volume', 0.214),
        ('weight_bias', 0.07),
        
        # 趋势调整参数
        ('uptrend_base_extra', 0.054),
        ('downtrend_base_reduction', 0.129),
        ('downtrend_pct_limit', 0.756),
        
        # 调整系数
        ('reserve_scale_down_factor', 0.4),
        ('reserve_scale_up_factor', 1.35),
        ('strong_trend_boost', 1.49),
        ('oversold_reduction_factor', 0.85),
        ('momentum_strength_factor_multiplier', 1.04),
        
        # 其他参数
        ('silent_mode', False),
        ('adx_trend_threshold', 35),
    )
    
    def __init__(self):
        # 创建数据源字典
        self.dnames = {d._name: d for d in self.datas}
        
        # 初始化技术指标
        self.initialize_indicators()
        
        # 初始化状态变量
        self.market_states = {d._name: 'oscillating' for d in self.datas}
        self.last_rebalance_date = None
        self.last_momentum_check = None
        self.stop_trading = False
        self.last_stop_date = None
        self.initial_position_established = False
        
        # 初始化缓存
        self.volatility_cache = {}
        self.trend_cache = {}
        
        # 转换bias_threshold为字典
        self.bias_threshold_map = self.p.bias_threshold
        
    def initialize_indicators(self):
        """初始化所有技术指标"""
        for d in self.datas:
            # 计算移动平均线
            self.ma20 = bt.indicators.SimpleMovingAverage(d.close, period=20)
            self.ma60 = bt.indicators.SimpleMovingAverage(d.close, period=60)
            
            # 计算BIAS
            self.bias = (d.close - self.ma20) / self.ma20
            
            # 计算ADX相关指标
            self.adx = bt.indicators.DirectionalMovement(d, period=self.p.adx_period)
            
            # 计算ATR
            self.atr = bt.indicators.ATR(d, period=self.p.atr_period)
            
            # 计算成交量指标
            self.volume_ma5 = bt.indicators.SimpleMovingAverage(d.volume, period=5)
            self.volume_ma20 = bt.indicators.SimpleMovingAverage(d.volume, period=20)
            
            # 计算M1动量
            self.momentum_m1 = M1MomentumIndicator(d, lookback=self.p.momentum_M1_lookback)
            
            # 计算波动率
            self.volatility = bt.indicators.StandardDeviation(d.close.pct_change(), period=20)
            
            # 存储指标到实例变量
            setattr(self, f'ma20_{d._name}', self.ma20)
            setattr(self, f'ma60_{d._name}', self.ma60)
            setattr(self, f'bias_{d._name}', self.bias)
            setattr(self, f'adx_{d._name}', self.adx)
            setattr(self, f'atr_{d._name}', self.atr)
            setattr(self, f'volume_ma5_{d._name}', self.volume_ma5)
            setattr(self, f'volume_ma20_{d._name}', self.volume_ma20)
            setattr(self, f'momentum_m1_{d._name}', self.momentum_m1)
            setattr(self, f'volatility_{d._name}', self.volatility)
    
    def log(self, txt, dt=None):
        """记录日志"""
        if not self.p.silent_mode:
            dt = dt or self.datetime.date(0)
            print(f'{dt.isoformat()}: {txt}')
    
    def next(self):
        """策略主逻辑"""
        current_date = self.datetime.date(0)
        
        # 止损恢复逻辑
        if self.stop_trading and current_date > self.last_stop_date:
            self.stop_trading = False
            self.log('恢复交易')
        
        if self.stop_trading:
            return
        
        # 更新市场状态
        for d in self.datas:
            self.market_states[d._name] = self.get_market_state(d)
        
        # 初始建仓
        if not self.initial_position_established:
            self.execute_initial_buy()
            self.initial_position_established = True
        
        # 止损检查
        if self.check_stop_loss():
            return
        
        # 周度动量调整
        if (current_date.weekday() == 0 and 
            current_date != self.last_momentum_check and 
            current_date != self.last_rebalance_date):
            self.apply_momentum_overlay()
            self.last_momentum_check = current_date
        
        # 月度再平衡
        if self.is_last_trading_day_of_month():
            self.rebalance_portfolio()
            self.last_rebalance_date = current_date
    
    def get_market_state(self, data):
        """判断市场状态"""
        bias = getattr(self, f'bias_{data._name}')[0]
        adx = getattr(self, f'adx_{data._name}')[0]
        plus_di = getattr(self, f'adx_{data._name}').plusDI[0]
        minus_di = getattr(self, f'adx_{data._name}').minusDI[0]
        
        bias_threshold = self.bias_threshold_map[data._name]
        
        if adx < self.p.adx_trend_threshold or abs(bias) < bias_threshold * 1.157:
            return 'oscillating'
        
        if adx > self.p.adx_trend_threshold:
            if plus_di > minus_di:
                if bias > bias_threshold:
                    return 'oscillating'
                return 'uptrend'
            else:
                if bias < -bias_threshold:
                    return 'oscillating'
                return 'downtrend'
        else:
            if abs(bias) > bias_threshold:
                if bias > bias_threshold:
                    return 'downtrend'
                else:
                    return 'uptrend'
            return 'oscillating'
    
    def calculate_risk_parity_weights(self):
        """计算风险平价权重"""
        volatilities = {}
        for d in self.datas:
            try:
                volatility = getattr(self, f'volatility_{d._name}')[0]
                volatilities[d._name] = volatility
            except Exception as e:
                self.log(f"计算 {d._name} 波动率时出错: {str(e)}")
                volatilities[d._name] = 0.02
        
        inv_vol_sum = sum(1.0 / vol for vol in volatilities.values())
        weights = {d._name: (1.0 / vol) / inv_vol_sum for d, vol in volatilities.items()}
        
        # 权重归一化
        total_pct = 100
        int_weights = {}
        remaining = total_pct
        
        for d_name, weight in weights.items():
            int_weight = int(weight * 100)
            int_weights[d_name] = int_weight
            remaining -= int_weight
        
        if remaining > 0:
            sorted_securities = sorted(weights.keys(), key=lambda x: weights[x], reverse=True)
            for i in range(remaining):
                security = sorted_securities[i % len(sorted_securities)]
                int_weights[security] += 1
        
        rounded_weights = {d_name: weight / 100.0 for d_name, weight in int_weights.items()}
        
        if not self.p.silent_mode:
            weight_str = ', '.join([f"{d_name}: {weight:.0%}" for d_name, weight in rounded_weights.items()])
            self.log(f"风险平价权重计算结果: {weight_str}")
        
        return rounded_weights
    
    def calculate_dynamic_allocation_factors(self, data):
        """计算动态配置调整因子"""
        try:
            # 获取指标值
            trend_strength = getattr(self, f'adx_{data._name}')[0]
            volatility = getattr(self, f'volatility_{data._name}')[0]
            volume_ratio = data.volume[0] / getattr(self, f'volume_ma5_{data._name}')[0]
            bias = getattr(self, f'bias_{data._name}')[0]
            adx = getattr(self, f'adx_{data._name}')[0]
            plus_di = getattr(self, f'adx_{data._name}').plusDI[0]
            minus_di = getattr(self, f'adx_{data._name}').minusDI[0]
            
            # 计算趋势强度因子
            trend_factor = min(2.0, max(0.5, trend_strength / 20))
            
            # 计算趋势方向因子
            direction_factor = 1.0
            if plus_di > minus_di:
                direction_factor = min(1.5, max(1.0, 1 + (plus_di - minus_di) / 100))
            else:
                direction_factor = min(1.0, max(0.8, 1 - (minus_di - plus_di) / 100))
            
            # 计算波动率因子
            vol_base = 0.02
            if volatility < vol_base:
                volatility_factor = min(1.5, vol_base / volatility)
            else:
                volatility_factor = max(0.6, vol_base / volatility)
            
            # 计算成交量因子
            if volume_ratio > 1:
                volume_factor = min(1.8, 1 + (volume_ratio - 1) * 0.5)
            else:
                volume_factor = max(0.7, 1 - (1 - volume_ratio) * 0.5)
            
            # 计算BIAS影响因子
            bias_threshold = self.bias_threshold_map[data._name]
            if abs(bias) < bias_threshold * 0.5:
                bias_factor = 1.6 - abs(bias) / bias_threshold
            else:
                bias_factor = max(0.6, 1 - abs(bias) / bias_threshold)
            
            # 计算市场趋势持续性
            trend_persistence = min(1.5, adx / 30)
            
            # 综合计算调整系数
            adjustment_factor = (
                trend_factor * self.p.weight_trend +
                direction_factor * self.p.weight_direction +
                volatility_factor * self.p.weight_volatility +
                volume_factor * self.p.weight_volume +
                bias_factor * self.p.weight_bias
            ) * trend_persistence
            
            # 计算置信度
            confidence = min(1.0, (
                (trend_strength / 40) * 0.4 +
                (1 - abs(bias) / (bias_threshold * 2)) * 0.3 +
                (adx / 40) * 0.3 +
                (volume_ratio - 0.5) * 0.2
            ))
            
            # 根据市场状态调整
            market_state = self.market_states[data._name]
            if market_state == 'uptrend':
                if confidence > 0.7 and trend_factor > 1.2:
                    adjustment_factor *= self.p.strong_trend_boost
            elif market_state == 'downtrend':
                if confidence > 0.6 and bias_factor > 1.2:
                    adjustment_factor *= self.p.oversold_reduction_factor
            
            # 确保最终调整系数在合理范围内
            adjustment_factor = min(2.5, max(0.5, adjustment_factor))
            
            if not self.p.silent_mode and (adjustment_factor > 1.5 or adjustment_factor < 0.7):
                self.log(f"{data._name} 调整因子详情:")
                self.log(f"趋势因子: {trend_factor:.2f}, 方向因子: {direction_factor:.2f}")
                self.log(f"波动率因子: {volatility_factor:.2f}, 成交量因子: {volume_factor:.2f}")
                self.log(f"BIAS因子: {bias_factor:.2f}, 趋势持续性: {trend_persistence:.2f}")
            
            return adjustment_factor, confidence
            
        except Exception as e:
            self.log(f"计算动态配置因子时出错: {str(e)}")
            return 1.0, 0.5
    
    def calculate_dynamic_stop_loss(self, data):
        """计算动态止损线"""
        try:
            market_state = self.market_states[data._name]
            volatility = getattr(self, f'volatility_{data._name}')[0]
            
            base_stop_loss = self.p.single_stock_stop_loss
            
            if market_state == 'uptrend':
                stop_loss_factor = 1.2
            elif market_state == 'downtrend':
                stop_loss_factor = 0.8
            else:
                stop_loss_factor = 1.0
            
            volatility_factor = 1.0 + (volatility - 0.02) * 2
            volatility_factor = max(0.8, min(1.5, volatility_factor))
            
            final_stop_loss = base_stop_loss * stop_loss_factor * volatility_factor
            
            return max(0.03, min(0.08, final_stop_loss))
            
        except Exception as e:
            self.log(f"计算动态止损线时出错: {str(e)}")
            return self.p.single_stop_loss
    
    def check_stop_loss(self):
        """检查止损条件"""
        for d in self.datas:
            returns = (d.close[0] / d.close[-1]) - 1
            stop_loss_line = self.calculate_dynamic_stop_loss(d)
            
            if returns <= -stop_loss_line:
                self.log(f"触发止损 - {d._name}: 收益率={returns:.2%}, 止损线={stop_loss_line:.2%}")
                self.order_target_percent(data=d, target=0.0)
                self.stop_trading = True
                self.last_stop_date = self.datetime.date(0)
                return True
        
        # 计算组合收益率
        portfolio_value = self.broker.getvalue()
        portfolio_returns = (portfolio_value / self.broker.startingcash) - 1
        
        if portfolio_returns <= -self.p.portfolio_stop_loss:
            self.log(f"触发组合止损: 收益率={portfolio_returns:.2%}")
            for d in self.datas:
                self.order_target_percent(data=d, target=0.0)
            self.stop_trading = True
            self.last_stop_date = self.datetime.date(0)
            return True
        
        return False
    
    def apply_momentum_overlay(self):
        """应用动量叠加策略"""
        try:
            # 获取基础风险平价权重
            base_weights = self.calculate_risk_parity_weights()
            
            # 计算可用备用金
            total_value = self.broker.getvalue()
            current_positions = sum(self.getposition(d).size * d.close[0] for d in self.datas)
            available_cash = total_value - current_positions
            actual_reserve_pct = available_cash / total_value if total_value > 0 else 0
            
            # 设置最大可用备用金
            max_total_boost = min(actual_reserve_pct / 2, self.p.max_momentum_allocation_pct)
            
            # 识别动量资产
            momentum_assets = []
            for d in self.datas:
                try:
                    momentum_strength = getattr(self, f'momentum_m1_{d._name}')[0]
                    if momentum_strength > self.p.momentum_threshold:
                        momentum_assets.append((d, momentum_strength))
                        self.log(f"{d._name} 满足M1动量条件: 价格={d.close[0]:.2f}, M1动量={momentum_strength:.2%}")
                except Exception as e:
                    self.log(f"处理{d._name}的动量判断时出错: {str(e)}")
                    continue
            
            # 按动量强度排序
            momentum_assets.sort(key=lambda x: x[1], reverse=True)
            
            # 初始化最终权重
            final_weights = base_weights.copy()
            total_boost_weight = 0.0
            
            # 计算动量调整
            for d, momentum_strength in momentum_assets:
                base_weight = float(base_weights[d._name])
                boost = base_weight * (self.p.momentum_boost_factor - 1)
                
                strength_factor = min(1.5, 1 + momentum_strength * 2)
                adjusted_boost = boost * strength_factor
                
                if total_boost_weight + adjusted_boost <= max_total_boost:
                    final_weights[d._name] = base_weight + adjusted_boost
                    total_boost_weight += adjusted_boost
                    self.log(f"动量增强 {d._name}: 基础权重 {base_weight:.1%} -> {final_weights[d._name]:.1%}, M1动量 {momentum_strength:.2%}")
                else:
                    remaining_boost = max_total_boost - total_boost_weight
                    if remaining_boost > 0:
                        scale = remaining_boost / adjusted_boost
                        final_weights[d._name] = base_weight + (adjusted_boost * scale)
                        total_boost_weight = max_total_boost
                        self.log(f"动量增强 {d._name}: 基础权重 {base_weight:.1%} -> {final_weights[d._name]:.1%}, M1动量 {momentum_strength:.2%} (已缩减)")
                    break
            
            # 执行调仓
            for d in self.datas:
                target_percent = final_weights[d._name]
                self.order_target_percent(data=d, target=target_percent)
                self.log(f"动量调整 {d._name}: 目标权重 {target_percent:.1%}")
            
        except Exception as e:
            self.log(f"应用动量叠加策略时出错: {str(e)}")
    
    def rebalance_portfolio(self):
        """执行月度再平衡"""
        try:
            # 计算风险平价权重
            weights = self.calculate_risk_parity_weights()
            
            # 记录原始备用资金比例
            original_reserve_pct = self.p.reserve_position_pct
            current_reserve_pct = original_reserve_pct
            
            # 分析整体市场状况
            up_count = sum(1 for d in self.datas if self.market_states[d._name] == 'uptrend')
            down_count = sum(1 for d in self.datas if self.market_states[d._name] == 'downtrend')
            
            # 根据整体市场状况调整备用资金
            if up_count == len(self.datas):
                current_reserve_pct = max(0.1, original_reserve_pct * self.p.reserve_scale_down_factor)
                self.log(f'全面上涨行情，备用资金调整为{current_reserve_pct:.1%}')
            elif down_count == len(self.datas):
                current_reserve_pct = min(0.5, original_reserve_pct * self.p.reserve_scale_up_factor)
                self.log(f'全面下跌行情，备用资金调整为{current_reserve_pct:.1%}')
            
            # 动态调整各个标的的配置
            total_adjustment = 0
            adjustments = {}
            
            # 计算理论调整幅度
            for d in self.datas:
                try:
                    market_state = self.market_states[d._name]
                    adjustment_factor, confidence = self.calculate_dynamic_allocation_factors(d)
                    
                    if market_state == 'uptrend':
                        base_extra = min(self.p.uptrend_base_extra, current_reserve_pct)
                        extra_weight = base_extra * adjustment_factor * confidence
                        
                        if getattr(self, f'adx_{d._name}')[0] > 30:
                            extra_weight *= self.p.strong_trend_boost
                        
                        adjustments[d._name] = extra_weight
                        total_adjustment += extra_weight
                        
                    elif market_state == 'downtrend':
                        base_reduction = min(self.p.downtrend_base_reduction, weights[d._name] * self.p.downtrend_pct_limit)
                        reduction = base_reduction * adjustment_factor * confidence
                        
                        if getattr(self, f'bias_{d._name}')[0] < -self.bias_threshold_map[d._name]:
                            reduction *= self.p.oversold_reduction_factor
                        
                        adjustments[d._name] = -reduction
                        total_adjustment -= reduction
                    
                except Exception as e:
                    self.log(f"计算{d._name}配置调整时出错: {str(e)}")
                    adjustments[d._name] = 0
            
            # 实际调整权重
            available_reserve = current_reserve_pct - 0.05
            if total_adjustment > 0:
                scale_factor = min(1.0, available_reserve / total_adjustment)
                for d in self.datas:
                    if d._name in adjustments:
                        actual_adjustment = adjustments[d._name] * scale_factor
                        weights[d._name] += actual_adjustment
                        current_reserve_pct -= actual_adjustment
                        
                        if abs(actual_adjustment) >= 0.001:
                            self.log(f"{d._name} {'超配' if actual_adjustment > 0 else '减配'} {abs(actual_adjustment):.1%}")
            
            # 确保权重非负且总和不超过1
            weights = {d_name: max(0.0, w) for d_name, w in weights.items()}
            weight_sum = sum(float(w) for w in weights.values())
            
            if weight_sum > 1:
                scale = (1 - current_reserve_pct) / weight_sum
                weights = {d_name: w * scale for d_name, w in weights.items()}
                self.log(f"权重总和超过1，进行等比例缩减，缩减系数: {scale:.3f}")
            
            # 执行调仓
            for d in self.datas:
                target_percent = weights[d._name]
                self.order_target_percent(data=d, target=target_percent)
                self.log(f"再平衡 {d._name}: 目标权重 {target_percent:.1%}")
            
        except Exception as e:
            self.log(f"执行再平衡时发生错误: {str(e)}")
    
    def execute_initial_buy(self):
        """执行初始建仓"""
        try:
            # 计算风险平价权重
            weights = self.calculate_risk_parity_weights()
            
            # 记录原始备用资金比例
            original_reserve_pct = self.p.reserve_position_pct
            current_reserve_pct = original_reserve_pct
            
            # 分析整体市场状况
            up_count = sum(1 for d in self.datas if self.market_states[d._name] == 'uptrend')
            down_count = sum(1 for d in self.datas if self.market_states[d._name] == 'downtrend')
            
            # 根据整体市场状况调整备用资金
            if up_count == len(self.datas):
                current_reserve_pct = max(0.1, original_reserve_pct * self.p.reserve_scale_down_factor)
                self.log(f'初始建仓：全面上涨行情，备用资金调整为{current_reserve_pct:.1%}')
            elif down_count == len(self.datas):
                current_reserve_pct = min(0.5, original_reserve_pct * self.p.reserve_scale_up_factor)
                self.log(f'初始建仓：全面下跌行情，备用资金调整为{current_reserve_pct:.1%}')
            
            # 动态调整各个标的的配置
            total_adjustment = 0
            adjustments = {}
            
            # 计算理论调整幅度
            for d in self.datas:
                try:
                    market_state = self.market_states[d._name]
                    adjustment_factor, confidence = self.calculate_dynamic_allocation_factors(d)
                    
                    if market_state == 'uptrend':
                        base_extra = min(0.12, current_reserve_pct)
                        extra_weight = base_extra * adjustment_factor * confidence
                        
                        if getattr(self, f'adx_{d._name}')[0] > 30:
                            extra_weight *= self.p.strong_trend_boost
                        
                        adjustments[d._name] = extra_weight
                        total_adjustment += extra_weight
                        
                    elif market_state == 'downtrend':
                        base_reduction = min(0.08, weights[d._name] * 0.6)
                        reduction = base_reduction * adjustment_factor * confidence
                        
                        if getattr(self, f'bias_{d._name}')[0] < -self.bias_threshold_map[d._name]:
                            reduction *= self.p.oversold_reduction_factor
                        
                        adjustments[d._name] = -reduction
                        total_adjustment -= reduction
                    
                except Exception as e:
                    self.log(f"计算{d._name}配置调整时出错: {str(e)}")
                    adjustments[d._name] = 0
            
            # 实际调整权重
            available_reserve = current_reserve_pct - 0.05
            if total_adjustment > 0:
                scale_factor = min(1.0, available_reserve / total_adjustment)
                for d in self.datas:
                    if d._name in adjustments:
                        actual_adjustment = adjustments[d._name] * scale_factor
                        weights[d._name] += actual_adjustment
                        current_reserve_pct -= actual_adjustment
                        
                        if abs(actual_adjustment) >= 0.001:
                            self.log(f"初始建仓 {d._name} {'超配' if actual_adjustment > 0 else '减配'} {abs(actual_adjustment):.1%}")
            
            # 确保权重非负且总和不超过1
            weights = {d_name: max(0.0, w) for d_name, w in weights.items()}
            weight_sum = sum(float(w) for w in weights.values())
            
            if weight_sum > 1:
                scale = (1 - current_reserve_pct) / weight_sum
                weights = {d_name: w * scale for d_name, w in weights.items()}
                self.log(f"初始建仓：权重总和超过1，进行等比例缩减，缩减系数: {scale:.3f}")
            
            # 执行建仓
            for d in self.datas:
                target_percent = weights[d._name]
                self.order_target_percent(data=d, target=target_percent)
                self.log(f"初始建仓 {d._name}: 目标权重 {target_percent:.1%}")
            
        except Exception as e:
            self.log(f"执行初始建仓时发生错误: {str(e)}")
    
    def is_last_trading_day_of_month(self):
        """判断是否为月末最后一个交易日"""
        current_date = self.datetime.date(0)
        next_date = self.datetime.date(1)
        return current_date.month != next_date.month
    
    def notify_order(self, order):
        """订单状态通知"""
        if order.status in [order.Submitted, order.Accepted]:
            return
        
        if order.status in [order.Completed]:
            if order.isbuy():
                self.log(f'买入执行: {order.data._name}, 价格: {order.executed.price:.2f}, 数量: {order.executed.size}')
            else:
                self.log(f'卖出执行: {order.data._name}, 价格: {order.executed.price:.2f}, 数量: {order.executed.size}')
        
        elif order.status in [order.Canceled, order.Margin, order.Rejected]:
            self.log(f'订单取消/拒绝: {order.data._name}')
    
    def notify_trade(self, trade):
        """交易通知"""
        if trade.isclosed:
            self.log(f'交易完成: {trade.data._name}, 毛利润: {trade.pnl:.2f}, 净利润: {trade.pnlcomm:.2f}')
    
    def stop(self):
        """策略结束"""
        self.log('策略结束')
        self.log(f'最终组合价值: {self.broker.getvalue():.2f}')
        self.log(f'总收益率: {(self.broker.getvalue() / self.broker.startingcash - 1):.2%}') 