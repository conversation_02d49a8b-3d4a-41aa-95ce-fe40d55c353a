# 现金管理修改说明 - 从货币ETF到国债逆回购

## 修改概述

根据用户要求，将策略中的现金管理方式从买入货币ETF改为每天自动进行国债逆回购，参考Ptrade API官方案例实现。

## 主要修改内容

### 1. 参数配置修改

**修改前（货币ETF）：**
```python
# 添加货币基金相关参数
if not hasattr(g, 'money_fund'):
    g.money_fund = '511990.XSHG'  # 华宝添益货币ETF
if not hasattr(g, 'min_fund_amount'):
    g.min_fund_amount = 1  # 最小货基交易金额（1元）
if not hasattr(g, 'money_fund_threshold'):
    g.money_fund_threshold = 0.01  # 货基交易阈值（1%）
```

**修改后（国债逆回购）：**
```python
# 添加国债逆回购相关参数
if not hasattr(g, 'reverse_repo_codes'):
    # 国债逆回购代码：上海1天、深圳1天
    g.reverse_repo_codes = {
        'SH_1D': '204001.XSHG',  # 上海1天期国债逆回购
        'SZ_1D': '131810.XSHE'   # 深圳1天期国债逆回购
    }
if not hasattr(g, 'min_reverse_repo_amount'):
    g.min_reverse_repo_amount = 1000  # 最小逆回购金额（1000元）
if not hasattr(g, 'reverse_repo_reserve'):
    g.reverse_repo_reserve = 1010  # 保留资金（用于新债中签等）
```

### 2. 定时任务修改

**修改前：**
```python
run_daily(context, trade_money_fund, time='9:32')
run_daily(context, check_fund_income, time='15:10')
```

**修改后：**
```python
run_daily(context, reverse_repurchase, time='15:10')  # 盘后进行国债逆回购
```

### 3. 核心函数替换

#### 3.1 删除货币基金交易函数
- 删除 `trade_money_fund(context)` 函数
- 删除 `check_fund_income(context)` 函数

#### 3.2 新增国债逆回购函数
```python
def reverse_repurchase(context):
    """
    盘后国债逆回购
    
    根据Ptrade API官方案例，盘后进行逆回购，只能使用run_daily设定在3点之后执行
    参考官方案例：保留1000元用于新债中签等，其余资金进行逆回购
    """
    try:
        # 获取当前可用现金
        cash = context.portfolio.cash
        
        # 保留资金，用于新债中签等
        cash = cash - g.reverse_repo_reserve
        
        # 检查是否有足够资金进行逆回购
        if cash < g.min_reverse_repo_amount:
            log.info(f"可用资金不足，跳过逆回购: 可用资金={cash:.2f}, 最小金额={g.min_reverse_repo_amount}")
            return
        
        # 计算逆回购数量（以1000元为单位，对应10手）
        amount = int(cash / 1000) * 10
        
        if amount <= 0:
            log.info(f"计算逆回购数量为0，跳过逆回购: 可用资金={cash:.2f}")
            return
        
        # 选择逆回购品种（优先选择上海1天期）
        reverse_repo_code = g.reverse_repo_codes['SH_1D']  # 默认使用上海1天期
        
        # 检查上海逆回购是否可用，如果不可用则使用深圳
        try:
            snapshot = get_snapshot(reverse_repo_code)
            if not snapshot or reverse_repo_code not in snapshot or snapshot[reverse_repo_code]['trade_status'] == 'HALT':
                # 上海逆回购不可用，尝试深圳
                reverse_repo_code = g.reverse_repo_codes['SZ_1D']
                log.info("上海逆回购不可用，切换到深圳逆回购")
        except:
            # 获取快照失败，使用深圳逆回购
            reverse_repo_code = g.reverse_repo_codes['SZ_1D']
            log.info("获取上海逆回购快照失败，切换到深圳逆回购")
        
        # 执行逆回购（卖出操作）
        # 根据官方案例，逆回购是卖出操作，所以使用负数
        order(reverse_repo_code, -amount)
        
        log.info(f"执行国债逆回购: 代码={reverse_repo_code}, 数量={amount}手, 金额={amount*100:.2f}元")
        
    except Exception as e:
        log.error(f"执行国债逆回购时发生错误: {str(e)}")
```

### 4. 策略逻辑修改

#### 4.1 备用资金计算
**修改前：**
```python
# 计算货基市值
money_fund_value = 0
if g.money_fund in context.portfolio.positions:
    position = context.portfolio.positions[g.money_fund]
    if position and position.amount > 0:
        snapshot = get_snapshot(g.money_fund)
        if snapshot and snapshot[g.money_fund]['trade_status'] != 'HALT':
            money_fund_value = position.amount * snapshot[g.money_fund]['last_px']

# 计算实际可用的备用金总额
actual_reserve_value = available_cash + money_fund_value
```

**修改后：**
```python
# 计算实际可用的备用金总额（不再考虑货币基金，改为国债逆回购）
actual_reserve_value = available_cash
```

#### 4.2 再平衡时的处理
**修改前：**
```python
# 处理货基持仓
if g.money_fund in context.portfolio.positions:
    position = context.portfolio.positions[g.money_fund]
    if position and position.amount > 0:
        order_target(g.money_fund, 0)
        log.info(f"季度再平衡: 清空货基持仓 {position.amount}份")
```

**修改后：**
```python
# 不再处理货币基金持仓，改为国债逆回购
```

## 技术要点

### 1. 国债逆回购特点
- **交易时间**：只能在盘后（15:10）执行
- **交易方式**：卖出操作（使用负数数量）
- **最小单位**：1000元对应10手
- **到期方式**：T+1自动到期，无需手动操作

### 2. 品种选择策略
- **优先级**：上海1天期 > 深圳1天期
- **自动切换**：上海不可用时自动切换到深圳
- **状态检查**：通过get_snapshot检查交易状态

### 3. 资金管理
- **保留资金**：1010元（用于新债中签等）
- **最小金额**：1000元（低于此金额不进行逆回购）
- **计算方式**：int(cash / 1000) * 10

## 优势对比

### 国债逆回购 vs 货币ETF

| 特性 | 国债逆回购 | 货币ETF |
|------|------------|---------|
| **收益率** | 通常更高，特别是月末、季末 | 相对稳定但较低 |
| **流动性** | T+1到期，流动性好 | T+1交易，需要卖出 |
| **管理复杂度** | 简单，自动到期 | 需要持续管理持仓 |
| **资金效率** | 100%资金利用 | 需要保留交易资金 |
| **交易成本** | 极低 | 有申赎费用 |
| **风险** | 几乎无风险 | 净值波动风险 |

## 实施效果

### 1. 简化策略逻辑
- 删除货币基金相关的复杂持仓管理
- 减少日内交易频次
- 降低策略维护成本

### 2. 提高资金效率
- 每日自动进行逆回购，无闲置资金
- T+1到期，资金周转效率高
- 无需预留交易保证金

### 3. 增强收益稳定性
- 国债逆回购收益相对稳定
- 特殊时点（月末、季末）收益更高
- 无净值波动风险

## 注意事项

1. **交易时间限制**：必须在15:10之后执行
2. **最小金额要求**：低于1000元不进行逆回购
3. **品种可用性**：需要检查交易状态，做好备选方案
4. **资金预留**：保留1010元用于新债中签等特殊需求
5. **到期处理**：T+1自动到期，无需手动操作

## 相关文档

- Ptrade API官方文档：http://ptradeapi.com
- 策略文件：strategy_M1_Ptrade.py
- 修改记录：现金管理修改说明_国债逆回购.md
