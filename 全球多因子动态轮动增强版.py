# 全球多因子动态轮动增强版 - 聚宽平台实现
# 策略说明：基于多因子模型对全球主要市场ETF进行动态轮动，选择综合得分最高的前3只ETF进行投资
# 因子包括：动量因子、波动率因子、风险调整收益因子、趋势质量因子
# 风控措施：ATR动态止损

from jqdata import *
import numpy as np
import pandas as pd
import talib as ta
from scipy import stats
from scipy.stats import linregress
from scipy.stats.mstats import winsorize
import datetime
import time

# 全局变量g会在聚宽平台上自动定义

# 初始化函数，设定基准等等
def initialize(context):
    # 设置参数
    set_params(context)

    # 设置基准
    set_benchmark('000300.XSHG')

    # 设置滑点、手续费
    set_option('use_real_price', True)
    set_slippage(PriceRelatedSlippage(0.00246))
    set_order_cost(OrderCost(open_tax=0, close_tax=0, open_commission=0.0003, close_commission=0.0003, min_commission=5), type='stock')

    # 设置全局变量
    g.etf_list = [
        '510300.XSHG',  # 沪深300ETF
        '513100.XSHG',  # 纳斯达克100ETF
        '513000.XSHG',  # 日经225ETF
        '513730.XSHG',  # 东南亚科技ETF
        '513080.XSHG',  # 法国CAC40ETF
        '513030.XSHG',  # 德国DAX ETF
        '518880.XSHG',  # 黄金ETF
        '159985.XSHE',  # 豆粕ETF
        '513500.XSHG',  # 标普500ETF
    ]

    # 添加初始化建仓标志，用于确保初始建仓只执行一次
    context.initial_position_flag = False

    # 在每个交易日开盘后执行初始建仓检查
    # 由于标志位的存在，实际上只会在第一个交易日执行建仓操作
    run_daily(initial_position, time='09:31')

    # 每月最后一个交易日调仓
    run_monthly(monthly_rebalance, monthday=-1, time='10:00')

    # 打印初始化信息
    log.info('策略初始化完成')

# 设置策略参数
def set_params(context):
    # 因子权重
    context.weight_momentum = 0.35
    context.weight_volatility = 0.20
    context.weight_risk_adjusted = 0.25
    context.weight_trend_quality = 0.20

    # 动量因子内部权重
    context.weight_mom_1m = 0.4
    context.weight_mom_3m = 0.3
    context.weight_mom_6m = 0.3

    # 趋势质量因子内部权重
    context.weight_adx = 0.6
    context.weight_ma200 = 0.4

    # 选股数量
    context.top_n = 3

    # 数据参数
    context.max_days = 250  # 最大回溯天数
    context.min_required_days = 60  # 最少需要的数据天数

# 获取调整后的历史数据（处理上市时间不足的ETF）
def get_adjusted_history(security, end_date, fields, max_days=250):
    """
    动态获取历史数据，如果标的上市时间不足max_days，则获取最大可用天数
    使用get_price API代替attribute_history，确保数据接口正常

    参数:
    security: 证券代码
    end_date: 结束日期，通常是context.current_dt.date()
    fields: 需要获取的字段列表，如['close', 'high', 'low']
    max_days: 最大回溯天数，默认250天

    返回:
    pandas.DataFrame: 包含请求字段的历史数据
    """
    try:
        # 获取证券信息，检查上市日期
        security_info = get_security_info(security)

        if security_info is None:
            log.warning(f"⚠️ {security} 无法获取证券信息，可能未上市或代码错误")
            return None

        # 计算实际可用的历史数据天数
        start_date = security_info.start_date
        available_days = (end_date - start_date).days

        # 如果可用天数太少，则返回None
        if available_days < 20:  # 至少需要20天数据
            log.warning(f"⚠️ {security} 上市时间过短，可用天数: {available_days}，至少需要20天")
            return None

        # 确定实际获取的天数并获取历史数据
        actual_days = min(available_days, max_days)
        history_data = get_price(
            security,
            count=actual_days,
            end_date=end_date,
            frequency='daily',
            fields=fields,
            skip_paused=False,
            fq='pre'
        )

        # 检查数据是否有效
        if history_data is None or history_data.empty:
            log.warning(f"⚠️ {security} 获取历史数据失败")
            return None
        if len(history_data) < 20:
            log.warning(f"⚠️ {security} 获取的历史数据不足，实际获取: {len(history_data)}天")
            return None

        # 检查数据中是否包含所需字段
        missing_fields = [field for field in fields if field not in history_data.columns]
        if missing_fields:
            log.warning(f"⚠️ {security} 历史数据缺少字段: {missing_fields}")
            return None

        return history_data

    except Exception as e:
        log.error(f"❌ 获取 {security} 历史数据出错: {str(e)}")
        import traceback
        log.error(f"❌ 错误详情: {traceback.format_exc()}")
        return None

# 计算动量因子
def calculate_momentum(prices):
    """
    计算1M、3M、6M动量因子
    """
    periods = [20, 60, 120]  # 约1个月、3个月、6个月
    moms = []

    for p in periods:
        if len(prices) >= p:
            mom = prices.iloc[-1] / prices.iloc[-p] - 1
        else:
            mom = np.nan
        moms.append(mom)

    return moms

# 计算趋势斜率
def calculate_slope(prices, period=60):
    """
    计算对数价格的线性回归斜率
    """
    if len(prices) < period:
        return np.nan

    log_prices = np.log(prices.iloc[-period:].values)
    x = np.arange(len(log_prices))

    try:
        slope, _, _, _, _ = linregress(x, log_prices)
        return slope
    except:
        return np.nan

# 计算波动率
def calculate_volatility(returns, period=60):
    """
    计算年化波动率
    """
    if len(returns) < period:
        return np.nan

    return np.std(returns.iloc[-period:]) * np.sqrt(252)

# 计算夏普比率
def calculate_sharpe(returns, period=60):
    """
    计算年化夏普比率
    """
    if len(returns) < period:
        return np.nan

    period_returns = returns.iloc[-period:]
    mean_return = np.mean(period_returns)
    std_return = np.std(period_returns)

    if std_return == 0:
        return np.nan

    return (mean_return / std_return) * np.sqrt(252)

# 计算ADX
def calculate_adx(high, low, close, period=14):
    """
    计算平均趋向指数ADX
    """
    if len(close) < period + 10:  # ADX需要额外的数据来计算
        return np.nan

    try:
        adx = ta.ADX(high.values, low.values, close.values, timeperiod=period)
        return adx[-1]
    except:
        return np.nan

# 计算MA200过滤器
def calculate_ma200_filter(close):
    """
    计算200日均线过滤器
    """
    if len(close) < 200:
        return np.nan

    ma200 = np.mean(close.iloc[-200:])
    current_price = close.iloc[-1]

    return 1 if current_price > ma200 else 0

# 计算ATR
def calculate_atr(high, low, close, period=14):
    """
    计算平均真实波幅ATR
    """
    if len(close) < period + 1:
        return np.nan

    try:
        atr = ta.ATR(high.values, low.values, close.values, timeperiod=period)
        return atr[-1]
    except:
        return np.nan

# 截面标准化
def cross_sectional_zscores(factor_values):
    """
    对因子值进行去极值和标准化处理
    """
    # 去除NaN值
    valid_values = [v for v in factor_values if not np.isnan(v)]

    if len(valid_values) < 2:  # 至少需要2个有效值
        return factor_values

    # 去极值
    try:
        winsorized = winsorize(valid_values, limits=[0.05, 0.05])
    except:
        winsorized = valid_values

    # 标准化
    mean = np.mean(winsorized)
    std = np.std(winsorized)

    if std == 0:
        return [0] * len(factor_values)

    # 将原始因子值替换为标准化后的值
    z_scores = []
    for i, v in enumerate(factor_values):
        if np.isnan(v):
            z_scores.append(np.nan)
        else:
            idx = valid_values.index(v)
            z_scores.append((winsorized[idx] - mean) / std)

    return z_scores

# 月度调仓函数
def monthly_rebalance(context):
    """
    每月最后一个交易日执行调仓
    """
    log.info("=== 开始月度调仓 ===")

    # 获取当前日期
    current_date = context.current_dt.date()
    log.info(f"当前日期: {current_date}")

    # 获取当前数据
    current_data = get_current_data()

    # 检查current_data是否为空
    if current_data is None:
        log.error("❌ current_data为空，API可能异常")
        return

    # 跳过未上市或停牌的标的
    valid_etfs = []
    for etf in g.etf_list:
        try:
            # 检查是否停牌
            security_data = current_data[etf]
            if security_data.paused:
                log.warning(f"⚠️ {etf} 处于停牌状态")
                continue

            valid_etfs.append(etf)
        except KeyError:
            log.warning(f"⚠️ {etf} 不存在于current_data中，可能代码错误或未上市")
            continue
        except Exception as e:
            log.error(f"❌ 处理 {etf} 时发生未知错误: {str(e)}")
            continue

    log.info(f"有效ETF列表: {valid_etfs}")

    if not valid_etfs:
        log.error("❌ 所有ETF均被过滤，原因可能是：ETF代码错误、全部停牌或current_data接口异常")
        return

    # 计算各ETF的因子值
    momentum_scores = []
    volatility_scores = []
    sharpe_scores = []
    trend_quality_scores = []
    valid_etf_list = []  # 存储有足够数据的ETF

    for etf in valid_etfs:
        # 获取历史数据
        history_data = get_adjusted_history(etf, current_date, ['close', 'high', 'low'], context.max_days)

        if history_data is None:
            log.warning(f"❌ {etf} 获取历史数据失败")
            continue
        elif len(history_data) < context.min_required_days:
            log.warning(f"❌ {etf} 数据不足，实际获取: {len(history_data)}天，最少需要: {context.min_required_days}天")
            continue

        # 计算日收益率
        returns = history_data['close'].pct_change().dropna()
        if len(returns) < context.min_required_days:
            log.warning(f"{etf} 收益率数据不足，跳过计算")
            continue

        # 计算各因子
        # 1. 动量因子
        mom_1m, mom_3m, mom_6m = calculate_momentum(history_data['close'])
        if np.isnan(mom_1m) or np.isnan(mom_3m) or np.isnan(mom_6m):
            momentum_combo = np.nan
            log.warning(f"{etf} 动量组合计算失败: 存在NaN值")
        else:
            momentum_combo = (context.weight_mom_1m * mom_1m +
                             context.weight_mom_3m * mom_3m +
                             context.weight_mom_6m * mom_6m)

        # 计算趋势斜率
        slope = calculate_slope(history_data['close'])

        # 计算动量得分
        if np.isnan(momentum_combo) or np.isnan(slope):
            momentum_score = np.nan
            log.warning(f"{etf} 动量得分计算失败: 存在NaN值")
        else:
            momentum_score = 0.7 * momentum_combo + 0.3 * slope

        # 2. 波动率因子
        volatility = calculate_volatility(returns)

        # 3. 夏普比率
        sharpe = calculate_sharpe(returns)

        # 4. 趋势质量因子
        adx = calculate_adx(history_data['high'], history_data['low'], history_data['close'])
        ma200_filter = calculate_ma200_filter(history_data['close'])

        if np.isnan(adx) or np.isnan(ma200_filter):
            trend_quality_score = np.nan
            log.warning(f"{etf} 趋势质量得分计算失败: 存在NaN值")
        else:
            trend_quality_score = context.weight_adx * adx + context.weight_ma200 * ma200_filter

        # 存储因子值
        momentum_scores.append(momentum_score)
        volatility_scores.append(volatility)
        sharpe_scores.append(sharpe)
        trend_quality_scores.append(trend_quality_score)
        valid_etf_list.append(etf)

    # 检查是否有足够的有效ETF
    if len(valid_etf_list) < 1:
        log.warning("没有足够的有效ETF，本次调仓取消")
        return

    # 对因子进行标准化处理
    log.info("=== 开始因子标准化处理 ===")
    
    # 波动率取负值（低波动性更好）
    neg_volatility_scores = [-v if not np.isnan(v) else np.nan for v in volatility_scores]

    # 标准化处理
    z_momentum = cross_sectional_zscores(momentum_scores)
    z_volatility = cross_sectional_zscores(neg_volatility_scores)
    z_sharpe = cross_sectional_zscores(sharpe_scores)
    z_trend_quality = cross_sectional_zscores(trend_quality_scores)

    # 计算综合得分
    log.info("=== 计算综合得分 ===")

    total_scores = []
    for i in range(len(valid_etf_list)):
        if (np.isnan(z_momentum[i]) or np.isnan(z_volatility[i]) or
            np.isnan(z_sharpe[i]) or np.isnan(z_trend_quality[i])):
            total_scores.append(np.nan)
            log.warning(f"{valid_etf_list[i]} 综合得分计算失败: 存在NaN值")
        else:
            score = (context.weight_momentum * z_momentum[i] +
                    context.weight_volatility * z_volatility[i] +
                    context.weight_risk_adjusted * z_sharpe[i] +
                    context.weight_trend_quality * z_trend_quality[i])
            total_scores.append(score)

    # 创建ETF-得分对应表，保留三位小数
    etf_scores = [(valid_etf_list[i], round(total_scores[i], 3)) for i in range(len(valid_etf_list)) if not np.isnan(total_scores[i])]

    # 按得分排序
    etf_scores.sort(key=lambda x: x[1], reverse=True)

    log.info(f"ETF得分排名: {etf_scores}")

    # 选择得分最高的前N只ETF
    top_etfs = etf_scores[:min(context.top_n, len(etf_scores))]

    if len(top_etfs) == 0:
        log.warning("没有有效的ETF得分，本次调仓取消")
        return

    # 计算权重 (得分加权)
    total_score = sum([max(0.01, score) for _, score in top_etfs])
    weights = {}

    for etf, score in top_etfs:
        weights[etf] = max(0.01, score) / total_score

    # 执行调仓
    rebalance_portfolio(context, weights)

# 执行调仓
def rebalance_portfolio(context, target_weights):
    """
    根据目标权重调整投资组合
    """
    # 获取当前持仓
    current_positions = context.portfolio.positions

    # 计算总价值 (包括现金)
    portfolio_value = context.portfolio.total_value

    # 卖出不在目标持仓中的股票
    for stock in list(current_positions.keys()):
        if stock not in target_weights:
            log.info(f"卖出 {stock}")
            order_target_value(stock, 0)

    # 计算可用于调仓的资金
    available_value = portfolio_value

    # 买入或调整目标持仓
    for stock, weight in target_weights.items():
        target_value = available_value * weight
        log.info(f"调整 {stock} 至目标价值: {target_value}")
        order_target_value(stock, target_value)



# 初始化建仓函数
def initial_position(context):
    """
    在策略启动的第一个交易日执行初始建仓
    使用与月度调仓相同的因子计算和打分逻辑
    """
    # 如果已经执行过初始建仓，则不再执行
    if context.initial_position_flag:
        return

    log.info("=== 开始执行初始建仓 ===")

    # 获取当前日期
    current_date = context.current_dt.date()
    log.info(f"初始建仓日期: {current_date}")

    # 调用月度调仓函数进行初始建仓
    monthly_rebalance(context)

    # 标记初始建仓已完成
    context.initial_position_flag = True
    log.info("=== 初始建仓完成，标记初始建仓标志为True ===")

    # 取消每日运行初始建仓的定时任务，避免不必要的函数调用
    # 注意：在实盘中，这个函数会在第一个交易日执行一次后就不再需要了
    # 但在回测中，为了确保只执行一次，我们依然保留了initial_position_flag标志
    try:
        unschedule_function(initial_position)
        log.info("已取消初始建仓的定时任务")
    except:
        log.info("取消初始建仓定时任务失败，将依赖标志位控制")
