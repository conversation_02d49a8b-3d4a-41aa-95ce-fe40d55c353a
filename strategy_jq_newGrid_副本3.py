# 导入所需的库
from jqdata import *
import numpy as np
import pandas as pd
import datetime
import math
import talib

# 初始化函数
def initialize(context):
    # 设置动态复权模式
    set_option('use_real_price', True)
    log.info("开启动态复权模式")
    
    # 设置基准
    set_benchmark('000300.XSHG')  # 沪深300作为基准
    
    # 设置滑点
    set_slippage(FixedSlippage(0.002))
    
    # 更新手续费为万一免5
    set_order_cost(OrderCost(open_tax=0, close_tax=0.001, open_commission=0.0001, close_commission=0.0001, min_commission=5), type='stock')
    
    # 设置参数
    context.total_position_pct = 0.7  # 总仓位70%
    context.reserve_position_pct = 0.3  # 备用资金30%
    
    # 存储每日网格价格的字典
    context.daily_grid_prices = {
        '513100.XSHG': {'buy_prices': [], 'sell_prices': [], 'amounts': []},
        '510300.XSHG': {'buy_prices': [], 'sell_prices': [], 'amounts': []},
        '518880.XSHG': {'buy_prices': [], 'sell_prices': [], 'amounts': []}
    }
    
    # 添加网格状态记录
    context.grid_status = {
        '513100.XSHG': {'triggered_buy': set(), 'triggered_sell': set(), 'last_price': None, 'base_price': None, 'dynamic_buy_prices': [], 'dynamic_sell_prices': [], 'dynamic_amounts': []},
        '510300.XSHG': {'triggered_buy': set(), 'triggered_sell': set(), 'last_price': None, 'base_price': None, 'dynamic_buy_prices': [], 'dynamic_sell_prices': [], 'dynamic_amounts': []},
        '518880.XSHG': {'triggered_buy': set(), 'triggered_sell': set(), 'last_price': None, 'base_price': None, 'dynamic_buy_prices': [], 'dynamic_sell_prices': [], 'dynamic_amounts': []}
    }
    
    # 添加动态网格参数
    context.dynamic_grid = True  # 是否启用动态网格
    
    # 设置交易标的
    context.stock_list = ['513100.XSHG',  # 纳斯达克ETF
                         '510300.XSHG',  # 沪深300ETF
                         '518880.XSHG']  # 黄金ETF
    
    # 初始化市场状态字典
    context.market_states = {}
    
    # 设置技术指标参数
    context.bias_period = 24  # BIAS周期
    context.adx_period = 13   # ADX周期
    context.atr_period = 12  # ATR周期
    
    # 设置BIAS阈值
    context.bias_threshold = {
        '513100.XSHG': 0.09,   # 纳斯达克ETF阈值±9%
        '510300.XSHG': 0.07,  # 沪深300ETF阈值±7%
        '518880.XSHG': 0.05   # 黄金ETF阈值±5%
    }
    
    # 添加持仓限制参数
    context.position_limits = {
        'oscillating': {'lower': 0.5, 'upper': 1.4},  # 震荡市
        'uptrend': {'lower': 0.7, 'upper': 1.6},      # 上涨趋势
        'downtrend': {'lower': 0.5, 'upper': 1.1}     # 下跌趋势
    }
    
    # 记录初始目标仓位
    context.initial_positions = {}
    
    # 设置网格参数
    context.grid_params = {
        '513100.XSHG': {  # 纳斯达克ETF
            'oscillating': {
                'layers': 14, 
                'buy_atr_multiple': 0.8,   # 震荡市买入间距
                'sell_atr_multiple': 0.9,  # 震荡市卖出间距
                'grid_value_pct': 0.04     # 震荡市每层资金占比
            },
            'uptrend': {
                'layers': 11, 
                'buy_atr_multiple': 1.1,   # 上涨市买入间距
                'sell_atr_multiple': 2.3,  # 上涨市卖出间距
                'grid_value_pct': 0.04     # 单边市每层资金占比
            },
            'downtrend': {
                'layers': 10, 
                'buy_atr_multiple': 2.3,   # 下跌市买入间距
                'sell_atr_multiple': 1.1,  # 下跌市卖出间距
                'grid_value_pct': 0.03     # 单边市每层资金占比
            }
        },
        '510300.XSHG': {  # 沪深300ETF
            'oscillating': {
                'layers': 12, 
                'buy_atr_multiple': 1.0,   # 震荡市买入间距
                'sell_atr_multiple': 1.0,   # 震荡市卖出间距
                'grid_value_pct': 0.030    # 震荡市每层资金占比
            },
            'uptrend': {
                'layers': 15, 
                'buy_atr_multiple': 1.0,   # 上涨市买入间距
                'sell_atr_multiple': 2.5,   # 上涨市卖出间距
                'grid_value_pct': 0.040    # 单边市每层资金占比
            },
            'downtrend': {
                'layers': 10, 
                'buy_atr_multiple': 2.5,   # 下跌市买入间距
                'sell_atr_multiple': 1.0,   # 下跌市卖出间距
                'grid_value_pct': 0.040    # 单边市每层资金占比
            }
        },
        '518880.XSHG': {  # 黄金ETF
            'oscillating': {
                'layers': 15, 
                'buy_atr_multiple': 1.2,   # 震荡市买入间距
                'sell_atr_multiple': 1.2,   # 震荡市卖出间距
                'grid_value_pct': 0.022    # 震荡市每层资金占比
            },
            'uptrend': {
                'layers': 12, 
                'buy_atr_multiple': 1.2,   # 上涨市买入间距
                'sell_atr_multiple': 1.8,   # 上涨市卖出间距
                'grid_value_pct': 0.030    # 单边市每层资金占比
            },
            'downtrend': {
                'layers': 13, 
                'buy_atr_multiple': 1.8,   # 下跌市买入间距
                'sell_atr_multiple': 1.2,   # 下跌市卖出间距
                'grid_value_pct': 0.030    # 单边市每层资金占比
            }
        }
    }
    
    # 设置风控参数
    context.single_stock_stop_loss = 0.05  # 单个标的5%止损
    context.portfolio_stop_loss = 0.04  # 组合4%止损
    
    # 运行时需要的变量
    context.last_rebalance_date = None  # 上次再平衡时间
    context.stop_trading = False  # 是否停止交易的标志
    
    # 初始化每个标的的市场状态
    for security in context.stock_list:
        context.market_states[security] = 'oscillating'
    
    # 添加缓存相关变量
    context.cache = {
        'technical_indicators': {},
        'last_update_date': None,
        'price_data': {},
        'last_minute': -1,
        'last_money_fund_trade_date': None  # 添加货币基金最后交易日期缓存
    }
    
    # 恢复为每分钟执行
    run_daily(market_open, time='9:30', reference_security='000300.XSHG')
    run_daily(check_stop_loss, time='9:31', reference_security='000300.XSHG')
    run_daily(trade_money_fund, time='9:32', reference_security='000300.XSHG')
    run_daily(check_fund_income, time='15:10', reference_security='000300.XSHG')
    
    # 设置最小交易数量
    context.min_trade_amount = 100
    
    # 设置是否开启交易日志
    log.set_level('order', 'info')
    log.set_level('strategy', 'info')
    
    # 添加初始化日志
    log.info("策略初始化完成")
    log.info(f"交易标的: {context.stock_list}")
    log.info(f"总仓位比例: {context.total_position_pct:.0%}")
    log.info(f"备用资金比例: {context.reserve_position_pct:.0%}")
    log.info(f"单个标的止损线: {context.single_stock_stop_loss:.0%}")
    log.info(f"组合止损线: {context.portfolio_stop_loss:.0%}")
    
    # 添加网格最小间距参数
    context.min_grid_spacing_pct = 0.002  # 最小网格间距为0.2%
    
    # 设置波动率分级参数
    context.volatility_level = {
        '513100.XSHG': [0.8, 1.5],  # [低波动阈值, 高波动阈值]
        '510300.XSHG': [0.7, 1.5],
        '518880.XSHG': [0.5, 1.7]
    }
    
    # 设置价格区间参数
    context.price_zones = {
        '513100.XSHG': {  # 纳斯达克ETF
            'low': {'range': (1.5, 2.0), 'layers': 12, 'buy_multiple': 0.8, 'sell_multiple': 0.8},
            'medium': {'range': (2.0, 2.5), 'layers': 10, 'buy_multiple': 1.0, 'sell_multiple': 1.0},
            'high': {'range': (2.5, 3.0), 'layers': 8, 'buy_multiple': 1.2, 'sell_multiple': 1.2}
        },
        '510300.XSHG': {  # 沪深300ETF
            'low': {'range': (3.0, 3.5), 'layers': 10, 'buy_multiple': 0.8, 'sell_multiple': 0.8},
            'medium': {'range': (3.5, 4.0), 'layers': 8, 'buy_multiple': 1.0, 'sell_multiple': 1.0},
            'high': {'range': (4.0, 4.5), 'layers': 6, 'buy_multiple': 1.2, 'sell_multiple': 1.2}
        },
        '518880.XSHG': {  # 黄金ETF
            'low': {'range': (2.5, 3.5), 'layers': 12, 'buy_multiple': 0.6, 'sell_multiple': 0.6},
            'medium': {'range': (3.5, 4.5), 'layers': 10, 'buy_multiple': 0.8, 'sell_multiple': 0.8},
            'high': {'range': (4.5, 5.5), 'layers': 8, 'buy_multiple': 1.0, 'sell_multiple': 1.0}
        }
    }
    
    # 设置价格区间切换缓冲带比例
    context.zone_buffer_pct = 0.03  # 3%的缓冲带
    
    # 替换逆回购配置为货币基金配置
    context.money_fund = '511990.XSHG'  # 华宝添益货币ETF
    
    # 设置最小货基交易金额（单位：万元）
    context.min_fund_amount = 5  # 提高最小交易金额到5万，减少频繁交易
    
    # 设置货基交易阈值
    context.money_fund_threshold = 0.02  # 2%的变动阈值，超过才触发交易
    
    # 设置是否已经完成初始建仓的标志
    context.initial_position_established = False

# 优化技术指标计算
def update_technical_indicators(context, security):
    """批量更新技术指标"""
    current_date = context.current_dt.date()
    
    # 如果不是新的交易日，且已有缓存，直接返回
    if (context.cache['last_update_date'] == current_date and 
        security in context.cache['technical_indicators']):
        return context.cache['technical_indicators'][security]
    
    # 获取所需的最长周期
    max_period = max(context.bias_period, context.adx_period*2, context.atr_period*2)
    
    # 一次性获取所有需要的数据
    prices = attribute_history(security, max_period, '1d', 
                             ['high', 'low', 'close', 'factor'])
    
    # 计算真实价格
    real_closes = prices['close'] / prices['factor']
    real_highs = prices['high'] / prices['factor']
    real_lows = prices['low'] / prices['factor']
    
    # 计算BIAS
    ma = real_closes[-context.bias_period:].mean()
    latest_price = real_closes[-1]
    bias = (latest_price - ma) / ma
    
    # 计算ADX
    tr = pd.Series(0.0, index=prices.index)
    plus_dm = pd.Series(0.0, index=prices.index)
    minus_dm = pd.Series(0.0, index=prices.index)
    
    for i in range(1, len(real_highs)):
        tr1 = real_highs[i] - real_lows[i]
        tr2 = abs(real_highs[i] - real_closes[i-1])
        tr3 = abs(real_lows[i] - real_closes[i-1])
        tr[i] = max(tr1, tr2, tr3)
        
        up_move = real_highs[i] - real_highs[i-1]
        down_move = real_lows[i-1] - real_lows[i]
        
        if up_move > down_move and up_move > 0:
            plus_dm[i] = up_move
        if down_move > up_move and down_move > 0:
            minus_dm[i] = down_move
    
    tr_ema = tr.ewm(span=context.adx_period, adjust=False).mean()
    plus_di = 100 * plus_dm.ewm(span=context.adx_period, adjust=False).mean() / tr_ema
    minus_di = 100 * minus_dm.ewm(span=context.adx_period, adjust=False).mean() / tr_ema
    dx = 100 * abs(plus_di - minus_di) / (plus_di + minus_di)
    adx = dx.ewm(span=context.adx_period, adjust=False).mean()
    
    # 计算ATR
    atr = tr.ewm(span=context.atr_period, adjust=False).mean()
    
    # 存储计算结果
    if 'technical_indicators' not in context.cache:
        context.cache['technical_indicators'] = {}
    
    context.cache['technical_indicators'][security] = {
        'bias': bias,
        'adx': adx[-1],
        'plus_di': plus_di[-1],
        'minus_di': minus_di[-1],
        'atr': atr[-1]
    }
    context.cache['last_update_date'] = current_date
    
    return context.cache['technical_indicators'][security]

# 修改原有的技术指标计算函数
def calculate_bias(context, security, n=24):
    """使用缓存的技术指标"""
    try:
        return context.cache['technical_indicators'][security]['bias']
    except Exception as e:
        log.error(f"计算BIAS时出错: {str(e)}")
        return 0.0

def calculate_adx(context, security, n=14):
    """使用缓存的技术指标"""
    try:
        indicators = context.cache['technical_indicators'][security]
        return indicators['adx'], indicators['plus_di'], indicators['minus_di']
    except Exception as e:
        log.error(f"计算ADX时出错: {str(e)}")
        return 0.0, 0.0, 0.0

def calculate_atr(context, security, n=14):
    """使用缓存的技术指标"""
    try:
        return context.cache['technical_indicators'][security]['atr']
    except Exception as e:
        log.error(f"计算ATR时出错: {str(e)}")
        return 0.0

# 修改market_open函数
def market_open(context):
    # 在开盘时更新所有标的的技术指标
    for security in context.stock_list:
        try:
            update_technical_indicators(context, security)
        except Exception as e:
            log.error(f"更新{security}技术指标时出错: {str(e)}")
    
    # 检查是否需要执行初始建仓
    if not context.initial_position_established:
        log.info("准备执行初始建仓")
        initial_buy(context)
        context.initial_position_established = True
        return
    
    # 取消未完成的订单
    cancel_open_orders(context)
    
    # 判断是否需要进行季度再平衡
    current_date = context.current_dt.date()
    if should_rebalance(context, current_date):
        log.info("执行季度再平衡")
        rebalance_portfolio(context)
    
    # 更新市场状态
    for security in context.stock_list:
        try:
            old_state = context.market_states[security]
            new_state = get_market_state(context, security)
            context.market_states[security] = new_state
            
            if old_state != new_state:
                log.info(f"{security} 市场状态从 {old_state} 变为 {new_state}")
                
            # 记录当前持仓情况
            position = context.portfolio.positions[security].total_amount if security in context.portfolio.positions else 0
            if position > 0:
                log.info(f"{security} 当前持仓: {position}股")
        except Exception as e:
            log.error(f"更新{security}市场状态时出错: {str(e)}")
    
    # 计算当日网格价格
    # calculate_daily_grid_prices(context)

# 计算证券收益率
def get_security_returns(security):
    """
    计算证券的日收益率，使用真实价格
    参数:
        security: 证券代码
    返回:
        float: 当日收益率
    """
    # 获取当前价格
    current_data = get_current_data()
    current_price = current_data[security].last_price
    
    # 获取昨日收盘价和复权因子
    hist = attribute_history(security, 1, '1d', ['close', 'factor'])
    yesterday_price = hist['close'][0] / hist['factor'][0]
    
    # 计算收益率
    returns = (current_price - yesterday_price) / yesterday_price
    
    return returns

# 检查止损
def check_stop_loss(context):
    for security in context.stock_list:
        returns = get_security_returns(security)
        # 使用动态止损线
        stop_loss_line = dynamic_stop_loss(context, security)
        if returns <= -stop_loss_line:
            log.info(f"{security} 触发动态止损线，当日收益率: {returns:.2%}")
            context.stop_trading = True
            return
    
    # 检查组合止损
    # 使用context.portfolio.returns获取组合收益率
    portfolio_returns = context.portfolio.returns
    if portfolio_returns <= -context.portfolio_stop_loss:
        log.info(f"触发组合止损线，当前组合收益率: {portfolio_returns:.2%}")
        context.stop_trading = True

def cancel_open_orders(context):
    """
    取消所有未完成的订单
    """
    orders = get_open_orders()
    for order in orders.values():
        cancel_order(order)
        log.info(f"取消未完成订单: {order.security}, 委托价格: {order.price}, 委托数量: {order.amount}")

def calculate_risk_parity_weights(context):
    # 初始化波动率字典
    volatilities = {}
    # 计算每个ETF的60日波动率
    for security in context.stock_list:
        price_data = get_price(security, count=60, frequency='daily', fields=['close'])
        close = price_data['close']
        returns = close.pct_change().dropna()
        volatility = returns.std()
        volatilities[security] = volatility
    # 计算权重：1/波动率 / 所有(1/波动率)之和
    inv_vol_sum = sum(1.0 / vol for vol in volatilities.values())
    weights = {security: (1.0 / vol) / inv_vol_sum for security, vol in volatilities.items()}
    # 优化：将权重转换为整数百分比
    total_pct = 100
    int_weights = {}
    remaining = total_pct
    for security, weight in weights.items():
        int_weight = int(weight * 100)
        int_weights[security] = int_weight
        remaining -= int_weight
    if remaining > 0:
        sorted_securities = sorted(weights.keys(), key=lambda x: weights[x], reverse=True)
        for i in range(remaining):
            security = sorted_securities[i % len(sorted_securities)]
            int_weights[security] += 1
    rounded_weights = {security: weight / 100.0 for security, weight in int_weights.items()}
    weight_str = ', '.join([f"{security}: {weight:.0%}" for security, weight in rounded_weights.items()])
    log.info(f"风险平价权重计算结果: {weight_str}")
    return rounded_weights

def rebalance_portfolio(context):
    weights = calculate_risk_parity_weights(context)
    original_reserve_pct = context.reserve_position_pct
    current_reserve_pct = original_reserve_pct
    for security in context.stock_list:
        if context.market_states[security] == 'uptrend':
            extra_weight = min(0.08, current_reserve_pct)
            weights[security] += extra_weight
            current_reserve_pct -= extra_weight
            log.info(f"{security} 处于上涨趋势，从备用资金中获得 {extra_weight:.1%} 的超配额度")
    for security in context.stock_list:
        if context.market_states[security] == 'downtrend':
            reduction = min(weights[security] * 0.05, 0.05)
            weights[security] -= reduction
            current_reserve_pct += reduction
            log.info(f"{security} 处于下跌趋势，减配 {reduction:.1%} 至备用资金")
    context.reserve_position_pct = current_reserve_pct
    log.info(f"调整后备用资金比例: {current_reserve_pct:.1%} (原始: {original_reserve_pct:.1%})")
    if context.money_fund in context.portfolio.positions:
        position = context.portfolio.positions[context.money_fund]
        if position and getattr(position, 'total_amount', 0) > 0:
            order_target(context.money_fund, 0)
            log.info(f"季度再平衡: 清空货基持仓 {position.total_amount}份")
    total_position_value = context.portfolio.total_value * (1 - current_reserve_pct)
    for security in context.stock_list:
        target_value = total_position_value * weights[security]
        order_target_value(security, target_value)
        log.info(f"调整 {security} 目标市值至: {target_value:.2f}, 权重: {weights[security]:.2%}")
    context.last_rebalance_date = context.current_dt.date()

def initial_buy(context):
    """
    初始建仓函数
    使用风险平价模型计算权重并进行首次建仓
    """
    log.info("开始执行初始建仓")
    
    try:
        # 计算风险平价权重
        weights = calculate_risk_parity_weights(context)
        weight_str = ', '.join([f"{security}: {weight:.2%}" for security, weight in weights.items()])
        log.info(f"初始风险平价权重: {weight_str}")
        
        # 获取当前市场状态并调整权重
        for security in context.stock_list:
            context.market_states[security] = get_market_state(context, security)
            log.info(f"{security} 初始市场状态: {context.market_states[security]}")
            
            # 根据市场状态调整权重
            if context.market_states[security] == 'uptrend':
                # 上涨趋势超配8%
                weights[security] = weights.get(security, 0) + 0.08
                # 从其他标的中等比例减少权重
                other_securities = [s for s in context.stock_list if s != security]
                weight_reduction = 0.08 / len(other_securities)
                for other_security in other_securities:
                    weights[other_security] = weights.get(other_security, 0) - weight_reduction
            elif context.market_states[security] == 'downtrend':
                # 下跌趋势减配5%
                reduction = min(weights.get(security, 0) * 0.05, 0.05)
                weights[security] = weights.get(security, 0) - reduction
                # 将减配的资金加入到备用资金中
                context.reserve_position_pct += reduction
        
        # 计算权重总和并归一化
        weight_sum = sum(weight for weight in weights.values())
        log.info(f"调整后权重总和: {weight_sum:.2%}")
        
        if weight_sum > 1:
            # 对权重进行归一化
            weights = {security: weight/weight_sum for security, weight in weights.items()}
            weight_str = ', '.join([f"{security}: {weight:.2%}" for security, weight in weights.items()])
            log.info(f"归一化后的权重: {weight_str}")
        
        # 计算每个标的的目标金额
        total_value = context.portfolio.total_value
        available_cash = context.portfolio.available_cash
        
        # 执行建仓
        for security in context.stock_list:
            try:
                # 计算目标金额
                target_value = total_value * context.total_position_pct * weights.get(security, 0)
                
                # 获取当前价格
                current_data = get_current_data()[security]
                if current_data.paused:
                    log.warning(f"{security} 当前暂停交易，跳过建仓")
                    continue
                    
                current_price = current_data.last_price
                
                # 计算购买数量（确保是100的整数倍）
                amount = int(target_value / current_price / 100) * 100
                
                if amount >= 100:  # 确保购买数量至少为100股
                    order_value = amount * current_price
                    if order_value <= available_cash:  # 确保资金充足
                        order(security, amount)
                        log.info(f"初始建仓 {security}: 数量={amount}, 价格={current_price:.3f}, 金额={order_value:.2f}")
                    else:
                        log.warning(f"初始建仓 {security}: 资金不足，需要{order_value:.2f}，可用{available_cash:.2f}")
                else:
                    log.warning(f"初始建仓 {security}: 计算购买数量小于100股，跳过购买")
            except Exception as e:
                log.error(f"处理标的 {security} 时发生错误: {str(e)}")
                continue
                
        log.info("初始建仓完成")
    except Exception as e:
        log.error(f"初始建仓过程中发生错误: {str(e)}")
        raise

def manage_grid_orders(context, security):
    """
    管理网格订单
    """
    # 获取所有未完成订单
    all_open_orders = get_open_orders()
    # 筛选出指定security的订单
    open_orders = {order_id: order for order_id, order in all_open_orders.items() 
                  if order.security == security}
    
    # 获取当前价格
    current_data = get_current_data()[security]
    current_price = current_data.last_price
    
    # 检查每个未完成订单
    for order in open_orders.values():
        # 如果订单价格偏离当前价格太远（例如超过2%），则取消该订单
        price_deviation = abs(order.price - current_price) / current_price
        if price_deviation > 0.02:
            cancel_order(order)
            log.info(f"取消偏离过大的订单: {security}, 委托价格: {order.price}, 当前价格: {current_price}")
        # 如果订单超过30分钟未成交，取消订单
        order_time = order.entrust_time
        current_time = context.current_dt
        if (current_time - order_time).total_seconds() > 1800:  # 1800秒 = 30分钟
            cancel_order(order)
            log.info(f"取消长时间未成交订单: {security}, 委托价格: {order.price}, 委托时间: {order_time}")

def adjust_by_volatility(context, security):
    """
    根据波动率调整网格参数
    """
    # 计算当前ATR
    current_atr = calculate_atr(context, security, context.atr_period)
    
    # 计算60日历史波动率基准
    hist_data = attribute_history(security, 60, '1d', ['high', 'low', 'close', 'factor'])
    real_closes = hist_data['close'] / hist_data['factor']
    base_volatility = real_closes.pct_change().std()
    
    # 计算当前波动率相对基准的比例
    volatility_ratio = current_atr / (base_volatility * real_closes.mean())
    
    # 根据波动率水平返回调整系数
    if volatility_ratio < context.volatility_level[security][0]:
        return 0.7  # 低波动系数
    elif volatility_ratio > context.volatility_level[security][1]:
        return 1.5  # 高波动系数
    else:
        return 1.0  # 正常系数

def get_price_zone_params(context, security, current_price):
    """
    根据当前价格获取对应区间的网格参数
    """
    price_zones = context.price_zones[security]
    
    # 计算基于ATR的动态缓冲带
    atr = calculate_atr(context, security, context.atr_period)
    buffer = 2 * atr  # 使用2倍ATR作为缓冲带
    
    # 遍历价格区间，考虑缓冲带
    for zone, config in price_zones.items():
        zone_range = config['range']
        lower_bound = zone_range[0] - buffer
        upper_bound = zone_range[1] + buffer
        
        if lower_bound <= current_price < upper_bound:
            return config
    
    # 如果价格超出所有区间，使用最近的区间参数
    if current_price < price_zones['low']['range'][0]:
        return price_zones['low']
    else:
        return price_zones['high']

def calculate_daily_grid_prices(context):
    for security in context.stock_list:
        try:
            # 获取当前价格
            current_data = get_current_data()[security]
            if current_data.paused:
                log.warning(f"{security} 当前暂停交易，跳过网格价格计算")
                continue
            current_price = current_data.last_price

            # 获取自适应网格参数
            layers, spacing, weights = adaptive_grid_params(context, security)
            # 计算每层资金
            total_value = context.portfolio.total_value * context.total_position_pct / len(context.stock_list)
            grid_amounts = [max(100, int(total_value * w / current_price / 100) * 100) for w in weights]

            # 后续用grid_amounts替换原有amounts
            # 这里补充你原有的网格价格计算逻辑
            # 例如：
            # buy_prices, sell_prices = ... # 你的网格价格生成逻辑
            # context.daily_grid_prices[security] = {
            #     'buy_prices': buy_prices,
            #     'sell_prices': sell_prices,
            #     'amounts': grid_amounts
            # }
            # log.info(f"{security} 网格层数: {layers}, 每层资金: {grid_amounts}")

        except Exception as e:
            log.error(f"计算{security}网格价格时发生错误: {str(e)}")

def calculate_grid_prices(context, security, current_price, atr, market_state, layers):
    """动态计算网格价格"""
    try:
        # 获取历史波动率
        hist_data = attribute_history(security, 20, '1d', ['high', 'low', 'close'])
        volatility = hist_data['close'].pct_change().std()
        
        # 获取当前价格限制
        current_data = get_current_data()[security]
        low_limit = current_data.low_limit
        high_limit = current_data.high_limit
        
        # 计算动态网格间距
        base_spacing = atr * context.grid_params[security][market_state]['buy_atr_multiple']
        dynamic_spacing = base_spacing * (1 + volatility * 0.5)  # 根据波动率调整间距
        
        buy_prices = []
        sell_prices = []
        
        for i in range(layers):
            # 使用非线性网格间距
            spacing_factor = math.sqrt(i + 1)  # 使用平方根函数使间距逐渐增大
            buy_spacing = dynamic_spacing * spacing_factor
            sell_spacing = dynamic_spacing * spacing_factor * 1.2  # 卖出间距略大于买入间距
            
            buy_price = max(low_limit, 
                           current_price * (1 - buy_spacing / current_price))
            sell_price = min(high_limit, 
                            current_price * (1 + sell_spacing / current_price))
            
            buy_prices.append(round(buy_price, 3))
            sell_prices.append(round(sell_price, 3))
        
        log.info(f"{security} 计算网格价格 - 基准价: {current_price:.3f}, ATR: {atr:.4f}, 波动率: {volatility:.4f}")
        log.info(f"买入价格: {buy_prices}")
        log.info(f"卖出价格: {sell_prices}")
        
        return buy_prices, sell_prices
        
    except Exception as e:
        log.error(f"计算 {security} 网格价格时发生错误: {str(e)}")
        # 返回默认网格价格
        default_buy_prices = [round(current_price * (1 - 0.01 * (i+1)), 3) for i in range(layers)]
        default_sell_prices = [round(current_price * (1 + 0.01 * (i+1)), 3) for i in range(layers)]
        log.warning(f"使用默认网格价格 - 买入: {default_buy_prices}, 卖出: {default_sell_prices}")
        return default_buy_prices, default_sell_prices

def calculate_grid_amount(context, security, base_amount, price_level, market_state):
    """动态计算网格数量"""
    # 获取当前持仓
    position = context.portfolio.positions.get(security)
    current_amount = position.total_amount if position else 0
    
    # 计算价格偏离度
    current_price = get_current_data()[security].last_price
    hist_data = attribute_history(security, 20, '1d', ['close'])
    price_mean = hist_data['close'].mean()
    price_deviation = abs(current_price - price_mean) / price_mean
    
    # 根据市场状态和价格偏离度调整数量
    if market_state == 'uptrend':
        # 上涨趋势时，高位少买，低位多买
        amount_factor = 1 + (1 - price_level) * 0.2
    elif market_state == 'downtrend':
        # 下跌趋势时，高位多卖，低位少卖
        amount_factor = 1 + price_level * 0.2
    else:
        # 震荡市时，根据价格偏离度调整
        amount_factor = 1 + price_deviation * 0.1
    
    # 计算最终数量
    final_amount = int(base_amount * amount_factor)
    return max(context.min_trade_amount, final_amount)

def calculate_dynamic_grid_prices(context, security, base_price, is_buy_trigger=False, is_sell_trigger=False):
    """
    计算动态网格价格，在每次交易后调用
    base_price: 新的基准价格
    is_buy_trigger: 是否由买入触发的重新计算
    is_sell_trigger: 是否由卖出触发的重新计算
    """
    try:
        # 获取市场数据
        current_data = get_current_data()[security]
        
        # 获取ATR和市场状态
        atr = calculate_atr(context, security, context.atr_period)
        market_state = context.market_states[security]
        params = context.grid_params[security][market_state]
        
        # 获取波动率调整系数
        vol_factor = adjust_by_volatility(context, security)
        
        # 获取价格区间参数
        zone_params = get_price_zone_params(context, security, base_price)
        
        # 合并参数：使用价格区间的层数，并调整ATR倍数
        layers = zone_params['layers']
        buy_atr_multiple = params['buy_atr_multiple'] * zone_params['buy_multiple'] * vol_factor
        sell_atr_multiple = params['sell_atr_multiple'] * zone_params['sell_multiple'] * vol_factor
        grid_value_pct = params['grid_value_pct']
        
        # 计算可用资金和基础网格数量
        available_cash = context.portfolio.available_cash
        
        # 获取持仓信息
        position_value = 0
        if security in context.portfolio.positions:
            position = context.portfolio.positions[security]
            position_value = position.total_amount * base_price if position.total_amount > 0 else 0
        
        total_value = position_value + available_cash
        
        # 计算每个网格的资金金额
        grid_value = total_value * grid_value_pct
        
        # 计算基础网格数量（确保是100的整数倍）
        base_grid_amount = int(grid_value / base_price / 100) * 100
        base_grid_amount = max(base_grid_amount, 100)  # 确保最小交易数量
        
        buy_prices, sell_prices = calculate_grid_prices(context, security, base_price, atr, market_state, layers)
        
        # 更新动态网格价格
        context.grid_status[security]['dynamic_buy_prices'] = buy_prices
        context.grid_status[security]['dynamic_sell_prices'] = sell_prices
        context.grid_status[security]['dynamic_amounts'] = [calculate_grid_amount(context, security, base_grid_amount, i, market_state) for i in range(layers)]
        context.grid_status[security]['base_price'] = base_price
        
        # 日志输出
        log.info(f"{security} 更新动态网格 - 基准价: {base_price:.3f}, 市场状态: {market_state}")
        log.info(f"价格区间参数 - 层数: {layers}, 买入倍数: {zone_params['buy_multiple']:.2f}, 卖出倍数: {zone_params['sell_multiple']:.2f}")
        log.info(f"波动率调整系数: {vol_factor:.2f}")
        log.info(f"最终ATR倍数 - 买入: {buy_atr_multiple:.2f}, 卖出: {sell_atr_multiple:.2f}")
        log.info(f"买入价格: {buy_prices}")
        log.info(f"卖出价格: {sell_prices}")
        
        if is_buy_trigger:
            log.info(f"买入触发更新 - 新买入价格: {buy_prices}")
        elif is_sell_trigger:
            log.info(f"卖出触发更新 - 新卖出价格: {sell_prices}")
        else:
            log.info(f"全面更新 - 买入价格: {buy_prices}")
            log.info(f"全面更新 - 卖出价格: {sell_prices}")
        
        return True
    except Exception as e:
        log.error(f"计算 {security} 动态网格价格时发生错误: {str(e)}")
        return False

def check_risk_limits(context, security):
    """
    检查风险限制
    """
    # 检查浮动盈亏
    if security in context.portfolio.positions:
        position = context.portfolio.positions[security]
        # 使用当前价格和持仓均价计算浮动盈亏
        current_data = get_current_data()[security]
        current_price = current_data.last_price
        floating_pnl = (current_price - position.avg_cost) / position.avg_cost
        
        # 如果浮动亏损超过7%，暂停开新仓
        if floating_pnl < -0.07:
            log.info(f"{security} 浮动亏损 {floating_pnl:.2%} 超过限制，暂停开新仓")
            return False
    
    return True

def check_position_before_order(context, security, amount):
    """
    下单前检查持仓是否足够
    """
    if amount < 0:  # 卖出订单
        # 获取当前可用持仓
        total_amount = 0
        if security in context.portfolio.positions:
            position = context.portfolio.positions[security]
            total_amount = position.total_amount
        
        # 获取所有未完成的卖出订单
        all_open_orders = get_open_orders()
        pending_sell_orders = sum([order.amount for order in all_open_orders.values() 
                                 if order.security == security and not order.is_buy])
        available_position = total_amount + pending_sell_orders  # 当前持仓减去未成交的卖出订单
        
        if abs(amount) > available_position:
            log.info(f"{security} 可用持仓不足，调整卖出数量从 {abs(amount)} 到 {available_position}")
            return -available_position if available_position > 0 else 0
    
    return amount

def trade_money_fund(context):
    """
    优化后的货币基金交易函数
    """
    try:
        current_date = context.current_dt.date()
        
        # 如果今天已经交易过，直接返回
        if context.cache['last_money_fund_trade_date'] == current_date:
            return
            
        # 计算可用于货基的资金
        total_value = context.portfolio.total_value
        reserved_cash = total_value * context.reserve_position_pct
        available_cash = context.portfolio.available_cash
        
        # 确保至少留存1%的资金作为缓冲
        buffer_cash = total_value * 0.01
        investable_cash = min(reserved_cash, available_cash - buffer_cash)
        
        if investable_cash < context.min_fund_amount * 10000:
            return
            
        try:
            # 获取货基价格和交易状态
            current_data = get_current_data()[context.money_fund]
            if current_data and not current_data.paused:
                current_price = current_data.last_price
                
                # 获取当前货基持仓
                current_position = context.portfolio.positions.get(context.money_fund, None)
                current_amount = current_position.total_amount if current_position else 0
                
                # 计算目标持仓
                target_amount = int(investable_cash / (current_price * 100)) * 100
                
                # 只有当目标持仓与当前持仓差异超过阈值时才交易
                if current_amount > 0:
                    current_value = current_amount * current_price
                    target_value = target_amount * current_price
                    change_pct = abs(target_value - current_value) / current_value
                    
                    if change_pct < context.money_fund_threshold:
                        return
                
                # 执行交易
                if target_amount >= 100:
                    order_target(context.money_fund, target_amount)
                    context.cache['last_money_fund_trade_date'] = current_date
                    
        except Exception as e:
            log.error(f"处理货基交易时发生错误: {str(e)}")
            
    except Exception as e:
        log.error(f"货基交易过程中发生错误: {str(e)}")

def calculate_volatility(context, security, period=20):
    """
    计算证券的波动率
    参数:
        context: 策略上下文
        security: 证券代码
        period: 计算周期，默认20天
    返回:
        float: 波动率
    """
    try:
        # 获取历史数据
        hist_data = attribute_history(security, period, '1d', ['close', 'factor'])
        
        # 计算真实价格
        real_closes = hist_data['close'] / hist_data['factor']
        
        # 计算日收益率
        returns = real_closes.pct_change().dropna()
        
        # 计算波动率（标准差）
        volatility = returns.std()
        
        return volatility
    except Exception as e:
        log.error(f"计算{security}波动率时出错: {str(e)}")
        return 0.02  # 返回默认波动率2%

def calculate_portfolio_volatility(context, period=20):
    """
    计算投资组合的波动率
    参数:
        context: 策略上下文
        period: 计算周期，默认20天
    返回:
        float: 组合波动率
    """
    try:
        # 获取所有持仓的波动率
        volatilities = []
        weights = []
        
        for security in context.stock_list:
            if security in context.portfolio.positions:
                position = context.portfolio.positions[security]
                if position.total_amount > 0:
                    # 计算单个标的的波动率
                    vol = calculate_volatility(context, security, period)
                    # 计算权重
                    weight = position.value / context.portfolio.total_value
                    volatilities.append(vol)
                    weights.append(weight)
        
        # 如果没有持仓，返回默认波动率
        if not volatilities:
            return 0.02
            
        # 计算加权平均波动率
        portfolio_volatility = sum(v * w for v, w in zip(volatilities, weights))
        
        return portfolio_volatility
    except Exception as e:
        log.error(f"计算组合波动率时出错: {str(e)}")
        return 0.02  # 返回默认波动率2%

def calculate_sharpe_ratio(context, period=20, risk_free_rate=0.03):
    """
    计算夏普比率
    参数:
        context: 策略上下文
        period: 计算周期，默认20天
        risk_free_rate: 无风险利率，默认3%
    返回:
        float: 夏普比率
    """
    try:
        # 获取历史收益率
        returns = []
        for i in range(period):
            date = context.current_dt.date() - datetime.timedelta(days=i)
            if date in context.portfolio.daily_returns:
                returns.append(context.portfolio.daily_returns[date])
        
        if not returns:
            return 0.0
            
        # 计算平均收益率
        avg_return = sum(returns) / len(returns)
        
        # 计算波动率
        volatility = np.std(returns)
        
        if volatility == 0:
            return 0.0
            
        # 计算夏普比率
        sharpe_ratio = (avg_return - risk_free_rate/252) / volatility * np.sqrt(252)
        
        return sharpe_ratio
    except Exception as e:
        log.error(f"计算夏普比率时出错: {str(e)}")
        return 0.0

def check_fund_income(context):
    """
    优化后的货基收益检查函数
    """
    try:
        # 只在收盘前检查一次
        if context.current_dt.time() != datetime.time(15, 10):
            return
            
        # 检查货币基金持仓
        if context.money_fund in context.portfolio.positions:
            position = context.portfolio.positions[context.money_fund]
            if position and getattr(position, 'total_amount', 0) > 0:  # 确保持仓存在且数量大于0
                current_data = get_current_data()[context.money_fund]
                if current_data and not current_data.paused:  # 确保证券未停牌
                    current_price = current_data.last_price
                    
                    cost = position.avg_cost * position.total_amount
                    current_value = current_price * position.total_amount
                    daily_return = (current_value - cost) / cost
                    
                    # 只在收益率超过0.01%时记录日志
                    if abs(daily_return) > 0.0001:
                        log.info(f"货基当日收益: {daily_return:.4%}")
                    
    except Exception as e:
        log.error(f"检查货基收益时发生错误: {str(e)}")

def reset_grid_status(context, security, current_price, grid_status, buy_prices, sell_prices):
    """智能网格重置"""
    try:
        # 获取市场状态和波动率
        market_state = context.market_states[security]
        volatility = calculate_volatility(context, security)
        
        # 计算价格变动
        price_change = abs(current_price - grid_status['base_price']) / grid_status['base_price']
        
        # 根据市场状态和波动率计算重置阈值
        if market_state == 'uptrend':
            reset_threshold = 0.02 + volatility * 1.5  # 上涨趋势提高重置阈值
        elif market_state == 'downtrend':
            reset_threshold = 0.015 + volatility * 1.2  # 下跌趋势降低重置阈值
        else:
            reset_threshold = 0.01 + volatility  # 震荡市使用基础阈值
            
        # 检查是否需要重置
        if price_change > reset_threshold:
            # 重置网格
            grid_status['triggered_buy'].clear()
            grid_status['triggered_sell'].clear()
            grid_status['base_price'] = current_price
            
            # 重新计算网格价格
            calculate_daily_grid_prices(context)
            
            log.info(f"{security} 触发智能重置 - 价格变动: {price_change:.2%}, 阈值: {reset_threshold:.2%}")
            
    except Exception as e:
        log.error(f"智能网格重置时出错: {str(e)}")

def get_market_state(context, security):
    """
    获取市场状态
    参数:
        context: 策略上下文
        security: 证券代码
    返回:
        str: 市场状态 ('uptrend', 'downtrend', 'oscillating')
    """
    try:
        # 获取历史数据
        hist_data = attribute_history(security, 60, '1d', ['high', 'low', 'close', 'factor'])
        if len(hist_data) < 20:
            return 'oscillating'
            
        # 计算真实价格
        real_closes = hist_data['close'] / hist_data['factor']
        real_highs = hist_data['high'] / hist_data['factor']
        real_lows = hist_data['low'] / hist_data['factor']
        
        # 计算移动平均
        ma20 = real_closes[-20:].mean()
        ma60 = real_closes[-60:].mean() if len(real_closes) >= 60 else real_closes.mean()
        
        # 计算BIAS
        ma = real_closes[-context.bias_period:].mean()
        latest_price = real_closes[-1]
        bias = (latest_price - ma) / ma
        
        # 计算ADX
        tr = pd.Series(0.0, index=hist_data.index)
        plus_dm = pd.Series(0.0, index=hist_data.index)
        minus_dm = pd.Series(0.0, index=hist_data.index)
        
        for i in range(1, len(real_highs)):
            tr1 = real_highs[i] - real_lows[i]
            tr2 = abs(real_highs[i] - real_closes[i-1])
            tr3 = abs(real_lows[i] - real_closes[i-1])
            tr[i] = max(tr1, tr2, tr3)
            
            up_move = real_highs[i] - real_highs[i-1]
            down_move = real_lows[i-1] - real_lows[i]
            
            if up_move > down_move and up_move > 0:
                plus_dm[i] = up_move
            if down_move > up_move and down_move > 0:
                minus_dm[i] = down_move
        
        tr_ema = tr.ewm(span=context.adx_period, adjust=False).mean()
        plus_di = 100 * plus_dm.ewm(span=context.adx_period, adjust=False).mean() / tr_ema
        minus_di = 100 * minus_dm.ewm(span=context.adx_period, adjust=False).mean() / tr_ema
        dx = 100 * abs(plus_di - minus_di) / (plus_di + minus_di)
        adx = dx.ewm(span=context.adx_period, adjust=False).mean()
        
        # 判断趋势
        if real_closes[-1] > ma20 and ma20 > ma60 and plus_di[-1] > minus_di[-1] and bias > 0:
            return 'uptrend'
        elif real_closes[-1] < ma20 and ma20 < ma60 and minus_di[-1] > plus_di[-1] and bias < 0:
            return 'downtrend'
        else:
            return 'oscillating'
            
    except Exception as e:
        log.error(f"获取{security}市场状态时出错: {str(e)}")
        return 'oscillating'

def calculate_dynamic_stop_loss(context, security):
    """计算动态止损线"""
    try:
        # 获取市场状态和波动率
        market_state = context.market_states[security]
        volatility = calculate_volatility(context, security)
        
        # 基础止损线
        base_stop_loss = context.single_stock_stop_loss
        
        # 根据市场状态调整
        if market_state == 'uptrend':
            stop_loss_factor = 1.2  # 上涨趋势放宽止损
        elif market_state == 'downtrend':
            stop_loss_factor = 0.8  # 下跌趋势收紧止损
        else:
            stop_loss_factor = 1.0  # 震荡市保持基础止损
            
        # 根据波动率调整
        volatility_factor = 1.0 + (volatility - 0.02) * 2  # 波动率每增加1%，止损放宽2%
        volatility_factor = max(0.8, min(1.5, volatility_factor))  # 限制调整范围
        
        # 计算最终止损线
        final_stop_loss = base_stop_loss * stop_loss_factor * volatility_factor
        
        return max(0.03, min(0.08, final_stop_loss))  # 限制止损线在3%-8%之间
        
    except Exception as e:
        log.error(f"计算动态止损线时出错: {str(e)}")
        return context.single_stock_stop_loss

def monitor_strategy_performance(context):
    """监控策略性能"""
    try:
        # 计算当日收益
        daily_return = context.portfolio.returns
        
        # 计算波动率
        portfolio_volatility = calculate_portfolio_volatility(context)
        
        # 计算夏普比率
        sharpe_ratio = calculate_sharpe_ratio(context)
        
        # 检查风险指标
        if daily_return < -0.02:  # 单日亏损超过2%
            log.warning(f"策略单日亏损较大: {daily_return:.2%}")
            
        if portfolio_volatility > 0.03:  # 波动率超过3%
            log.warning(f"策略波动率较高: {portfolio_volatility:.2%}")
            
        if sharpe_ratio < 0.5:  # 夏普比率低于0.5
            log.warning(f"策略夏普比率较低: {sharpe_ratio:.2f}")
            
        # 记录性能指标
        log.info(f"策略性能指标 - 日收益: {daily_return:.2%}, 波动率: {portfolio_volatility:.2%}, 夏普比率: {sharpe_ratio:.2f}")
        
    except Exception as e:
        log.error(f"监控策略性能时出错: {str(e)}")

def dynamic_stop_loss(context, security):
    # 根据历史波动率动态调整止损线
    vol = calculate_volatility(context, security)
    base_stop = 0.05
    stop_loss = max(0.03, min(0.08, base_stop * (1 + vol / 0.02)))
    return stop_loss

def adaptive_grid_params(context, security):
    vol = calculate_volatility(context, security)
    trend = get_trend_strength(context, security)
    avg_vol = get_avg_volume(context, security)
    # 层数和间距自适应
    if vol > 0.03 or trend > 30:
        layers = 8
        spacing = 1.5
    else:
        layers = 14
        spacing = 1.0
    # 资金分配比例自适应（如底部网格分配更多资金）
    # 趋势强时，底部分配更多，顶部分配更少
    if trend > 30:
        weights = np.linspace(2, 1, layers)
    else:
        weights = np.ones(layers)
    weights = weights / weights.sum()  # 归一化
    return layers, spacing, weights

def should_rebalance(context, current_date):
    """判断是否需要再平衡"""
    if current_date.month not in [3, 6, 9, 12]:
        return False
    current_month = current_date.month
    next_month = current_month + 1 if current_month < 12 else 1
    next_month_first_day = datetime.date(current_date.year + (current_month == 12), next_month, 1)
    current_month_first_day = datetime.date(current_date.year, current_date.month, 1)
    month_trading_days = get_trade_days(start_date=current_month_first_day, end_date=next_month_first_day - datetime.timedelta(days=1))
    if len(month_trading_days) == 0:
        return False
    last_trading_day = month_trading_days[-1]
    if current_date == last_trading_day:
        if context.last_rebalance_date is not None:
            if context.last_rebalance_date.year == current_date.year and context.last_rebalance_date.month == current_date.month:
                return False
        return True
    return False