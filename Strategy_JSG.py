# 导入所需的库
from jqdata import *
import numpy as np
import pandas as pd
import datetime
import math

class Strategy:
    """
    策略基类，提供JSG_Strategy和其他策略的共同功能
    """
    def __init__(self, context, index, name):
        """
        初始化策略
        
        参数:
            context: 策略上下文
            index: 策略索引，用于在portfolio_value_proportion中查找对应的资金比例
            name: 策略名称
        """
        self.context = context
        self.index = index
        self.name = name
        self.hold_list = []  # 当前持仓列表
        self.def_stocks = []  # 默认防御性股票列表
    
    def filter_basic_stock(self, stocks):
        """
        基础股票过滤
        
        参数:
            stocks: 股票列表
        返回:
            过滤后的股票列表
        """
        # 过滤停牌股票
        current_data = get_current_data()
        stocks = [stock for stock in stocks if not current_data[stock].paused]
        
        # 过滤ST股票
        stocks = [stock for stock in stocks if not current_data[stock].is_st]
        
        # 过滤新股
        stocks = [stock for stock in stocks if (
            datetime.datetime.now().date() - 
            get_security_info(stock).start_date
        ).days > 60]
        
        return stocks
    
    def filter_limitup_stock(self, stocks, days=1):
        """
        过滤连续涨停的股票
        
        参数:
            stocks: 股票列表
            days: 连续涨停天数
        返回:
            过滤后的股票列表
        """
        if not stocks:
            return []
        
        # 获取历史数据
        df = get_price(
            stocks, 
            count=days+1, 
            end_date=self.context.previous_date, 
            frequency='daily', 
            fields=['close', 'high', 'low', 'pre_close']
        )
        
        # 计算涨停价
        df['limit_up'] = df['pre_close'] * 1.1
        df['limit_up'] = df['limit_up'].apply(lambda x: round(x, 2))
        
        # 判断是否涨停
        df['is_limit_up'] = df['close'] >= df['limit_up'] - 0.01
        
        # 统计连续涨停天数
        limit_up_stocks = []
        for stock in stocks:
            if df['is_limit_up'][stock][-days:].all():
                limit_up_stocks.append(stock)
        
        # 返回不在连续涨停列表中的股票
        return [stock for stock in stocks if stock not in limit_up_stocks]
    
    def filter_limitup_limitdown_stock(self, stocks):
        """
        过滤当日涨停或跌停的股票
        
        参数:
            stocks: 股票列表
        返回:
            过滤后的股票列表
        """
        if not stocks:
            return []
        
        # 获取当前数据
        current_data = get_current_data()
        filtered_stocks = []
        
        for stock in stocks:
            current_price = current_data[stock].last_price
            pre_close = current_data[stock].pre_close
            
            # 计算涨停价和跌停价
            limit_up = round(pre_close * 1.1, 2)
            limit_down = round(pre_close * 0.9, 2)
            
            # 判断是否涨停或跌停
            if current_price < limit_up - 0.01 and current_price > limit_down + 0.01:
                filtered_stocks.append(stock)
        
        return filtered_stocks
    
    def _adjust(self, stocks):
        """
        调整持仓到指定的股票列表
        
        参数:
            stocks: 目标股票列表
        """
        if not stocks:
            return
        
        # 计算每只股票的目标市值
        total_value = self.context.portfolio.total_value * g.portfolio_value_proportion[self.index]
        target_value_per_stock = total_value / len(stocks)
        
        # 卖出不在目标列表中的股票
        for stock in self.hold_list[:]:
            if stock not in stocks:
                order_target(stock, 0)
                self.hold_list.remove(stock)
                log.info(f"[{self.name}] 卖出: {stock}")
        
        # 买入或调整目标列表中的股票
        for stock in stocks:
            order_target_value(stock, target_value_per_stock)
            if stock not in self.hold_list:
                self.hold_list.append(stock)
                log.info(f"[{self.name}] 买入: {stock}, 目标市值: {target_value_per_stock}")
    
    def _adjust2(self, stock_values):
        """
        按指定市值调整持仓
        
        参数:
            stock_values: 字典，键为股票代码，值为目标市值
        """
        if not stock_values:
            return
        
        # 卖出不在目标列表中的股票
        for stock in self.hold_list[:]:
            if stock not in stock_values:
                order_target(stock, 0)
                self.hold_list.remove(stock)
                log.info(f"[{self.name}] 卖出: {stock}")
        
        # 买入或调整目标列表中的股票
        for stock, target_value in stock_values.items():
            order_target_value(stock, target_value)
            if stock not in self.hold_list:
                self.hold_list.append(stock)
                log.info(f"[{self.name}] 买入: {stock}, 目标市值: {target_value}")
    
    def _check(self):
        """
        检查昨日涨停的股票
        
        返回:
            昨日涨停的股票列表
        """
        if not self.hold_list:
            return []
        
        # 获取昨日价格数据
        df = get_price(
            self.hold_list, 
            count=2, 
            end_date=self.context.previous_date, 
            frequency='daily', 
            fields=['close', 'high', 'low', 'pre_close']
        )
        
        # 计算涨停价
        df['limit_up'] = df['pre_close'] * 1.1
        df['limit_up'] = df['limit_up'].apply(lambda x: round(x, 2))
        
        # 判断是否涨停
        df['is_limit_up'] = df['close'] >= df['limit_up'] - 0.01
        
        # 找出昨日涨停的股票
        limit_up_stocks = []
        for stock in self.hold_list:
            if df['is_limit_up'][stock][-1]:
                limit_up_stocks.append(stock)
                log.info(f"[{self.name}] 昨日涨停: {stock}")
        
        return limit_up_stocks
    
    def buy(self, stocks):
        """
        买入指定的股票
        
        参数:
            stocks: 要买入的股票列表
        """
        if not stocks:
            return
        
        # 计算每只股票的目标市值
        total_value = self.context.portfolio.total_value * g.portfolio_value_proportion[self.index]
        target_value_per_stock = total_value / (len(self.hold_list) + len(stocks))
        
        # 调整现有持仓
        for stock in self.hold_list:
            order_target_value(stock, target_value_per_stock)
        
        # 买入新股票
        for stock in stocks:
            order_target_value(stock, target_value_per_stock)
            self.hold_list.append(stock)
            log.info(f"[{self.name}] 买入: {stock}, 目标市值: {target_value_per_stock}")
    
    def is_empty_month(self):
        """
        判断当前月份是否为空仓月份
        
        返回:
            布尔值，True表示当前月份应该空仓
        """
        current_month = self.context.current_dt.month
        return current_month in getattr(self, 'pass_months', [])
