# 导入所需的库
from jqdata import *
import numpy as np
import pandas as pd
import datetime
import math
import talib

# 初始化函数
def initialize(context):
    # 设置动态复权模式
    set_option('use_real_price', True)
    log.info("开启动态复权模式")
    
    # 设置基准
    set_benchmark('000300.XSHG')  # 沪深300作为基准
    
    # 设置滑点
    set_slippage(FixedSlippage(0.002))
    
    # 更新手续费为万一免5
    set_order_cost(OrderCost(open_tax=0, close_tax=0.001, open_commission=0.0001, close_commission=0.0001, min_commission=5), type='stock')
    
    # 设置参数
    context.total_position_pct = 0.7  # 总仓位70%
    context.reserve_position_pct = 0.3  # 备用资金30%
    
    # 设置交易标的（必须最先定义）
    context.stock_list = ['513100.XSHG',  # 纳斯达克ETF
                         '510300.XSHG',  # 沪深300ETF
                         '518880.XSHG']  # 黄金ETF
    
    # 初始化市场状态字典
    context.market_states = {s: 'oscillating' for s in context.stock_list}
    
    # 设置技术指标参数
    context.bias_period = 24  # BIAS周期
    context.adx_period = 13   # ADX周期
    context.atr_period = 12  # ATR周期
    
    # 设置BIAS阈值
    context.bias_threshold = {
        '513100.XSHG': 0.09,   # 纳斯达克ETF阈值±9%
        '510300.XSHG': 0.07,  # 沪深300ETF阈值±7%
        '518880.XSHG': 0.05   # 黄金ETF阈值±5%
    }
    
    # 添加持仓限制参数
    context.position_limits = {
        'oscillating': {'lower': 0.5, 'upper': 1.4},  # 震荡市
        'uptrend': {'lower': 0.7, 'upper': 1.6},      # 上涨趋势
        'downtrend': {'lower': 0.5, 'upper': 1.1}     # 下跌趋势
    }
    
    # 记录初始目标仓位
    context.initial_positions = {}
    
    # 设置风控参数
    context.single_stock_stop_loss = 0.05  # 单个标的5%止损
    context.portfolio_stop_loss = 0.04  # 组合4%止损
    
    # 运行时需要的变量
    context.last_rebalance_date = None  # 上次再平衡时间
    context.stop_trading = False  # 是否停止交易的标志
    
    # 添加货币基金相关参数
    context.money_fund = '511990.XSHG'  # 华宝添益货币ETF
    context.min_fund_amount = 5  # 最小货基交易金额（万元）
    context.money_fund_threshold = 0.02  # 货基交易阈值（2%）
    
    # 添加缓存字典
    context.cache = {
        'technical_indicators': {},
        'last_update_date': None,
        'price_data': {},
        'last_minute': -1,
        'last_money_fund_trade_date': None
    }
    
    # 设置定时任务
    run_daily(market_open, time='9:30', reference_security='000300.XSHG')
    run_daily(check_stop_loss, time='9:31', reference_security='000300.XSHG')
    run_daily(trade_money_fund, time='9:32', reference_security='000300.XSHG')
    run_daily(check_fund_income, time='15:10', reference_security='000300.XSHG')
    
    # 设置最小交易数量
    context.min_trade_amount = 100
    
    # 设置是否开启交易日志
    log.set_level('order', 'info')
    log.set_level('strategy', 'info')
    
    # 简化初始化日志，只记录关键信息
    log.info("=== 策略初始化 ===")
    log.info(f"交易标的: {', '.join(context.stock_list)}")
    log.info(f"仓位配置: 主动仓位 {context.total_position_pct:.0%}, 备用资金 {context.reserve_position_pct:.0%}")
    log.info(f"风控阈值: 单标的 {context.single_stock_stop_loss:.0%}, 组合 {context.portfolio_stop_loss:.0%}")
    
    # 设置是否已经完成初始建仓的标志
    context.initial_position_established = False

# 优化技术指标计算
def update_technical_indicators(context, security):
    """批量更新技术指标"""
    current_date = context.current_dt.date()
    
    # 如果不是新的交易日，且已有缓存，直接返回
    if (context.cache['last_update_date'] == current_date and 
        security in context.cache['technical_indicators']):
        return context.cache['technical_indicators'][security]
    
    # 获取所需的最长周期
    max_period = max(context.bias_period, context.adx_period*2, context.atr_period*2)
    
    # 一次性获取所有需要的数据
    prices = attribute_history(security, max_period, '1d', 
                             ['high', 'low', 'close', 'factor'])
    
    # 计算真实价格
    real_closes = prices['close'] / prices['factor']
    real_highs = prices['high'] / prices['factor']
    real_lows = prices['low'] / prices['factor']
    
    # 计算BIAS
    ma = real_closes[-context.bias_period:].mean()
    latest_price = real_closes[-1]
    bias = (latest_price - ma) / ma
    
    # 计算ADX
    tr = pd.Series(0.0, index=prices.index)
    plus_dm = pd.Series(0.0, index=prices.index)
    minus_dm = pd.Series(0.0, index=prices.index)
    
    for i in range(1, len(real_highs)):
        tr1 = real_highs[i] - real_lows[i]
        tr2 = abs(real_highs[i] - real_closes[i-1])
        tr3 = abs(real_lows[i] - real_closes[i-1])
        tr[i] = max(tr1, tr2, tr3)
        
        up_move = real_highs[i] - real_highs[i-1]
        down_move = real_lows[i-1] - real_lows[i]
        
        if up_move > down_move and up_move > 0:
            plus_dm[i] = up_move
        if down_move > up_move and down_move > 0:
            minus_dm[i] = down_move
    
    tr_ema = tr.ewm(span=context.adx_period, adjust=False).mean()
    plus_di = 100 * plus_dm.ewm(span=context.adx_period, adjust=False).mean() / tr_ema
    minus_di = 100 * minus_dm.ewm(span=context.adx_period, adjust=False).mean() / tr_ema
    dx = 100 * abs(plus_di - minus_di) / (plus_di + minus_di)
    adx = dx.ewm(span=context.adx_period, adjust=False).mean()
    
    # 计算ATR
    atr = tr.ewm(span=context.atr_period, adjust=False).mean()
    
    # 存储计算结果
    if 'technical_indicators' not in context.cache:
        context.cache['technical_indicators'] = {}
    
    context.cache['technical_indicators'][security] = {
        'bias': bias,
        'adx': adx[-1],
        'plus_di': plus_di[-1],
        'minus_di': minus_di[-1],
        'atr': atr[-1]
    }
    context.cache['last_update_date'] = current_date
    
    return context.cache['technical_indicators'][security]

# 修改原有的技术指标计算函数
def calculate_bias(context, security, n=24):
    """使用缓存的技术指标"""
    try:
        return context.cache['technical_indicators'][security]['bias']
    except Exception as e:
        log.error(f"计算BIAS时出错: {str(e)}")
        return 0.0

def calculate_adx(context, security, n=14):
    """使用缓存的技术指标"""
    try:
        indicators = context.cache['technical_indicators'][security]
        return indicators['adx'], indicators['plus_di'], indicators['minus_di']
    except Exception as e:
        log.error(f"计算ADX时出错: {str(e)}")
        return 0.0, 0.0, 0.0

def calculate_atr(context, security, n=14):
    """使用缓存的技术指标"""
    try:
        return context.cache['technical_indicators'][security]['atr']
    except Exception as e:
        log.error(f"计算ATR时出错: {str(e)}")
        return 0.0

# 修改market_open函数
def market_open(context):
    # 在开盘时更新所有标的的技术指标
    for security in context.stock_list:
        try:
            update_technical_indicators(context, security)
        except Exception as e:
            log.error(f"更新{security}技术指标时出错: {str(e)}")
    
    # 检查是否需要执行初始建仓
    if not context.initial_position_established:
        log.info("准备执行初始建仓")
        initial_buy(context)
        context.initial_position_established = True
        return
    
    # 取消未完成的订单
    cancel_open_orders(context)
    
    # 判断是否需要进行季度再平衡
    current_date = context.current_dt.date()
    if should_rebalance(context, current_date):
        log.info("执行季度再平衡")
        rebalance_portfolio(context)
    
    # 更新市场状态
    for security in context.stock_list:
        try:
            old_state = context.market_states[security]
            new_state = get_market_state(context, security)
            context.market_states[security] = new_state
            
            # 只记录状态发生变化时的信息
            if old_state != new_state:
                log.info(f"市场状态变化 - {security}: {old_state} -> {new_state}")
            
            # 只记录持仓发生重大变化的情况
            position = context.portfolio.positions[security].total_amount if security in context.portfolio.positions else 0
            if position > 0:
                position_value = position * get_current_data()[security].last_price
                position_pct = position_value / context.portfolio.total_value
                if position_pct > 0.1:  # 仅记录超过10%仓位的情况
                    log.info(f"重要持仓 - {security}: {position}股, 占比{position_pct:.1%}")
        except Exception as e:
            log.error(f"更新{security}状态失败: {str(e)}")

# 计算证券收益率
def get_security_returns(security):
    """
    计算证券的日收益率，使用真实价格
    参数:
        security: 证券代码
    返回:
        float: 当日收益率
    """
    # 获取当前价格
    current_data = get_current_data()
    current_price = current_data[security].last_price
    
    # 获取昨日收盘价和复权因子
    hist = attribute_history(security, 1, '1d', ['close', 'factor'])
    yesterday_price = hist['close'][0] / hist['factor'][0]
    
    # 计算收益率
    returns = (current_price - yesterday_price) / yesterday_price
    
    return returns

# 检查止损
def check_stop_loss(context):
    for security in context.stock_list:
        returns = get_security_returns(security)
        stop_loss_line = dynamic_stop_loss(context, security)
        
        if returns <= -stop_loss_line:
            log.info(f"触发止损 - {security}: 收益率={returns:.2%}, 止损线={stop_loss_line:.2%}")
            position = context.portfolio.positions.get(security)
            if position and position.total_amount > 0:
                sell_amount = int(position.total_amount * 0.5)
                if sell_amount >= 100:
                    order(security, -sell_amount)
                    log.info(f"分批止损 - {security}: 卖出{sell_amount}股")
                else:
                    order_target(security, 0)
                    log.info(f"完全止损 - {security}: 清仓")
            context.stop_trading = True
            return
            
    portfolio_returns = context.portfolio.returns
    if portfolio_returns <= -context.portfolio_stop_loss:
        log.info(f"触发组合止损: 收益率={portfolio_returns:.2%}")
        context.stop_trading = True

# 判断是否需要再平衡
def should_rebalance(context, current_date):
    """
    判断是否需要再平衡
    1. 每个季度最后一个交易日进行再平衡
    2. 增加节假日处理逻辑
    3. 增加再平衡记录检查
    """
    try:
        # 如果不是季度末月份，直接返回False
        if current_date.month not in [3, 6, 9, 12]:
            return False
            
        # 获取当月的最后一个交易日
        next_month = current_date.month + 1 if current_date.month < 12 else 1
        next_year = current_date.year + (1 if current_date.month == 12 else 0)
        next_month_first_day = datetime.date(next_year, next_month, 1)
        current_month_first_day = datetime.date(current_date.year, current_date.month, 1)
        
        # 获取当月所有交易日
        month_trading_days = get_trade_days(start_date=current_month_first_day, 
                                         end_date=next_month_first_day - datetime.timedelta(days=1))
        
        if len(month_trading_days) == 0:
            log.warning(f"未找到{current_date.year}年{current_date.month}月的交易日")
            return False
            
        last_trading_day = month_trading_days[-1].date()
        
        # 检查是否是当月最后一个交易日
        if current_date == last_trading_day:
            # 检查是否已经在本月执行过再平衡
            if context.last_rebalance_date is not None:
                if (context.last_rebalance_date.year == current_date.year and 
                    context.last_rebalance_date.month == current_date.month):
                    log.info(f"本月已执行过再平衡({context.last_rebalance_date})")
                    return False
                    
            log.info(f"触发季度再平衡: {current_date}")
            return True
            
        return False
        
    except Exception as e:
        log.error(f"判断再平衡日期时出错: {str(e)}")
        return False

# 计算风险平价权重
def calculate_risk_parity_weights(context):
    """
    计算考虑夏普比率的优化权重
    1. 首先计算基础风险平价权重
    2. 结合夏普比率进行权重优化
    3. 保持总权重为1
    """
    # 初始化数据存储
    volatilities = {}
    returns = {}
    sharpe_ratios = {}
    
    # 计算每个ETF的60日波动率、收益率和夏普比率
    for security in context.stock_list:
        try:
            # 获取过去60个交易日的收盘价
            price_data = get_price(security, count=60, frequency='daily', fields=['close'])
            close = price_data['close']
            
            # 计算日收益率
            daily_returns = close.pct_change().dropna()
            
            # 计算年化收益率
            annual_return = (1 + daily_returns.mean()) ** 252 - 1
            
            # 计算年化波动率
            volatility = daily_returns.std() * np.sqrt(252)
            
            # 计算夏普比率 (假设无风险利率为3%)
            risk_free_rate = 0.03
            sharpe_ratio = (annual_return - risk_free_rate) / volatility if volatility > 0 else 0
            
            # 存储计算结果
            volatilities[security] = volatility
            returns[security] = annual_return
            sharpe_ratios[security] = max(0, sharpe_ratio)  # 确保夏普比率非负
            
        except Exception as e:
            log.error(f"计算{security}指标时出错: {str(e)}")
            # 使用默认值
            volatilities[security] = 0.2
            returns[security] = 0.05
            sharpe_ratios[security] = 0.1
    
    # 步骤1：计算基础风险平价权重
    inv_vol_sum = sum(1.0 / vol for vol in volatilities.values())
    risk_parity_weights = {security: (1.0 / vol) / inv_vol_sum 
                          for security, vol in volatilities.items()}
    
    # 步骤2：计算综合权重因子
    # Weight_i = (1/σ_i × Sharpe_i) / Σ(1/σ_j × Sharpe_j)
    weight_factors = {}
    factor_sum = 0
    
    for security in context.stock_list:
        factor = (1.0 / volatilities[security]) * sharpe_ratios[security]
        weight_factors[security] = factor
        factor_sum += factor
    
    # 计算最终权重
    final_weights = {}
    if factor_sum > 0:
        for security in context.stock_list:
            # 基础权重占70%，夏普比率优化权重占30%
            base_weight = risk_parity_weights[security] * 0.7
            sharpe_weight = (weight_factors[security] / factor_sum) * 0.3
            final_weights[security] = base_weight + sharpe_weight
    else:
        # 如果无法计算夏普比率优化权重，使用纯风险平价权重
        final_weights = risk_parity_weights
    
    # 确保权重之和为1
    weight_sum = sum(float(w) for w in final_weights.values())
    
    if weight_sum > 1:
        scale = (1 - context.reserve_position_pct) / weight_sum
        final_weights = {s: w * scale for s, w in final_weights.items()}
        log.info(f"权重总和超过1，进行等比例缩减，缩减系数: {scale:.3f}")
    
    # 更新备用资金比例
    context.reserve_position_pct = 1 - weight_sum
    log.info(f"调整后备用资金比例: {context.reserve_position_pct:.1%}")
    
    # 调整持仓
    total_position_value = context.portfolio.total_value * (1 - context.reserve_position_pct)
    for security in context.stock_list:
        target_value = total_position_value * final_weights[security]
        order_target_value(security, target_value)
        log.info(f"调整 {security} 目标市值至: {target_value:.2f}, 权重: {final_weights[security]:.2%}")
    
    # 更新最后再平衡时间
    context.last_rebalance_date = context.current_dt.date()
    
    # 记录权重分配信息
    log.info("权重分配详情:")
    for security in context.stock_list:
        log.info(f"{security}:")
        log.info(f"  年化收益率: {returns[security]:.2%}")
        log.info(f"  波动率: {volatilities[security]:.2%}")
        log.info(f"  夏普比率: {sharpe_ratios[security]:.2f}")
        log.info(f"  最终权重: {final_weights[security]:.2%}")
    
    return final_weights

# 再平衡投资组合
def rebalance_portfolio(context):
    """
    优化的再平衡函数，使用动态调整比例
    """
    try:
        # 计算基础权重（结合风险平价和夏普比率）
        weights = calculate_risk_parity_weights(context)
        
        # 记录原始备用资金比例
        original_reserve_pct = context.reserve_position_pct
        current_reserve_pct = original_reserve_pct
        
        # 动态调整备用资金比例
        up_count = sum(1 for s in context.stock_list if context.market_states[s] == 'uptrend')
        down_count = sum(1 for s in context.stock_list if context.market_states[s] == 'downtrend')
        
        # 根据市场状态调整备用资金
        if up_count == len(context.stock_list):
            current_reserve_pct = max(0.1, original_reserve_pct * 0.5)
            log.info(f'全面上涨行情，备用资金调整为{current_reserve_pct:.1%}')
        elif down_count == len(context.stock_list):
            current_reserve_pct = min(0.5, original_reserve_pct * 1.5)
            log.info(f'全面下跌行情，备用资金调整为{current_reserve_pct:.1%}')
        
        # 动态调整各个标的的配置
        total_adjustment = 0
        adjustments = {}
        
        # 计算动态调整
        for security in context.stock_list:
            try:
                market_state = context.market_states[security]
                
                if market_state == 'uptrend':
                    # 上涨趋势的动态超配
                    base_extra = min(0.15, current_reserve_pct)  # 基础15%超配
                    extra_ratio = calculate_dynamic_allocation_ratio(context, security, base_extra)
                    extra_weight = extra_ratio * weights[security]
                    
                    adjustments[security] = extra_weight
                    total_adjustment += extra_weight
                    
                elif market_state == 'downtrend':
                    # 下跌趋势的动态减配
                    base_reduction = min(0.10, weights[security])  # 基础10%减配
                    reduction_ratio = calculate_dynamic_allocation_ratio(context, security, base_reduction)
                    reduction = reduction_ratio * weights[security]
                    
                    adjustments[security] = -reduction
                    total_adjustment -= reduction
                
                # 记录调整信息
                if security in adjustments:
                    log.info(f"{security} {market_state}调整: {adjustments[security]:.1%}")
                
            except Exception as e:
                log.error(f"计算{security}配置调整时出错: {str(e)}")
                adjustments[security] = 0
        
        # 实际调整权重
        available_reserve = current_reserve_pct - 0.05  # 保留5%最小备用资金
        if total_adjustment > 0:
            scale_factor = min(1.0, available_reserve / total_adjustment)
            for security in context.stock_list:
                if security in adjustments:
                    actual_adjustment = adjustments[security] * scale_factor
                    weights[security] += actual_adjustment
                    current_reserve_pct -= actual_adjustment
                    
                    if abs(actual_adjustment) >= 0.001:
                        log.info(f"{security} {'超配' if actual_adjustment > 0 else '减配'} {abs(actual_adjustment):.1%}")
        
        # 确保权重非负且总和不超过1
        weights = {s: max(0.0, w) for s, w in weights.items()}
        weight_sum = sum(float(w) for w in weights.values())
        
        if weight_sum > 1:
            scale = (1 - current_reserve_pct) / weight_sum
            weights = {s: w * scale for s, w in weights.items()}
            log.info(f"权重总和超过1，进行等比例缩减，缩减系数: {scale:.3f}")
        
        # 更新备用资金比例
        context.reserve_position_pct = current_reserve_pct
        log.info(f"调整后备用资金比例: {current_reserve_pct:.1%}")
        
        # 调整持仓
        total_position_value = context.portfolio.total_value * (1 - current_reserve_pct)
        for security in context.stock_list:
            target_value = total_position_value * weights[security]
            order_target_value(security, target_value)
            log.info(f"调整 {security} 目标市值至: {target_value:.2f}, 权重: {weights[security]:.2%}")
        
        # 更新最后再平衡时间
        context.last_rebalance_date = context.current_dt.date()
        
    except Exception as e:
        log.error(f"执行再平衡时发生错误: {str(e)}")
        raise

def cancel_open_orders(context):
    """
    取消所有未完成的订单
    """
    orders = get_open_orders()
    for order in orders.values():
        cancel_order(order)
        log.info(f"取消未完成订单: {order.security}, 委托价格: {order.price}, 委托数量: {order.amount}")

def initial_buy(context):
    """
    初始建仓函数
    使用风险平价模型计算权重并进行首次建仓
    """
    log.info("开始执行初始建仓")
    
    try:
        # 计算风险平价权重
        weights = calculate_risk_parity_weights(context)
        weight_str = ', '.join([f"{security}: {weight:.2%}" for security, weight in weights.items()])
        log.info(f"初始风险平价权重: {weight_str}")
        
        # 获取当前市场状态并调整权重
        for security in context.stock_list:
            context.market_states[security] = get_market_state(context, security)
            log.info(f"{security} 初始市场状态: {context.market_states[security]}")
            
            # 根据市场状态调整权重
            if context.market_states[security] == 'uptrend':
                # 上涨趋势超配8%
                weights[security] = weights.get(security, 0) + 0.08
                # 从其他标的中等比例减少权重
                other_securities = [s for s in context.stock_list if s != security]
                weight_reduction = 0.08 / len(other_securities)
                for other_security in other_securities:
                    weights[other_security] = weights.get(other_security, 0) - weight_reduction
            elif context.market_states[security] == 'downtrend':
                # 下跌趋势减配5%
                reduction = min(weights.get(security, 0) * 0.05, 0.05)
                weights[security] = weights.get(security, 0) - reduction
                # 将减配的资金加入到备用资金中
                context.reserve_position_pct += reduction
        
        # 计算权重总和并归一化
        weight_sum = sum(weight for weight in weights.values())
        log.info(f"调整后权重总和: {weight_sum:.2%}")
        
        if weight_sum > 1:
            # 对权重进行归一化
            weights = {security: weight/weight_sum for security, weight in weights.items()}
            weight_str = ', '.join([f"{security}: {weight:.2%}" for security, weight in weights.items()])
            log.info(f"归一化后的权重: {weight_str}")
        
        # 计算每个标的的目标金额
        total_value = context.portfolio.total_value
        available_cash = context.portfolio.available_cash
        
        # 执行建仓
        for security in context.stock_list:
            try:
                # 计算目标金额
                target_value = total_value * context.total_position_pct * weights.get(security, 0)
                
                # 获取当前价格
                current_data = get_current_data()[security]
                if current_data.paused:
                    log.warning(f"{security} 当前暂停交易，跳过建仓")
                    continue
                    
                current_price = current_data.last_price
                
                # 计算购买数量（确保是100的整数倍）
                amount = int(target_value / current_price / 100) * 100
                
                if amount >= 100:  # 确保购买数量至少为100股
                    order_value = amount * current_price
                    if order_value <= available_cash:  # 确保资金充足
                        order(security, amount)
                        log.info(f"初始建仓 {security}: 数量={amount}, 价格={current_price:.3f}, 金额={order_value:.2f}")
                    else:
                        log.warning(f"初始建仓 {security}: 资金不足，需要{order_value:.2f}，可用{available_cash:.2f}")
                else:
                    log.warning(f"初始建仓 {security}: 计算购买数量小于100股，跳过购买")
            except Exception as e:
                log.error(f"处理标的 {security} 时发生错误: {str(e)}")
                continue
                
        log.info("初始建仓完成")
    except Exception as e:
        log.error(f"初始建仓过程中发生错误: {str(e)}")
        raise

def trade_money_fund(context):
    """优化后的货币基金交易函数"""
    try:
        current_date = context.current_dt.date()
        
        # 如果今天已经交易过，直接返回
        if context.cache['last_money_fund_trade_date'] == current_date:
            return
        
        # 计算可用于货基的资金
        total_value = context.portfolio.total_value
        min_cash_buffer = total_value * 0.01  # 提高现金缓冲至1%
        available_cash = context.portfolio.available_cash - min_cash_buffer
        
        # 提高最小交易金额阈值
        min_trade_amount = context.min_fund_amount * 10000  # 提高到10万
        if available_cash < min_trade_amount:
            return
        
        try:
            # 获取货基价格和交易状态
            current_data = get_current_data()[context.money_fund]
            if current_data and not current_data.paused:
                current_price = current_data.last_price
                
                # 获取当前货基持仓
                position = context.portfolio.positions.get(context.money_fund)
                current_amount = position.total_amount if position is not None else 0
                
                # 计算目标持仓
                target_amount = int(available_cash / (current_price * 100)) * 100
                
                # 提高交易阈值，减少频繁交易
                if current_amount > 0:
                    current_value = current_amount * current_price
                    target_value = target_amount * current_price
                    change_pct = abs(target_value - current_value) / current_value if current_value > 0 else float('inf')
                    
                    if change_pct < 0.05:  # 提高到5%的变动阈值
                        return
                
                # 执行交易
                if target_amount >= 100:
                    # 检查是否是季度末
                    is_quarter_end = current_date.month in [3, 6, 9, 12] and should_rebalance(context, current_date)
                    
                    # 如果是季度末，保留更多现金以备再平衡使用
                    if is_quarter_end:
                        target_amount = int(target_amount * 0.5)  # 季度末只使用50%的可用资金
                    
                    if target_amount >= 100:  # 确保交易数量仍然有效
                        order_target(context.money_fund, target_amount)
                        context.cache['last_money_fund_trade_date'] = current_date
                        # 只记录重要的货基交易
                        if abs(target_amount - current_amount) > 1000:
                            log.info(f"货基调整: {target_amount-current_amount:+d}份, 触发条件: 变动比例={change_pct:.1%}")
                    
        except Exception as e:
            log.error(f"货基交易异常: {str(e)}")
            
    except Exception as e:
        log.error(f"货基交易过程中发生错误: {str(e)}")

def check_fund_income(context):
    """
    检查货基收益情况
    """
    try:
        # 只在收盘前检查一次
        if context.current_dt.time() != datetime.time(15, 10):
            return
            
        if context.money_fund in context.portfolio.positions:
            position = context.portfolio.positions[context.money_fund]
            if position.total_amount > 0:
                current_data = get_current_data()[context.money_fund]
                current_price = current_data.last_price
                
                cost = position.avg_cost * position.total_amount
                current_value = current_price * position.total_amount
                daily_return = (current_value - cost) / cost
                
                # 只在收益率超过0.01%时记录日志
                if abs(daily_return) > 0.0001:
                    log.info(f"货基当日收益: {daily_return:.4%}")
                    
    except Exception as e:
        log.error(f"检查货基收益时发生错误: {str(e)}")

def get_market_state(context, security):
    """
    根据技术指标判断市场状态
    返回: 'uptrend', 'downtrend', 或 'oscillating'
    """
    try:
        # 更新技术指标
        indicators = update_technical_indicators(context, security)
        
        # 获取BIAS和ADX值
        bias = indicators['bias']
        adx = indicators['adx']
        plus_di = indicators['plus_di']
        minus_di = indicators['minus_di']
        
        # 获取该证券的BIAS阈值
        bias_threshold = context.bias_threshold[security]
        
        # 趋势判断标准
        TREND_THRESHOLD = 25  # ADX高于25表示存在明显趋势
        
        # 判断市场状态
        if adx > TREND_THRESHOLD:  # 存在明显趋势
            if plus_di > minus_di:  # 上升趋势
                if bias > bias_threshold:  # BIAS过高，可能超买
                    return 'oscillating'
                return 'uptrend'
            else:  # 下降趋势
                if bias < -bias_threshold:  # BIAS过低，可能超卖
                    return 'oscillating'
                return 'downtrend'
        else:  # ADX较低，无明显趋势
            if abs(bias) > bias_threshold:  # BIAS超出阈值
                if bias > bias_threshold:  # 可能超买
                    return 'downtrend'
                else:  # 可能超卖
                    return 'uptrend'
            return 'oscillating'  # 震荡市场
            
    except Exception as e:
        log.error(f"判断市场状态时出错: {str(e)}")
        return 'oscillating'  # 发生错误时默认返回震荡状态

def calculate_price_trend(context, security, period=20):
    """
    计算价格趋势
    返回: float, 正值表示上涨趋势，负值表示下跌趋势
    """
    try:
        # 获取历史价格数据
        hist_data = attribute_history(security, period, '1d', ['close'])
        prices = hist_data['close']
        
        # 计算价格变化率
        price_change = (prices[-1] - prices[0]) / prices[0]
        
        return price_change
        
    except Exception as e:
        log.error(f"计算价格趋势时出错: {str(e)}")
        return 0.0

def calculate_volume_trend(context, security, period=20):
    """
    计算成交量趋势
    返回: float, 大于1表示放量，小于1表示缩量
    """
    try:
        # 获取历史成交量数据
        hist_data = attribute_history(security, period, '1d', ['volume'])
        volumes = hist_data['volume']
        
        # 计算最近5日平均成交量与20日平均成交量的比值
        recent_avg = np.mean(volumes[-5:])
        total_avg = np.mean(volumes)
        
        return recent_avg / total_avg if total_avg > 0 else 1.0
        
    except Exception as e:
        log.error(f"计算成交量趋势时出错: {str(e)}")
        return 1.0

def calculate_volatility(context, security, period=20):
    """
    计算历史波动率
    """
    try:
        # 获取历史价格数据
        hist_data = attribute_history(security, period, '1d', ['close'])
        prices = hist_data['close']
        
        # 计算日收益率
        returns = np.diff(np.log(prices))
        
        # 计算波动率（年化）
        volatility = np.std(returns) * np.sqrt(252)
        
        return volatility
        
    except Exception as e:
        log.error(f"计算波动率时出错: {str(e)}")
        return 0.02  # 返回默认波动率

def get_volume_ratio(context, security, period=20):
    """
    计算量比
    返回: float, 当前成交量与过去N日平均成交量的比值
    """
    try:
        # 获取当前成交量（使用当日分钟数据）
        current_volume_data = get_price(security, count=1, frequency='1m', fields=['volume'])
        if current_volume_data is None or len(current_volume_data) == 0:
            return 1.0
        current_volume = current_volume_data['volume'].iloc[-1]
        
        # 获取历史成交量
        hist_data = attribute_history(security, period, '1d', ['volume'])
        if hist_data is None or len(hist_data) == 0:
            return 1.0
        avg_volume = np.mean(hist_data['volume'])
        
        return current_volume / avg_volume if avg_volume > 0 else 1.0
        
    except Exception as e:
        log.error(f"计算量比时出错: {str(e)}")
        return 1.0

def check_trade_conditions(context, security, amount, trade_type):
    """
    检查交易条件
    """
    try:
        # 检查是否停止交易
        if context.stop_trading:
            return False
            
        # 检查交易数量是否合法
        if abs(amount) < context.min_trade_amount:
            return False
            
        # 获取当前价格和交易状态
        current_data = get_current_data()[security]
        if current_data.paused:
            return False
            
        # 检查资金是否充足（买入时）
        if trade_type == 'buy':
            cost = amount * current_data.last_price * (1 + 0.002)  # 考虑滑点
            if cost > context.portfolio.available_cash:
                return False
                
        # 检查持仓是否充足（卖出时）
        if trade_type == 'sell':
            position = context.portfolio.positions.get(security)
            if not position or position.total_amount < abs(amount):
                return False
                
        return True
        
    except Exception as e:
        log.error(f"检查交易条件时出错: {str(e)}")
        return False

def calculate_trend_intensity(context, security):
    """
    计算趋势强度指标，用于动态调整超配/减配幅度
    返回: (float, float) - (趋势强度, 趋势确信度)
    """
    try:
        # 获取价格数据
        price_data = get_price(security, count=60, frequency='daily', 
                             fields=['close', 'high', 'low', 'volume'])
        close = price_data['close']
        high = price_data['high']
        low = price_data['low']
        volume = price_data['volume']
        
        # 计算技术指标
        ma20 = pd.Series(close).rolling(20).mean()
        ma60 = pd.Series(close).rolling(60).mean()
        
        # 计算趋势强度指标
        price_trend = (close[-1] / close[-20] - 1) * 100  # 20日价格趋势
        ma_trend = (ma20[-1] / ma20[-20] - 1) * 100      # 20日均线趋势
        
        # 计算波动率
        returns = pd.Series(close).pct_change()
        volatility = returns.std() * np.sqrt(252)
        
        # 计算成交量趋势
        volume_ma = pd.Series(volume).rolling(20).mean()
        volume_trend = volume[-1] / volume_ma[-1]
        
        # 计算趋势强度
        trend_strength = 0
        
        # 价格位置判断
        if close[-1] > ma20[-1] > ma60[-1]:  # 多头排列
            trend_strength += 1
        elif close[-1] < ma20[-1] < ma60[-1]:  # 空头排列
            trend_strength -= 1
            
        # 趋势强度判断
        if price_trend > 0:
            trend_strength += price_trend / 2  # 上涨趋势加分
        else:
            trend_strength += price_trend  # 下跌趋势扣分
            
        # 成交量确认
        if volume_trend > 1.2 and price_trend > 0:  # 放量上涨
            trend_strength *= 1.3
        elif volume_trend > 1.2 and price_trend < 0:  # 放量下跌
            trend_strength *= 1.5
            
        # 计算趋势确信度
        confidence = min(1.0, max(0.1, abs(trend_strength) / 10))
        
        return trend_strength, confidence
        
    except Exception as e:
        log.error(f"计算趋势强度时出错: {str(e)}")
        return 0, 0.5

def calculate_dynamic_allocation_ratio(context, security, base_ratio):
    """
    计算动态调整比例
    base_ratio: 基础调整比例
    返回: float - 最终调整比例
    """
    try:
        # 获取趋势强度和确信度
        trend_strength, confidence = calculate_trend_intensity(context, security)
        
        # 获取夏普比率
        price_data = get_price(security, count=60, frequency='daily', fields=['close'])
        returns = price_data['close'].pct_change().dropna()
        volatility = returns.std() * np.sqrt(252)
        annual_return = (1 + returns.mean()) ** 252 - 1
        sharpe = (annual_return - 0.03) / volatility if volatility > 0 else 0
        
        # 基础调整系数
        adjustment = 1.0
        
        # 根据趋势强度调整
        if trend_strength > 0:  # 上涨趋势
            # 强势上涨时更激进
            if trend_strength > 5:
                adjustment *= (1 + trend_strength * 0.1)  # 最多可增加50%
            else:
                adjustment *= (1 + trend_strength * 0.05)
        else:  # 下跌趋势
            # 强势下跌时更保守
            if trend_strength < -5:
                adjustment *= (1 + abs(trend_strength) * 0.15)  # 最多可增加75%
            else:
                adjustment *= (1 + abs(trend_strength) * 0.1)
        
        # 根据夏普比率调整
        if trend_strength > 0:  # 上涨趋势
            if sharpe > 1.5:
                adjustment *= 1.5  # 高夏普比率时更激进
            elif sharpe > 0:
                adjustment *= (1 + sharpe * 0.2)
            else:
                adjustment *= 0.8  # 负夏普比率时更保守
        else:  # 下跌趋势
            if sharpe < -0.5:
                adjustment *= 1.3  # 显著负夏普比率时加大减配
            elif sharpe < 0:
                adjustment *= (1 + abs(sharpe) * 0.2)
            else:
                adjustment *= 0.9  # 正夏普比率时减少减配
        
        # 根据确信度调整
        adjustment *= (0.5 + confidence * 0.5)  # 确信度影响50%
        
        # 计算最终调整比例
        final_ratio = base_ratio * adjustment
        
        # 设置上下限
        if trend_strength > 0:  # 上涨趋势
            final_ratio = min(0.35, max(0.05, final_ratio))  # 上涨最高35%超配
        else:  # 下跌趋势
            final_ratio = min(0.25, max(0.03, final_ratio))  # 下跌最高25%减配
        
        return final_ratio
        
    except Exception as e:
        log.error(f"计算动态调整比例时出错: {str(e)}")
        return base_ratio

def dynamic_stop_loss(context, security):
    # 根据历史波动率动态调整止损线
    vol = calculate_volatility(context, security)
    base_stop = 0.05
    stop_loss = max(0.03, min(0.08, base_stop * (1 + vol / 0.02)))
    return stop_loss