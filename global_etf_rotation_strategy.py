#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
全球ETF轮动策略 - 基于聚宽平台
"""

import numpy as np
import pandas as pd
import datetime
import math
import talib
from jqdata import *

def initialize(context):
    """
    初始化函数，设置基本参数和全局变量
    """
    # 设置动态复权模式
    set_option('use_real_price', True)

    # 设置日志级别
    log.set_level('strategy', 'info')
    log.set_level('order', 'info')

    # 设置基准为沪深300
    set_benchmark('000300.XSHG')

    # 设置固定滑点
    set_slippage(FixedSlippage(0.002))

    # 设置ETF交易手续费：卖出时0.1%印花税，买卖双边万分之一佣金，最低佣金0元
    set_order_cost(OrderCost(open_tax=0, close_tax=0.001, open_commission=0.0001, close_commission=0.0001, min_commission=0), type='stock')

    # 投资标的与货币基金
    context.stock_list = ['510300.XSHG', '513100.XSHG', '513000.XSHG', '513730.XSHG', '513080.XSHG', '513030.XSHG', '518880.XSHG']
    context.money_fund = '511880.XSHG'  # 银华日利ETF
    context.min_trade_amount = 100  # 最小交易股数

    # 参数加载占位符 - 这些参数将从 final_optimized_params.json 文件加载
    # 目前使用默认值作为占位符
    context.bias_period = 20
    context.adx_period = 14
    context.atr_period = 14
    context.momentum_m1_period = 20  # 对应1个月，约20个交易日
    context.momentum_m3_period = 60  # 对应3个月
    context.momentum_m6_period = 120  # 对应6个月
    context.volatility_period = 60  # 波动率计算周期
    context.sp500_sma_period = 200  # S&P 500均线周期
    context.sp500_buffer = 0.02  # S&P 500均线缓冲带
    context.zscore_winsorize_limit = 3.0  # Z-score极端值处理阈值
    context.weight_momentum = 0.8  # 动量因子在综合评分中的权重
    context.weight_volatility = 0.2  # 波动率因子在综合评分中的权重
    context.crisis_mode_stock_top_n = 2  # 风险模式下股票类ETF持仓数量
    context.crisis_mode_stock_max_exposure = 0.5  # 风险模式下股票类ETF最大总仓位
    context.crisis_mode_gold_strong_momentum_threshold = 0.03  # 风险模式下黄金强动量阈值，例如3个月收益率>3%
    context.crisis_mode_gold_weak_momentum_threshold = 0.00  # 风险模式下黄金弱动量阈值
    context.crisis_mode_gold_pos_strong = 0.6  # 风险模式下黄金强动量仓位
    context.crisis_mode_gold_pos_weak = 0.3  # 风险模式下黄金弱动量仓位
    context.crisis_mode_gold_pos_negative = 0.1  # 风险模式下黄金负动量仓位
    context.normal_mode_top_n = 3  # 正常模式下持仓数量

    # S&P 500数据获取方案
    # 使用513500.XSHG（南方恒指ETF）作为S&P 500指数的替代
    # 注意：该ETF可能存在跟踪误差和汇率影响，但作为A股市场上可获取的跟踪美国市场的ETF，是相对合适的选择
    # 如果在update_sp500_sma函数中获取该ETF数据失败，会自动回退到使用沪深300作为替代
    context.sp500_ticker = '513500.XSHG'
    log.info("设置513500.XSHG作为S&P 500指数的替代，注意其可能存在跟踪误差和汇率影响")

    # 缓存系统初始化
    context.cache = {
        'technical_indicators': {},  # {'ETF代码': {'指标名': 值, ...}}
        'price_data': {},  # {'ETF代码': pd.DataFrame(历史价格)}
        'zscore_stats': {},  # {'date': {'动量均值':val, '动量标准差':val, '波动率均值':val, '波动率标准差':val}}
        'sp500_sma': {},  # {'date': sma_value}
    }

    # 初始化最后调仓日期
    context.last_rebalance_date = None

    # 预加载历史数据
    preload_historical_data(context)

    # 设置定时任务
    run_monthly(rebalance_on_schedule, 1, time='09:31', reference_security='000300.XSHG')
    run_daily(market_open, time='09:30')

def preload_historical_data(context):
    """
    为策略中的每个ETF预加载足够长的历史数据
    """
    try:
        # 计算需要的历史数据长度
        max_period = max(context.momentum_m6_period, context.volatility_period, context.sp500_sma_period)
        history_days = max_period + 50  # 添加一些冗余
        min_required_days = 20  # 最小所需数据天数

        log.info(f"开始预加载历史数据，回溯 {history_days} 个交易日")

        # 为每个ETF加载历史数据
        for security in context.stock_list:
            try:
                # 获取历史数据
                hist_data = get_price(
                    security,
                    count=history_days,
                    frequency='daily',
                    fields=['high', 'low', 'close', 'factor', 'volume']
                )

                # 检查数据是否足够
                if hist_data is None or hist_data.empty:
                    log.warning(f"{security} 无法获取历史数据，可能尚未上市")
                    # 初始化空的技术指标
                    if security not in context.cache['technical_indicators']:
                        context.cache['technical_indicators'][security] = {}
                    context.cache['technical_indicators'][security]['数据不足'] = True
                    continue

                if len(hist_data) < min_required_days:
                    log.warning(f"{security} 历史数据不足 ({len(hist_data)} 天 < {min_required_days} 天)，可能是新上市ETF")
                    # 标记数据不足但仍然尝试计算
                    if security not in context.cache['technical_indicators']:
                        context.cache['technical_indicators'][security] = {}
                    context.cache['technical_indicators'][security]['数据不足'] = True

                # 存储到缓存
                context.cache['price_data'][security] = hist_data

                # 计算初始技术指标
                calculate_initial_indicators(context, security, hist_data)

                log.info(f"成功预加载 {security} 的历史数据和初始指标，数据长度: {len(hist_data)} 天")
            except Exception as e:
                log.error(f"预加载 {security} 数据时出错: {str(e)}")
                # 确保即使出错也初始化技术指标缓存
                if security not in context.cache['technical_indicators']:
                    context.cache['technical_indicators'][security] = {}
                context.cache['technical_indicators'][security]['数据不足'] = True

        # 预加载S&P500(或替代指数)的历史数据
        try:
            sp500_hist = get_price(
                context.sp500_ticker,
                count=history_days,
                frequency='daily',
                fields=['close', 'factor']
            )

            # 检查数据是否足够
            if sp500_hist is None or sp500_hist.empty or len(sp500_hist) < context.sp500_sma_period // 2:
                log.warning(f"{context.sp500_ticker} 数据不足，尝试使用沪深300作为替代")
                sp500_hist = get_price(
                    '000300.XSHG',
                    count=history_days,
                    frequency='daily',
                    fields=['close', 'factor']
                )
                log.info(f"使用沪深300替代，获取到 {len(sp500_hist)} 天数据")

            # 计算S&P500的SMA
            real_closes = sp500_hist['close'] * sp500_hist['factor']
            sma = talib.SMA(real_closes.values, timeperiod=context.sp500_sma_period)

            # 存储最新的SMA值
            latest_date = sp500_hist.index[-1].date()
            context.cache['sp500_sma'][latest_date] = sma[-1]

            log.info(f"成功预加载 {context.sp500_ticker} (S&P500替代品)的历史数据和SMA，数据长度: {len(sp500_hist)} 天")
        except Exception as e:
            log.error(f"预加载 {context.sp500_ticker} 数据时出错: {str(e)}")
            # 尝试使用沪深300作为备选
            try:
                sp500_hist = get_price(
                    '000300.XSHG',
                    count=history_days,
                    frequency='daily',
                    fields=['close', 'factor']
                )

                # 计算SMA
                real_closes = sp500_hist['close'] * sp500_hist['factor']
                sma = talib.SMA(real_closes.values, timeperiod=context.sp500_sma_period)

                # 存储最新的SMA值
                latest_date = sp500_hist.index[-1].date()
                context.cache['sp500_sma'][latest_date] = sma[-1]

                log.warning(f"使用沪深300作为S&P500替代，成功加载数据，长度: {len(sp500_hist)} 天")
            except Exception as e2:
                log.error(f"预加载沪深300作为替代时也出错: {str(e2)}")

    except Exception as e:
        log.error(f"预加载历史数据过程中出错: {str(e)}")

def calculate_initial_indicators(context, security, hist_data):
    """
    计算并缓存ETF的初始技术指标
    """
    try:
        # 初始化该证券的技术指标缓存
        if security not in context.cache['technical_indicators']:
            context.cache['technical_indicators'][security] = {}

        # 检查数据是否为空
        if hist_data is None or hist_data.empty:
            log.warning(f"{security} 历史数据为空，无法计算技术指标")
            # 设置默认值
            context.cache['technical_indicators'][security]['momentum_m1'] = -float('inf')
            context.cache['technical_indicators'][security]['momentum_m3'] = -float('inf')
            context.cache['technical_indicators'][security]['momentum_m6'] = -float('inf')
            context.cache['technical_indicators'][security]['vol_60d'] = 0.3  # 较高的默认波动率
            context.cache['technical_indicators'][security]['数据不足'] = True
            return

        # 计算复权收盘价
        real_closes = hist_data['close'] * hist_data['factor']
        context.cache['technical_indicators'][security]['real_closes'] = real_closes

        # 绝对最小观察期
        absolute_min_obs = 5  # 至少5个交易日的数据

        # 计算动量指标
        # 1个月动量
        if len(real_closes) < absolute_min_obs:
            log.warning(f"{security} 数据长度 {len(real_closes)} 极度不足（少于{absolute_min_obs}天），无法可靠计算M1动量")
            m1_return = -float('inf')
        else:
            m1_return = real_closes[-1] / real_closes[-min(len(real_closes), context.momentum_m1_period)] - 1
            if len(real_closes) < context.momentum_m1_period:
                log.info(f"{security} 数据长度 {len(real_closes)} 小于理想的M1动量周期 {context.momentum_m1_period}，但仍基于可用数据计算")
        context.cache['technical_indicators'][security]['momentum_m1'] = m1_return

        # 3个月动量
        if len(real_closes) < absolute_min_obs:
            log.warning(f"{security} 数据长度 {len(real_closes)} 极度不足（少于{absolute_min_obs}天），无法可靠计算M3动量")
            m3_return = -float('inf')
        else:
            m3_return = real_closes[-1] / real_closes[-min(len(real_closes), context.momentum_m3_period)] - 1
            if len(real_closes) < context.momentum_m3_period:
                log.info(f"{security} 数据长度 {len(real_closes)} 小于理想的M3动量周期 {context.momentum_m3_period}，但仍基于可用数据计算")
        context.cache['technical_indicators'][security]['momentum_m3'] = m3_return

        # 6个月动量
        if len(real_closes) < absolute_min_obs:
            log.warning(f"{security} 数据长度 {len(real_closes)} 极度不足（少于{absolute_min_obs}天），无法可靠计算M6动量")
            m6_return = -float('inf')
        else:
            m6_return = real_closes[-1] / real_closes[-min(len(real_closes), context.momentum_m6_period)] - 1
            if len(real_closes) < context.momentum_m6_period:
                log.info(f"{security} 数据长度 {len(real_closes)} 小于理想的M6动量周期 {context.momentum_m6_period}，但仍基于可用数据计算")
        context.cache['technical_indicators'][security]['momentum_m6'] = m6_return

        # 计算波动率
        returns = real_closes.pct_change().dropna()
        if len(returns) < absolute_min_obs:
            log.warning(f"{security} 有效收益率数据点 {len(returns)} 极度不足（少于{absolute_min_obs}点），无法可靠计算波动率")
            vol_60d = 0.2  # 默认波动率20%
        else:
            # 确保只使用最后 volatility_period 个有效收益率数据点进行计算
            vol_60d = returns[-min(len(returns), context.volatility_period):].std() * math.sqrt(252)  # 年化
            if len(returns) < context.volatility_period:
                log.info(f"{security} 有效收益率数据点 {len(returns)} 小于理想的波动率周期 {context.volatility_period}，但仍基于可用数据计算")
        context.cache['technical_indicators'][security]['vol_60d'] = vol_60d

        # 只有在数据极度不足时才标记为数据不足
        if len(real_closes) < absolute_min_obs:
            context.cache['technical_indicators'][security]['数据不足'] = True
            log.warning(f"{security} 被标记为数据极度不足，可能影响评分")
        else:
            # 确保移除可能存在的数据不足标记
            if '数据不足' in context.cache['technical_indicators'][security]:
                del context.cache['technical_indicators'][security]['数据不足']

    except Exception as e:
        log.error(f"计算 {security} 初始指标时出错: {str(e)}")
        # 设置默认值
        context.cache['technical_indicators'][security]['momentum_m1'] = 0
        context.cache['technical_indicators'][security]['momentum_m3'] = 0
        context.cache['technical_indicators'][security]['momentum_m6'] = 0
        context.cache['technical_indicators'][security]['vol_60d'] = 0.2  # 默认波动率20%
        context.cache['technical_indicators'][security]['数据不足'] = True

def update_and_calculate_factors(context, security):
    """
    更新并计算ETF的技术指标和因子
    """
    try:
        # 获取最新的历史数据
        max_period = max(context.momentum_m6_period, context.volatility_period)
        hist_data = get_price(
            security,
            count=max_period + 10,  # 添加一些冗余
            frequency='daily',
            fields=['high', 'low', 'close', 'factor', 'volume']
        )

        # 检查数据是否为空
        if hist_data is None or hist_data.empty:
            log.warning(f"{security} 历史数据为空，无法计算技术指标")
            # 设置默认值
            context.cache['technical_indicators'][security]['momentum_m1'] = -float('inf')
            context.cache['technical_indicators'][security]['momentum_m3'] = -float('inf')
            context.cache['technical_indicators'][security]['momentum_m6'] = -float('inf')
            context.cache['technical_indicators'][security]['综合动量分'] = -float('inf')
            context.cache['technical_indicators'][security]['vol_60d'] = 0.3  # 较高的默认波动率
            context.cache['technical_indicators'][security]['数据不足'] = True
            return {
                '综合动量分': -float('inf'),
                'vol_60d': 0.3
            }

        # 更新缓存中的价格数据
        context.cache['price_data'][security] = hist_data

        # 计算复权价格
        real_closes = hist_data['close'] * hist_data['factor']
        context.cache['technical_indicators'][security]['real_closes'] = real_closes

        # 绝对最小观察期
        absolute_min_obs = 5  # 至少5个交易日的数据

        # 计算动量因子
        # 1个月动量
        if len(real_closes) < absolute_min_obs:
            log.warning(f"{security} 数据长度 {len(real_closes)} 极度不足（少于{absolute_min_obs}天），无法可靠计算M1动量")
            m1_return = -float('inf')
        else:
            m1_return = real_closes[-1] / real_closes[-min(len(real_closes), context.momentum_m1_period)] - 1
            if len(real_closes) < context.momentum_m1_period:
                log.info(f"{security} 数据长度 {len(real_closes)} 小于理想的M1动量周期 {context.momentum_m1_period}，但仍基于可用数据计算")
        context.cache['technical_indicators'][security]['momentum_m1'] = m1_return

        # 3个月动量
        if len(real_closes) < absolute_min_obs:
            log.warning(f"{security} 数据长度 {len(real_closes)} 极度不足（少于{absolute_min_obs}天），无法可靠计算M3动量")
            m3_return = -float('inf')
        else:
            m3_return = real_closes[-1] / real_closes[-min(len(real_closes), context.momentum_m3_period)] - 1
            if len(real_closes) < context.momentum_m3_period:
                log.info(f"{security} 数据长度 {len(real_closes)} 小于理想的M3动量周期 {context.momentum_m3_period}，但仍基于可用数据计算")
        context.cache['technical_indicators'][security]['momentum_m3'] = m3_return

        # 6个月动量
        if len(real_closes) < absolute_min_obs:
            log.warning(f"{security} 数据长度 {len(real_closes)} 极度不足（少于{absolute_min_obs}天），无法可靠计算M6动量")
            m6_return = -float('inf')
        else:
            m6_return = real_closes[-1] / real_closes[-min(len(real_closes), context.momentum_m6_period)] - 1
            if len(real_closes) < context.momentum_m6_period:
                log.info(f"{security} 数据长度 {len(real_closes)} 小于理想的M6动量周期 {context.momentum_m6_period}，但仍基于可用数据计算")
        context.cache['technical_indicators'][security]['momentum_m6'] = m6_return

        # 计算综合动量分
        # 只有当所有动量指标都是有限数时才计算综合动量分
        if np.isfinite(m1_return) and np.isfinite(m3_return) and np.isfinite(m6_return):
            composite_momentum = 0.3 * m1_return + 0.4 * m3_return + 0.3 * m6_return
        else:
            # 如果有任何一个动量指标是无限数，则综合动量分也设为无限数
            composite_momentum = -float('inf')
            log.warning(f"{security} 部分动量指标无效，无法计算有效的综合动量分")

        context.cache['technical_indicators'][security]['综合动量分'] = composite_momentum

        # 计算波动率
        returns = real_closes.pct_change().dropna()
        if len(returns) < absolute_min_obs:
            log.warning(f"{security} 有效收益率数据点 {len(returns)} 极度不足（少于{absolute_min_obs}点），无法可靠计算波动率")
            vol_60d = 0.2  # 默认波动率20%
        else:
            # 确保只使用最后 volatility_period 个有效收益率数据点进行计算
            vol_60d = returns[-min(len(returns), context.volatility_period):].std() * math.sqrt(252)  # 年化
            if len(returns) < context.volatility_period:
                log.info(f"{security} 有效收益率数据点 {len(returns)} 小于理想的波动率周期 {context.volatility_period}，但仍基于可用数据计算")
        context.cache['technical_indicators'][security]['vol_60d'] = vol_60d

        # 只有在数据极度不足时才标记为数据不足
        if len(real_closes) < absolute_min_obs:
            context.cache['technical_indicators'][security]['数据不足'] = True
            log.warning(f"{security} 被标记为数据极度不足，可能影响评分")
        else:
            # 确保移除可能存在的数据不足标记
            if '数据不足' in context.cache['technical_indicators'][security]:
                del context.cache['technical_indicators'][security]['数据不足']

        # 返回计算结果
        return {
            '综合动量分': composite_momentum,
            'vol_60d': vol_60d
        }

    except Exception as e:
        log.error(f"更新 {security} 因子时出错: {str(e)}")
        # 设置默认值
        context.cache['technical_indicators'][security]['momentum_m1'] = 0
        context.cache['technical_indicators'][security]['momentum_m3'] = 0
        context.cache['technical_indicators'][security]['momentum_m6'] = 0
        context.cache['technical_indicators'][security]['综合动量分'] = -float('inf')
        context.cache['technical_indicators'][security]['vol_60d'] = 0.2  # 默认波动率20%
        context.cache['technical_indicators'][security]['数据不足'] = True
        return {
            '综合动量分': -float('inf'),
            'vol_60d': 0.2
        }

def calculate_zscores_and_final_score(context):
    """
    计算所有ETF的Z-Score标准化分数和最终综合评分
    """
    try:
        current_date = context.current_dt.date()

        # 初始化当日的Z-score统计数据
        if current_date not in context.cache['zscore_stats']:
            context.cache['zscore_stats'][current_date] = {}

        # 收集所有ETF的动量和波动率数据
        momentum_scores = []
        volatility_scores = []
        valid_securities_for_scoring = []  # 记录哪些ETF参与后续评分

        for security in context.stock_list:
            if security in context.cache['technical_indicators']:
                tech_ind = context.cache['technical_indicators'][security]

                momentum = tech_ind.get('综合动量分')
                volatility = tech_ind.get('vol_60d')

                # 检查指标是否有效 (例如，不是None，不是NaN，不是inf)
                if momentum is not None and np.isfinite(momentum) and volatility is not None and np.isfinite(volatility):
                    momentum_scores.append(momentum)
                    volatility_scores.append(volatility)
                    valid_securities_for_scoring.append(security)
                else:
                    log.warning(f"{security} 的指标无效，将不参与本轮评分。动量: {momentum}, 波动率: {volatility}")
                    # 给这些ETF一个极低的评分
                    tech_ind['最终综合评分'] = -float('inf')

        # 如果没有有效的ETF数据，则设置所有ETF的最终评分为默认值
        if not momentum_scores or not volatility_scores:
            log.warning("没有足够的有效ETF数据进行Z-Score计算，所有ETF将使用默认评分")
            for security in context.stock_list:
                if security in context.cache['technical_indicators']:
                    context.cache['technical_indicators'][security]['最终综合评分'] = -float('inf')

            # 存储默认统计数据
            context.cache['zscore_stats'][current_date]['动量均值'] = 0
            context.cache['zscore_stats'][current_date]['动量标准差'] = 0.01
            context.cache['zscore_stats'][current_date]['波动率均值'] = 0.2
            context.cache['zscore_stats'][current_date]['波动率标准差'] = 0.01

            log.info("Z-Score计算完成 (使用默认值)")
            return

        # 计算动量的均值和标准差
        momentum_mean = np.mean(momentum_scores)
        momentum_std = np.std(momentum_scores) if len(momentum_scores) > 1 else 0.01

        # 计算波动率的均值和标准差
        volatility_mean = np.mean(volatility_scores)
        volatility_std = np.std(volatility_scores) if len(volatility_scores) > 1 else 0.01

        # 检查计算结果是否有效
        if not np.isfinite(momentum_mean) or not np.isfinite(momentum_std) or not np.isfinite(volatility_mean) or not np.isfinite(volatility_std):
            log.warning("Z-Score统计量计算结果包含NaN或Inf，使用默认值")
            momentum_mean = 0
            momentum_std = 0.01
            volatility_mean = 0.2
            volatility_std = 0.01

        # 存储统计数据
        context.cache['zscore_stats'][current_date]['动量均值'] = momentum_mean
        context.cache['zscore_stats'][current_date]['动量标准差'] = momentum_std
        context.cache['zscore_stats'][current_date]['波动率均值'] = volatility_mean
        context.cache['zscore_stats'][current_date]['波动率标准差'] = volatility_std

        log.info(f"Z-Score统计数据 - 动量均值: {momentum_mean:.4f}, 标准差: {momentum_std:.4f}, 波动率均值: {volatility_mean:.4f}, 标准差: {volatility_std:.4f}")

        # 仅为有效的ETF计算Z-score和最终评分
        for security in valid_securities_for_scoring:
            # 获取该ETF的动量和波动率
            momentum = context.cache['technical_indicators'][security].get('综合动量分', 0)
            volatility = context.cache['technical_indicators'][security].get('vol_60d', 0.2)

            # 计算动量Z-score
            momentum_zscore = (momentum - momentum_mean) / momentum_std if momentum_std > 0 else 0

            # Winsorize处理
            momentum_zscore = max(min(momentum_zscore, context.zscore_winsorize_limit), -context.zscore_winsorize_limit)

            # 计算波动率Z-score
            volatility_zscore = (volatility - volatility_mean) / volatility_std if volatility_std > 0 else 0

            # Winsorize处理
            volatility_zscore = max(min(volatility_zscore, context.zscore_winsorize_limit), -context.zscore_winsorize_limit)

            # 存储Z-score
            context.cache['technical_indicators'][security]['动量Zscore'] = momentum_zscore
            context.cache['technical_indicators'][security]['波动率Zscore'] = volatility_zscore

            # 计算最终综合评分 (动量正向贡献，波动率负向贡献)
            final_score = context.weight_momentum * momentum_zscore - context.weight_volatility * volatility_zscore
            context.cache['technical_indicators'][security]['最终综合评分'] = final_score

            log.debug(f"{security} Z-Score - 动量: {momentum_zscore:.4f}, 波动率: {volatility_zscore:.4f}, 最终评分: {final_score:.4f}")

        log.info(f"完成所有ETF的Z-Score和最终评分计算，有效ETF数量: {len(valid_securities_for_scoring)}")

    except Exception as e:
        log.error(f"计算Z-Score和最终评分时出错: {str(e)}")
        # 出错时，为所有ETF设置默认评分
        for security in context.stock_list:
            if security in context.cache['technical_indicators']:
                context.cache['technical_indicators'][security]['最终综合评分'] = -float('inf')

def update_sp500_sma(context):
    """
    更新S&P 500指数的简单移动平均线
    """
    try:
        current_date = context.current_dt.date()

        # 获取历史数据
        history_days = context.sp500_sma_period + 50  # 添加冗余

        # 检查是否使用替代指数
        if context.sp500_ticker != '000300.XSHG':
            try:
                sp500_hist = get_price(
                    context.sp500_ticker,
                    count=history_days,
                    frequency='daily',
                    fields=['close', 'factor']
                )
            except Exception as e:
                log.warning(f"获取{context.sp500_ticker}数据失败: {str(e)}，使用沪深300作为替代")
                sp500_hist = get_price(
                    '000300.XSHG',
                    count=history_days,
                    frequency='daily',
                    fields=['close', 'factor']
                )
        else:
            log.warning("使用沪深300作为S&P 500的替代指数")
            sp500_hist = get_price(
                '000300.XSHG',
                count=history_days,
                frequency='daily',
                fields=['close', 'factor']
            )

        # 计算复权收盘价
        real_closes = sp500_hist['close'] * sp500_hist['factor']

        # 计算SMA
        sma = talib.SMA(real_closes.values, timeperiod=context.sp500_sma_period)

        # 存储最新的SMA值
        context.cache['sp500_sma'][current_date] = sma[-1]

        # 获取当前价格
        current_price = real_closes[-1]

        log.info(f"更新S&P 500 (或替代指数) SMA - 当前价格: {current_price:.2f}, SMA{context.sp500_sma_period}: {sma[-1]:.2f}")

        return current_price, sma[-1]

    except Exception as e:
        log.error(f"更新S&P 500 SMA时出错: {str(e)}")
        # 如果出错，返回缓存中的最后一个值，或者默认值
        last_date = sorted(context.cache['sp500_sma'].keys())[-1] if context.cache['sp500_sma'] else None
        last_sma = context.cache['sp500_sma'].get(last_date, 0) if last_date else 0
        return 0, last_sma

def get_market_mode(context):
    """
    根据S&P 500与其200日均线的关系判断市场状态
    """
    try:
        current_date = context.current_dt.date()

        # 获取当前价格和SMA
        current_price, sma200 = update_sp500_sma(context)

        # 判断市场状态
        if current_price < (sma200 * (1 - context.sp500_buffer)):
            log.warning(f"市场处于风险模式 - 当前价格 ({current_price:.2f}) < SMA200 * (1 - buffer) ({sma200 * (1 - context.sp500_buffer):.2f})")
            return 'Crisis_Mode'
        else:
            log.info(f"市场处于正常模式 - 当前价格 ({current_price:.2f}) >= SMA200 * (1 - buffer) ({sma200 * (1 - context.sp500_buffer):.2f})")
            return 'Normal_Mode'

    except Exception as e:
        log.error(f"判断市场状态时出错: {str(e)}")
        # 如果出错，默认返回正常模式
        return 'Normal_Mode'

def execute_normal_mode_rebalance(context):
    """
    正常市场模式下的调仓逻辑
    """
    try:
        log.info("执行正常模式调仓...")

        # 获取所有ETF的最终综合评分
        etf_scores = {}
        for security in context.stock_list:
            if security in context.cache['technical_indicators']:
                score = context.cache['technical_indicators'][security].get('最终综合评分', 0)
                etf_scores[security] = score

        # 按评分从高到低排序
        sorted_etfs = sorted(etf_scores.items(), key=lambda x: x[1], reverse=True)

        # 选择排名前N的ETF
        top_n_etfs = sorted_etfs[:context.normal_mode_top_n]

        log.info(f"正常模式 - 选择排名前{context.normal_mode_top_n}的ETF: {[etf[0] for etf in top_n_etfs]}")

        # 计算调整后评分和总分
        adj_scores = {}
        total_adj_score = 0

        for security, score in top_n_etfs:
            adj_score = max(0, score)
            adj_scores[security] = adj_score
            total_adj_score += adj_score

        # 计算权重
        target_weights = {}

        # 如果所有调整后评分都为0，则全部资金配置到货币ETF
        if total_adj_score <= 0:
            log.warning("正常模式 - 所有ETF的调整后评分都为0，全部资金配置到货币ETF")
            # 所有股票类ETF权重为0
            for security in context.stock_list:
                target_weights[security] = 0
            # 货币ETF权重为1
            target_weights[context.money_fund] = 1

            log.info("正常模式 - 全部资金配置到货币ETF")
        else:
            # 计算每个ETF的权重
            for security in context.stock_list:
                if security in adj_scores:
                    target_weights[security] = adj_scores[security] / total_adj_score
                else:
                    target_weights[security] = 0

            # 货币ETF权重为0
            target_weights[context.money_fund] = 0

            # 计算总权重并确保其为有效的浮点数
            current_target_total_weight = sum(list(target_weights.values()))  # 确保转换为列表再求和，以防 dict_values 的特殊行为
            if isinstance(current_target_total_weight, (float, int)) and np.isfinite(current_target_total_weight):
                log.info(f"正常模式 - 选中的ETF总权重: {current_target_total_weight:.4f}")
            else:
                log.warning(f"正常模式 - 计算出的选中的ETF总权重无效: {current_target_total_weight}，跳过该日志的格式化输出")

        # 打印目标权重
        for security, weight in target_weights.items():
            if weight > 0:
                log.info(f"正常模式 - {security} 目标权重: {weight:.4f}")

        # 返回目标权重，供后续交易执行使用
        return target_weights

    except Exception as e:
        log.error(f"执行正常模式调仓时出错: {str(e)}")
        # 如果出错，返回空字典
        return {}

def execute_crisis_mode_rebalance(context):
    """
    风险市场模式下的调仓逻辑
    """
    try:
        log.info("执行风险模式调仓...")

        # 获取黄金ETF的动量指标
        gold_etf = '518880.XSHG'
        gold_momentum = context.cache['technical_indicators'].get(gold_etf, {}).get('momentum_m3', 0)

        # 根据黄金动量确定黄金ETF目标仓位
        if gold_momentum > context.crisis_mode_gold_strong_momentum_threshold:
            gold_target_weight = context.crisis_mode_gold_pos_strong
            log.info(f"风险模式 - 黄金强动量 ({gold_momentum:.4f} > {context.crisis_mode_gold_strong_momentum_threshold}), 目标仓位: {gold_target_weight:.4f}")
        elif gold_momentum > context.crisis_mode_gold_weak_momentum_threshold:
            gold_target_weight = context.crisis_mode_gold_pos_weak
            log.info(f"风险模式 - 黄金弱动量 ({gold_momentum:.4f} > {context.crisis_mode_gold_weak_momentum_threshold}), 目标仓位: {gold_target_weight:.4f}")
        else:
            gold_target_weight = context.crisis_mode_gold_pos_negative
            log.info(f"风险模式 - 黄金负动量 ({gold_momentum:.4f} <= {context.crisis_mode_gold_weak_momentum_threshold}), 目标仓位: {gold_target_weight:.4f}")

        # 股票类ETF总目标仓位上限
        stock_max_exposure = context.crisis_mode_stock_max_exposure

        # 获取除黄金外的股票类ETF的最终综合评分
        stock_etfs = [etf for etf in context.stock_list if etf != gold_etf]
        stock_scores = {}

        for security in stock_etfs:
            if security in context.cache['technical_indicators']:
                score = context.cache['technical_indicators'][security].get('最终综合评分', 0)
                stock_scores[security] = score

        # 按评分从高到低排序
        sorted_stocks = sorted(stock_scores.items(), key=lambda x: x[1], reverse=True)

        # 选择排名前N的股票类ETF
        top_n_stocks = sorted_stocks[:context.crisis_mode_stock_top_n]

        log.info(f"风险模式 - 选择排名前{context.crisis_mode_stock_top_n}的股票类ETF: {[stock[0] for stock in top_n_stocks]}")

        # 计算调整后评分和总分
        adj_scores = {}
        total_adj_score = 0

        for security, score in top_n_stocks:
            adj_score = max(0, score)
            adj_scores[security] = adj_score
            total_adj_score += adj_score

        # 计算股票类ETF的权重
        stock_weights = {}

        # 如果所有调整后评分都为0，则股票类ETF权重都为0
        if total_adj_score <= 0:
            log.warning("风险模式 - 所有股票类ETF的调整后评分都为0，不配置股票类ETF")
            for security in stock_etfs:
                stock_weights[security] = 0
        else:
            # 计算每个股票类ETF的相对权重和实际权重
            for security in stock_etfs:
                if security in adj_scores:
                    relative_weight = adj_scores[security] / total_adj_score
                    actual_weight = relative_weight * stock_max_exposure
                    stock_weights[security] = actual_weight
                else:
                    stock_weights[security] = 0

        # 计算股票类ETF的总实际权重
        total_stock_weight = sum(list(stock_weights.values()))

        # 计算货币ETF的权重
        money_fund_weight = max(0, 1 - gold_target_weight - total_stock_weight)

        # 合并所有权重
        target_weights = {}

        # 股票类ETF权重
        for security in stock_etfs:
            target_weights[security] = stock_weights.get(security, 0)

        # 黄金ETF权重
        target_weights[gold_etf] = gold_target_weight

        # 货币ETF权重
        target_weights[context.money_fund] = money_fund_weight

        # 打印目标权重
        for security, weight in target_weights.items():
            if weight > 0:
                log.info(f"风险模式 - {security} 目标权重: {weight:.4f}")

        # 确保所有权重值都是有效的浮点数
        if (isinstance(total_stock_weight, (float, int)) and np.isfinite(total_stock_weight) and
            isinstance(gold_target_weight, (float, int)) and np.isfinite(gold_target_weight) and
            isinstance(money_fund_weight, (float, int)) and np.isfinite(money_fund_weight)):
            log.info(f"风险模式 - 总权重分配: 股票ETF={total_stock_weight:.2f}, 黄金ETF={gold_target_weight:.2f}, 货币ETF={money_fund_weight:.2f}")
        else:
            log.warning(f"风险模式 - 计算出的权重值无效: 股票ETF={total_stock_weight}, 黄金ETF={gold_target_weight}, 货币ETF={money_fund_weight}，跳过该日志的格式化输出")

        # 返回目标权重，供后续交易执行使用
        return target_weights

    except Exception as e:
        log.error(f"执行风险模式调仓时出错: {str(e)}")
        # 如果出错，返回空字典
        return {}

def is_rebalance_day(context):
    """
    判断当天是否为每月的第一个交易日
    """
    try:
        today = context.current_dt.date()
        current_month_first_day = datetime.date(today.year, today.month, 1)

        # 获取当月的所有交易日
        trading_days_this_month = get_trade_days(start_date=current_month_first_day, end_date=today)

        # 判断今天是否为当月第一个交易日
        if len(trading_days_this_month) > 0 and today == trading_days_this_month[0]:
            # 避免重复调仓
            if hasattr(context, 'last_rebalance_date') and context.last_rebalance_date == today:
                return False
            return True
        return False
    except Exception as e:
        log.error(f"判断调仓日时出错: {str(e)}")
        return False

def adjust_shares_for_lot_size(price, cash_amount, lot_size=100):
    """
    根据股票价格、计划投入现金和每手股数，计算可购买的股数

    参数:
        price: 股票单价
        cash_amount: 计划投入的现金
        lot_size: 每手股数，默认为100

    返回:
        调整后可购买的股数（为lot_size的整数倍且不超过投入现金）
    """
    try:
        if price <= 0 or cash_amount <= 0:
            return 0

        # 计算最大可购买股数
        max_shares = int(cash_amount / price)

        # 调整为lot_size的整数倍
        adjusted_shares = (max_shares // lot_size) * lot_size

        return adjusted_shares
    except Exception as e:
        log.error(f"计算调整股数时出错: {str(e)}")
        return 0

def execute_trades(context, target_weights):
    """
    执行交易，根据目标权重调整持仓

    参数:
        context: 策略上下文
        target_weights: 目标权重字典 {security: weight}
    """
    try:
        if not target_weights:
            log.warning("目标权重为空，跳过交易执行")
            return

        portfolio_value = context.portfolio.total_value
        log.info(f"开始执行交易 - 当前总资产: {portfolio_value:.2f}")

        # 获取当前持仓
        current_positions = {security: position.value / portfolio_value
                            for security, position in context.portfolio.positions.items()
                            if position.value > 0}

        # 计算需要卖出的证券
        for security in current_positions:
            target_weight = target_weights.get(security, 0)
            current_weight = current_positions.get(security, 0)

            # 如果目标权重小于当前权重，需要卖出
            if target_weight < current_weight:
                target_value = portfolio_value * target_weight

                # 如果目标权重为0，则清仓
                if target_weight == 0:
                    log.info(f"卖出 {security} - 清仓")
                else:
                    log.info(f"减仓 {security} - 从 {current_weight:.2%} 到 {target_weight:.2%}, 目标价值: {target_value:.2f}")

                try:
                    order_target_value(security, target_value)
                except Exception as e:
                    log.error(f"卖出 {security} 时出错: {str(e)}")

        # 等待一段时间，让卖出订单有机会执行
        # 在实际环境中，可能需要在下一个bar或使用其他机制确保卖出完成后再买入

        # 计算需要买入的证券
        for security, target_weight in target_weights.items():
            current_weight = current_positions.get(security, 0)

            # 如果目标权重大于当前权重，需要买入
            if target_weight > current_weight:
                target_value = portfolio_value * target_weight

                # 获取当前价格
                current_data = get_current_data()
                if security in current_data and current_data[security].paused:
                    log.warning(f"{security} 当前停牌，跳过买入")
                    continue

                log.info(f"买入 {security} - 从 {current_weight:.2%} 到 {target_weight:.2%}, 目标价值: {target_value:.2f}")

                try:
                    order_target_value(security, target_value)
                except Exception as e:
                    log.error(f"买入 {security} 时出错: {str(e)}")

        log.info("交易执行完成")

    except Exception as e:
        log.error(f"执行交易时出错: {str(e)}")

def rebalance_on_schedule(context):
    """
    定期调仓函数
    """
    try:
        # 判断是否为调仓日
        if not is_rebalance_day(context):
            return

        log.info(f"开始执行定期调仓 - {context.current_dt.date()}")

        # 更新S&P 500 SMA
        update_sp500_sma(context)

        # 更新每个ETF的因子
        for security in context.stock_list:
            update_and_calculate_factors(context, security)

        # 计算Z-score和最终评分
        calculate_zscores_and_final_score(context)

        # 判断市场状态
        market_mode = get_market_mode(context)

        # 根据市场状态执行不同的调仓逻辑
        if market_mode == 'Normal_Mode':
            target_weights = execute_normal_mode_rebalance(context)
        else:  # Crisis_Mode
            target_weights = execute_crisis_mode_rebalance(context)

        # 执行交易
        execute_trades(context, target_weights)

        # 更新最后调仓日期
        context.last_rebalance_date = context.current_dt.date()

        log.info(f"定期调仓完成 - {context.current_dt.date()}")

    except Exception as e:
        log.error(f"定期调仓时出错: {str(e)}")

def cancel_all_orders(context):
    """
    取消所有未完成的订单
    """
    try:
        # 获取所有未完成订单
        open_orders = get_open_orders()

        # 如果有未完成订单，则取消
        if open_orders:
            for order_id in open_orders:
                cancel_order(order_id)
                log.info(f"取消未完成订单: {order_id}")

            log.info(f"共取消 {len(open_orders)} 个未完成订单")

    except Exception as e:
        log.error(f"取消订单时出错: {str(e)}")

def market_open(context):
    """
    开盘时执行的函数
    """
    try:
        # 取消所有未完成订单
        cancel_all_orders(context)

    except Exception as e:
        log.error(f"开盘处理时出错: {str(e)}")
