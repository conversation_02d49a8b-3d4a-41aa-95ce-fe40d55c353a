import pandas as pd
import numpy as np
from datetime import datetime
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats

def load_transaction_data(file_path):
    """加载交易记录数据"""
    try:
        df = pd.read_csv(file_path)
        # 确保日期列是datetime类型
        if 'date' in df.columns:
            df['date'] = pd.to_datetime(df['date'])
        return df
    except Exception as e:
        print(f"加载数据时出错: {e}")
        return None

def calculate_returns(df):
    """计算收益率相关指标"""
    if df is None or df.empty:
        return None
    
    # 计算每日收益率
    df['daily_return'] = df['profit'].pct_change()
    
    # 计算累计收益率
    df['cumulative_return'] = (1 + df['daily_return']).cumprod() - 1
    
    # 计算年化收益率
    days = (df['date'].max() - df['date'].min()).days
    annual_return = (1 + df['cumulative_return'].iloc[-1]) ** (365/days) - 1
    
    return {
        'total_return': df['cumulative_return'].iloc[-1],
        'annual_return': annual_return,
        'daily_returns': df['daily_return']
    }

def calculate_drawdown(df):
    """计算最大回撤"""
    if df is None or df.empty:
        return None
    
    # 计算累计收益
    cumulative_returns = (1 + df['daily_return']).cumprod()
    
    # 计算历史最高点
    running_max = cumulative_returns.cummax()
    
    # 计算回撤
    drawdown = (cumulative_returns - running_max) / running_max
    
    return {
        'max_drawdown': drawdown.min(),
        'drawdown_series': drawdown
    }

def calculate_sharpe_ratio(df, risk_free_rate=0.03):
    """计算夏普比率"""
    if df is None or df.empty:
        return None
    
    # 计算超额收益
    excess_returns = df['daily_return'] - risk_free_rate/252
    
    # 计算夏普比率
    sharpe_ratio = np.sqrt(252) * excess_returns.mean() / excess_returns.std()
    
    return sharpe_ratio

def calculate_win_rate(df):
    """计算胜率和盈亏比"""
    if df is None or df.empty:
        return None
    
    # 计算胜率
    winning_trades = df[df['profit'] > 0]
    win_rate = len(winning_trades) / len(df)
    
    # 计算盈亏比
    avg_win = winning_trades['profit'].mean()
    avg_loss = df[df['profit'] < 0]['profit'].mean()
    profit_loss_ratio = abs(avg_win / avg_loss) if avg_loss != 0 else float('inf')
    
    return {
        'win_rate': win_rate,
        'profit_loss_ratio': profit_loss_ratio
    }

def analyze_monthly_returns(df):
    """分析月度收益"""
    if df is None or df.empty:
        return None
    
    # 添加月份列
    df['month'] = df['date'].dt.to_period('M')
    
    # 计算月度收益
    monthly_returns = df.groupby('month')['profit'].sum()
    
    return monthly_returns

def plot_performance(df, returns, drawdown):
    """绘制性能图表"""
    if df is None or df.empty:
        return
    
    # 设置图表风格
    plt.style.use('seaborn')
    
    # 创建子图
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 8))
    
    # 绘制累计收益
    ax1.plot(df['date'], returns['cumulative_return'], label='累计收益')
    ax1.set_title('策略累计收益')
    ax1.set_xlabel('日期')
    ax1.set_ylabel('收益率')
    ax1.legend()
    
    # 绘制回撤
    ax2.fill_between(df['date'], drawdown['drawdown_series'], 0, color='red', alpha=0.3)
    ax2.set_title('策略回撤')
    ax2.set_xlabel('日期')
    ax2.set_ylabel('回撤')
    
    plt.tight_layout()
    plt.savefig('performance_analysis.png')
    plt.close()

def main():
    # 加载交易数据
    df = load_transaction_data('transaction-2.csv')
    if df is None:
        print("无法加载交易数据")
        return
    
    # 计算各项指标
    returns = calculate_returns(df)
    drawdown = calculate_drawdown(df)
    sharpe = calculate_sharpe_ratio(df)
    win_stats = calculate_win_rate(df)
    monthly_returns = analyze_monthly_returns(df)
    
    # 打印分析结果
    print("\n=== 策略性能分析报告 ===")
    print(f"总收益率: {returns['total_return']:.2%}")
    print(f"年化收益率: {returns['annual_return']:.2%}")
    print(f"最大回撤: {drawdown['max_drawdown']:.2%}")
    print(f"夏普比率: {sharpe:.2f}")
    print(f"胜率: {win_stats['win_rate']:.2%}")
    print(f"盈亏比: {win_stats['profit_loss_ratio']:.2f}")
    
    print("\n=== 月度收益分析 ===")
    print(monthly_returns)
    
    # 绘制性能图表
    plot_performance(df, returns, drawdown)
    print("\n性能分析图表已保存为 'performance_analysis.png'")

if __name__ == "__main__":
    main() 