# 2024/08/01  止损卖出修改为跌破5日均线
# Ptrade平台适配版本

import datetime as dt
import pandas as pd
import numpy as np
from datetime import datetime
from datetime import timedelta
import math
import pickle  # 导入pickle模块用于持久化处理
from ptrade import log  # 导入Ptrade的日志API


def initialize(context):
    # 获取研究路径，用于持久化文件存储
    g.notebook_path = get_research_path()

    # 初始化现金管理相关变量
    g.money_fund = '511880.XSHG'  # 华宝添益货币ETF
    g.money_fund_threshold = 0.01  # 货基交易阈值（1%）
    g.min_fund_amount = 100  # 最小货基交易数量
    g.last_timing_result = None  # 上次择时结果
    g.cash_management_enabled = True  # 是否启用现金管理

    # 尝试加载持久化数据
    try:
        # 使用研究路径，不使用os.path.join
        with open(g.notebook_path + 'strategy_3on1_state.pkl', 'rb') as f:
            saved_data = pickle.load(f)
            # 恢复保存的全局变量
            g.target_list = saved_data.get('target_list', [])
            g.target_list2 = saved_data.get('target_list2', [])
            g.last_timing_result = saved_data.get('last_timing_result', None)
            log.info("成功加载持久化数据")
    except Exception as e:
        # 如果加载失败，初始化默认值
        g.target_list = []
        g.target_list2 = []
        log.info("未找到持久化数据或加载失败: %s", str(e))

    # 一进二
    run_daily(context, get_stock_list, time='09:01')
    run_daily(context, buy, time='09:26')
    run_daily(context, sell, time='11:25')
    run_daily(context, sell, time='14:50')

    # 现金管理相关
    run_daily(context, manage_cash, time='14:55')  # 收盘前管理现金

    # 首版低开
    # run_daily(context, buy2, time='09:27') #9:25分知道开盘价后可以提前下单


# 选股
def get_stock_list(context):
    # 获取前一交易日
    trading_day = get_trading_day()
    date = get_shifted_date(trading_day, -1, 'T')
    date_1 = get_shifted_date(date, -1, 'T')
    date_2 = get_shifted_date(date, -2, 'T')

    # 初始列表
    initial_list = prepare_stock_list(date)
    # 昨日涨停
    hl_list = get_hl_stock(initial_list, date)
    # 前日曾涨停
    hl1_list = get_ever_hl_stock(initial_list, date_1)
    # 前前日曾涨停
    hl2_list = get_ever_hl_stock(initial_list, date_2)
    # 合并 hl1_list 和 hl2_list 为一个集合，用于快速查找需要剔除的元素
    elements_to_remove = set(hl1_list + hl2_list)
    # 使用列表推导式来剔除 hl_list 中存在于 elements_to_remove 集合中的元素
    hl_list = [stock for stock in hl_list if stock not in elements_to_remove]

    g.target_list = hl_list

    # 昨日曾涨停
    h1_list = get_ever_hl_stock2(initial_list, date)
    # 上上个交易日涨停过滤
    elements_to_remove = get_hl_stock(initial_list, date_1)

    # 过滤上上个交易日涨停、曾涨停
    all_list = [stock for stock in h1_list if stock not in elements_to_remove]

    g.target_list2 = all_list


# 交易
def buy(context):
    # 先进行择时判断
    log.info('开始进行择时判断...')
    timing_result = select_timing(context)
    log.info('择时判断结果: %s', timing_result)

    # 保存择时结果，用于现金管理
    g.last_timing_result = timing_result

    if not timing_result:
        log.info('今日择时信号不满足，不进行交易')

        # 如果启用了现金管理，将闲置资金买入货币ETF
        if g.cash_management_enabled:
            invest_in_money_fund(context)

        return

    log.info('择时信号满足，开始选股...')
    qualified_stocks = []
    gk_stocks = []
    dk_stocks = []
    rzq_stocks = []

    # 获取当前日期
    date_now = get_trading_day()
    mid_time1 = ' 09:15:00'
    end_times1 = ' 09:26:00'
    start = date_now + mid_time1
    end = date_now + end_times1

    # 高开
    for s in g.target_list:
        # 条件一：均价，金额，市值，换手率
        prev_day_data = get_price(s, count=1, end_date=get_shifted_date(date_now, -1, 'T'), fields=['close', 'volume', 'money'], frequency='daily')
        avg_price_increase_value = prev_day_data['money'][0] / prev_day_data['volume'][0] / prev_day_data['close'][0] * 1.1 - 1
        if avg_price_increase_value < 0.07 or prev_day_data['money'][0] < 5.5e8 or prev_day_data['money'][0] > 20e8:
            continue

        # market_cap 总市值(亿元) > 70亿 流通市值(亿元) < 520亿
        turnover_ratio_data = get_fundamentals(s, 'valuation', ['turnover_ratio', 'market_cap', 'circulating_market_cap'], date=get_shifted_date(date_now, -1, 'T'))
        if turnover_ratio_data.empty or turnover_ratio_data['market_cap'][0] < 70 or turnover_ratio_data['circulating_market_cap'][0] > 520:
            continue

        # 条件二：左压
        zyts = calculate_zyts(s, context)
        volume_data = get_price(s, count=zyts, end_date=get_shifted_date(date_now, -1, 'T'), fields=['volume'], frequency='daily')
        if len(volume_data) < 2 or volume_data['volume'][-1] <= max(volume_data['volume'][:-1]) * 0.9:
            continue

        # 条件三：高开，开比
        auction_data = get_trend_data(s, start_date=start, end_date=end, fields=['time', 'volume', 'current'])
        # 获取涨停价
        stock_info = get_stock_info(s)
        high_limit = stock_info['close'] * 1.1

        if auction_data.empty or auction_data['volume'][0] / volume_data['volume'][-1] < 0.03:
            continue

        current_ratio = auction_data['current'][0] / (high_limit/1.1)
        if current_ratio <= 1 or current_ratio >= 1.06:
            continue

        # 如果股票满足所有条件，则添加到列表中
        gk_stocks.append(s)
        qualified_stocks.append(s)

    # 低开
    # 基础信息
    date = get_shifted_date(date_now, -1, 'T')

    # 昨日涨停列表
    initial_list = prepare_stock_list2(date)
    hl_list = get_hl_stock(initial_list, date)

    if len(hl_list) != 0:
        # 获取非连板涨停的股票
        ccd = get_continue_count_df(hl_list, date, 10)
        lb_list = list(ccd.index)
        stock_list = [s for s in hl_list if s not in lb_list]

        # 计算相对位置
        rpd = get_relative_position_df(stock_list, date, 60)
        rpd = rpd[rpd['rp'] <= 0.5]
        stock_list = list(rpd.index)

        # 低开
        df = pd.DataFrame()
        if len(stock_list) != 0:
            df = get_price(stock_list, end_date=date, frequency='daily', fields=['close'], count=1, panel=False).set_index('code')
            # 获取当前开盘价
            for s in stock_list:
                snapshot = get_snapshot(s)
                df.loc[s, 'open_pct'] = snapshot['open'] / df.loc[s, 'close']

            df = df[(0.955 <= df['open_pct']) & (df['open_pct'] <= 0.97)]  # 低开越多风险越大，选择3个多点即可
            stock_list = list(df.index)

        for s in stock_list:
            prev_day_data = get_price(s, count=1, end_date=date, fields=['close', 'volume', 'money'], frequency='daily')
            if prev_day_data['money'][0] >= 1e8:
                dk_stocks.append(s)
                qualified_stocks.append(s)

    # 弱转强
    for s in g.target_list2:
        # 过滤前面三天涨幅超过28%的票
        price_data = get_price(s, count=4, end_date=get_shifted_date(date_now, -1, 'T'), fields=['close'], frequency='daily')
        if len(price_data) < 4:
            continue

        increase_ratio = (price_data['close'][-1] - price_data['close'][0]) / price_data['close'][0]
        if increase_ratio > 0.28:
            continue

        # 过滤前一日收盘价小于开盘价5%以上的票
        prev_day_data = get_price(s, count=1, end_date=get_shifted_date(date_now, -1, 'T'), fields=['open', 'close'], frequency='daily')
        if len(prev_day_data) < 1:
            continue

        open_close_ratio = (prev_day_data['close'][0] - prev_day_data['open'][0]) / prev_day_data['open'][0]
        if open_close_ratio < -0.05:
            continue

        prev_day_data = get_price(s, count=1, end_date=get_shifted_date(date_now, -1, 'T'), fields=['close', 'volume', 'money'], frequency='daily')
        avg_price_increase_value = prev_day_data['money'][0] / prev_day_data['volume'][0] / prev_day_data['close'][0] - 1
        if avg_price_increase_value < -0.04 or prev_day_data['money'][0] < 3e8 or prev_day_data['money'][0] > 19e8:
            continue

        turnover_ratio_data = get_fundamentals(s, 'valuation', ['turnover_ratio', 'market_cap', 'circulating_market_cap'], date=get_shifted_date(date_now, -1, 'T'))
        if turnover_ratio_data.empty or turnover_ratio_data['market_cap'][0] < 70 or turnover_ratio_data['circulating_market_cap'][0] > 520:
            continue

        zyts = calculate_zyts(s, context)
        volume_data = get_price(s, count=zyts, end_date=get_shifted_date(date_now, -1, 'T'), fields=['volume'], frequency='daily')
        if len(volume_data) < 2 or volume_data['volume'][-1] <= max(volume_data['volume'][:-1]) * 0.9:
            continue

        auction_data = get_trend_data(s, start_date=start, end_date=end, fields=['time', 'volume', 'current'])
        # 获取涨停价
        stock_info = get_stock_info(s)
        high_limit = stock_info['close'] * 1.1

        if auction_data.empty or auction_data['volume'][0] / volume_data['volume'][-1] < 0.03:
            continue

        current_ratio = auction_data['current'][0] / (high_limit/1.1)
        if current_ratio <= 0.98 or current_ratio >= 1.09:
            continue

        rzq_stocks.append(s)
        qualified_stocks.append(s)

    if len(qualified_stocks) > 0:
        log.info('———————————————————————————————————')
        log.info('今日选股：%s', ','.join(qualified_stocks))
        log.info('一进二：%s', ','.join(gk_stocks))
        log.info('首板低开：%s', ','.join(dk_stocks))
        log.info('弱转强：%s', ','.join(rzq_stocks))
        log.info('今日选股：%s', ','.join(qualified_stocks))
        log.info('———————————————————————————————————')
    else:
        log.info('今日无目标个股')

    # 检查可用资金比例，与原策略保持一致
    if len(qualified_stocks) != 0 and context.portfolio.available_cash / context.portfolio.total_value > 0.3:
        # 按照原策略逻辑，平均分配资金
        value = context.portfolio.available_cash / len(qualified_stocks)

        for s in qualified_stocks:
            # 获取最新价格
            current_price = get_snapshot(s)['last']

            # 检查资金是否足够买入至少100股
            if context.portfolio.available_cash / current_price > 100:
                # 使用市价下单
                order_value(s, value)
                log.info('买入 %s', s)
                log.info('———————————————————————————————————')


def sell(context):
    """
    卖出逻辑 - 与原策略保持一致，但排除货币ETF
    """
    # 获取当前时间
    current_time = str(context.current_dt)[-8:]

    # 根据时间执行不同的卖出策略
    if current_time == '11:25:00':
        for s in list(context.portfolio.positions):
            # 跳过货币ETF
            if s == g.money_fund:
                continue

            # 获取最新价格
            current_price = get_snapshot(s)['last']
            # 获取涨停价
            stock_info = get_stock_info(s)
            high_limit = stock_info['close'] * 1.1

            # 止盈条件：非涨停且有盈利
            if ((context.portfolio.positions[s].closeable_amount != 0) and
                (current_price < high_limit) and
                (current_price > 1 * context.portfolio.positions[s].avg_cost)):
                order_target(s, 0)
                log.info('止盈卖出 %s', s)
                log.info('———————————————————————————————————')

    elif current_time == '14:50:00':
        for s in list(context.portfolio.positions):
            # 跳过货币ETF
            if s == g.money_fund:
                continue

            # 获取最新价格
            current_price = get_snapshot(s)['last']
            # 获取涨停价
            stock_info = get_stock_info(s)
            high_limit = stock_info['close'] * 1.1

            # 计算5日均线
            close_data = get_price(s, count=4, end_date=get_shifted_date(get_trading_day(), -1, 'T'),
                                 fields=['close'], frequency='daily')
            M4 = close_data['close'].mean()
            MA5 = (M4 * 4 + current_price) / 5

            # 止盈条件：非涨停且有盈利
            if ((context.portfolio.positions[s].closeable_amount != 0) and
                (current_price < high_limit) and
                (current_price > 1 * context.portfolio.positions[s].avg_cost)):
                order_target(s, 0)
                log.info('止盈卖出 %s', s)
                log.info('———————————————————————————————————')
            # 止损条件：价格低于5日均线
            elif ((context.portfolio.positions[s].closeable_amount != 0) and
                  (current_price < MA5)):
                order_target(s, 0)
                log.info('止损卖出 %s', s)
                log.info('———————————————————————————————————')


# 处理日期相关函数
def transform_date(date, date_type):
    """
    将日期在字符串格式和datetime格式之间转换
    """
    if date_type == 'str':
        if isinstance(date, str):
            return date
        elif isinstance(date, datetime):
            return date.strftime('%Y-%m-%d')
    elif date_type == 'datetime':
        if isinstance(date, datetime):
            return date
        elif isinstance(date, str):
            return datetime.strptime(date, '%Y-%m-%d')
    return date


def get_shifted_date(date, days, days_type='T'):
    """
    获取日期之前或之后的日期，可按交易日或自然日偏移
    参数：
        date：开始日期（str或datetime）
        days：偏移的天数，正为之后，负为之前
        days_type：类型 'T'交易日, 'D'自然日
    """
    date = transform_date(date, 'str')
    if days_type == 'D':
        # 按自然日偏移
        dt_date = transform_date(date, 'datetime')
        shifted_date = dt_date + timedelta(days=days)
        return transform_date(shifted_date, 'str')
    elif days_type == 'T':
        # 按交易日偏移
        if days > 0:
            trade_days = get_trade_days(start_date=date, count=days+1)
            if len(trade_days) == days+1:
                return trade_days[-1]
            else:
                return date
        elif days < 0:
            days = abs(days)
            trade_days = get_trade_days(end_date=date, count=days+1)
            if len(trade_days) == days+1:
                return trade_days[0]
            else:
                return date
        else:
            return date


# 过滤函数
def filter_new_stock(initial_list, date, days=50):
    """
    过滤掉上市不足指定天数的新股
    """
    filtered_list = []
    for stock in initial_list:
        stock_info = get_stock_info(stock)
        list_date = stock_info.get('list_date', '')
        if list_date and pd.to_datetime(list_date) < pd.to_datetime(date) - pd.Timedelta(days=days):
            filtered_list.append(stock)
    return filtered_list


def filter_st_stock(initial_list, date):
    """
    过滤ST股票
    """
    filtered_list = []
    for stock in initial_list:
        stock_name = get_stock_name(stock)
        if 'ST' not in stock_name and '*' not in stock_name:
            filtered_list.append(stock)
    return filtered_list


def filter_kcbj_stock(initial_list):
    """
    过滤科创板、北交所股票
    """
    filtered_list = [stock for stock in initial_list if not (stock.startswith('688') or stock.startswith('689') or stock.endswith('.BJ'))]
    return filtered_list


def filter_paused_stock(initial_list, date):
    """
    过滤停牌股票
    """
    filtered_list = []
    for stock in initial_list:
        status_info = get_stock_status(stock, date)
        if status_info.get('paused', 1) == 0:  # 0表示未停牌
            filtered_list.append(stock)
    return filtered_list


# 一字
def filter_extreme_limit_stock(stock_list, date):
    """
    过滤一字涨停股票
    """
    filtered_list = []
    for stock in stock_list:
        price_data = get_price(stock, count=1, end_date=date, fields=['high', 'low'], frequency='daily')
        if price_data['high'][0] != price_data['low'][0]:  # 非一字板
            filtered_list.append(stock)
    return filtered_list


# 每日初始股票池
def prepare_stock_list(date):
    """
    准备初始股票池
    """
    # 获取所有A股
    initial_list = get_Ashares(date)
    # 过滤新股
    initial_list = filter_new_stock(initial_list, date)
    # 过滤ST股
    initial_list = filter_st_stock(initial_list, date)
    # 过滤科创板、北交所
    initial_list = filter_kcbj_stock(initial_list)
    # 过滤停牌
    initial_list = filter_paused_stock(initial_list, date)

    return initial_list


# 计算左压天数
def calculate_zyts(stock, context):
    """
    计算左压天数 - 与原策略保持一致
    """
    date = get_shifted_date(get_trading_day(), -1, 'T')
    # 获取历史高价数据
    price_data = get_price(stock, count=101, end_date=date, fields=['high'], frequency='daily')
    if len(price_data) < 10:  # 数据不足时返回默认值
        return 20

    high_prices = price_data['high']
    prev_high = high_prices.iloc[-1]

    # 查找前面的高点
    zyts_0 = 100  # 默认值
    for i in range(2, len(high_prices)-1):
        if high_prices.iloc[-i-1] >= prev_high:
            zyts_0 = i-1
            break

    zyts = zyts_0 + 5
    return zyts


# 筛选出某一日涨停的股票
def get_hl_stock(initial_list, date):
    """
    获取指定日期涨停的股票列表
    """
    # 批量获取数据，提高效率
    if not initial_list:
        return []

    # 分批处理，避免一次请求数据过多
    batch_size = 100
    hl_list = []

    for i in range(0, len(initial_list), batch_size):
        batch_stocks = initial_list[i:i+batch_size]
        df = get_price(batch_stocks, end_date=date, frequency='daily',
                      fields=['close', 'high_limit'], count=1, panel=False)

        # 处理数据
        if not df.empty:
            df = df.dropna()  # 去除停牌
            df = df[df['close'] >= df['high_limit'] * 0.999]  # 涨停判断
            if 'code' in df.columns:
                batch_hl_list = list(df['code'])
                hl_list.extend(batch_hl_list)

    return hl_list


# 筛选曾涨停
def get_ever_hl_stock(initial_list, date):
    """
    获取指定日期内盘中曾涨停的股票列表
    """
    # 批量获取数据，提高效率
    if not initial_list:
        return []

    # 分批处理，避免一次请求数据过多
    batch_size = 100
    hl_list = []

    for i in range(0, len(initial_list), batch_size):
        batch_stocks = initial_list[i:i+batch_size]
        df = get_price(batch_stocks, end_date=date, frequency='daily',
                      fields=['high', 'high_limit'], count=1, panel=False)

        # 处理数据
        if not df.empty:
            df = df.dropna()  # 去除停牌
            df = df[df['high'] >= df['high_limit'] * 0.999]  # 曾涨停判断
            if 'code' in df.columns:
                batch_hl_list = list(df['code'])
                hl_list.extend(batch_hl_list)

    return hl_list


# 筛选曾涨停
def get_ever_hl_stock2(initial_list, date):
    """
    获取指定日期内盘中曾涨停但收盘未涨停的股票列表
    """
    # 批量获取数据，提高效率
    if not initial_list:
        return []

    # 分批处理，避免一次请求数据过多
    batch_size = 100
    hl_list = []

    for i in range(0, len(initial_list), batch_size):
        batch_stocks = initial_list[i:i+batch_size]
        df = get_price(batch_stocks, end_date=date, frequency='daily',
                      fields=['close', 'high', 'high_limit'], count=1, panel=False)

        # 处理数据
        if not df.empty:
            df = df.dropna()  # 去除停牌
            # 高点涨停但收盘未涨停
            cd1 = df['high'] >= df['high_limit'] * 0.999
            cd2 = df['close'] < df['high_limit'] * 0.999
            df = df[cd1 & cd2]

            if 'code' in df.columns:
                batch_hl_list = list(df['code'])
                hl_list.extend(batch_hl_list)

    return hl_list


# 计算涨停数
def get_hl_count_df(hl_list, date, watch_days):
    """
    计算股票在指定日期前的涨停次数和一字涨停次数
    """
    if not hl_list:
        return pd.DataFrame(columns=['count', 'extreme_count'])

    # 批量获取数据
    batch_size = 50
    result_df = pd.DataFrame()

    for i in range(0, len(hl_list), batch_size):
        batch_stocks = hl_list[i:i+batch_size]

        # 获取历史数据
        df = get_price(batch_stocks, end_date=date, frequency='daily',
                      fields=['close', 'high_limit', 'low'], count=watch_days, panel=False)

        if df.empty:
            continue

        # 按股票代码分组处理
        for stock in batch_stocks:
            stock_data = df[df['code'] == stock]
            if stock_data.empty:
                continue

            # 计算涨停次数和一字涨停次数
            hl_days = sum(stock_data['close'] >= stock_data['high_limit'] * 0.999)
            extreme_hl_days = sum(stock_data['low'] >= stock_data['high_limit'] * 0.999)

            # 添加到结果
            result_df = result_df.append({
                'code': stock,
                'count': hl_days,
                'extreme_count': extreme_hl_days
            }, ignore_index=True)

    # 设置索引并排序
    if not result_df.empty:
        result_df = result_df.set_index('code')
        result_df = result_df.sort_values('count', ascending=False)

    return result_df


# 计算连板数
def get_continue_count_df(hl_list, date, watch_days):
    """
    计算股票在指定日期前的连续涨停次数
    """
    if not hl_list:
        return pd.DataFrame(columns=['count', 'extreme_count'])

    # 创建空的DataFrame用于存储结果
    df = pd.DataFrame()

    # 对每个涨停天数进行计算
    for d in range(2, watch_days+1):
        # 获取涨停次数
        HLC = get_hl_count_df(hl_list, date, d)
        # 筛选出涨停次数等于d的股票（连续涨停）
        CHLC = HLC[HLC['count'] == d]
        # 添加到结果DataFrame
        df = df.append(CHLC)

    # 处理可能有多条记录的股票，保留最大连板数
    stock_list = list(set(df.index))
    ccd = pd.DataFrame()

    for s in stock_list:
        try:
            tmp = df.loc[[s]]
            if len(tmp) > 1:
                # 如果有多条记录，取最大连板数
                M = tmp['count'].max()
                tmp = tmp[tmp['count'] == M]
            ccd = ccd.append(tmp)
        except:
            continue

    # 按连板数排序
    if len(ccd) != 0:
        ccd = ccd.sort_values(by='count', ascending=False)

    return ccd


# 计算昨涨幅
def get_index_increase_ratio(index_code, context):
    """
    获取指数昨日涨幅
    """
    date = get_trading_day()
    prev_date = get_shifted_date(date, -1, 'T')
    prev_prev_date = get_shifted_date(prev_date, -1, 'T')

    # 获取前一日和前前一日收盘价
    price_data = get_price(index_code, count=2, end_date=prev_date, fields=['close'], frequency='daily')
    prev_close = price_data['close'][-1]
    prev_prev_close = price_data['close'][0]

    # 计算涨幅
    return (prev_close - prev_prev_close) / prev_prev_close


# 获取股票所属行业
def getStockIndustry(stocks):
    """
    获取股票所属行业
    """
    industry_dict = {}
    for stock in stocks:
        # 在Ptrade中没有直接获取股票行业的API，此处使用股票板块信息代替
        block_info = get_stock_blocks(stock)
        if block_info and len(block_info) > 0:
            industry = block_info[0]  # 取第一个板块作为行业
            industry_dict[stock] = industry
        else:
            industry_dict[stock] = '未知'
    return industry_dict


# 获取市场宽度
def get_market_breadth(context):
    """
    获取市场宽度 - 计算行业偏离度
    使用所有A股替代中证全指成分股
    """
    try:
        log.info("开始计算市场宽度...")
        # 获取前一交易日
        yesterday = get_shifted_date(get_trading_day(), -1, 'T')
        log.info("使用日期: %s", yesterday)

        # 获取所有A股作为初始列表，替代中证全指
        stocks = get_Ashares(yesterday)
        # 过滤新股、ST股等
        stocks = filter_new_stock(stocks, yesterday)
        stocks = filter_st_stock(stocks, yesterday)
        stocks = filter_kcbj_stock(stocks)
        stocks = filter_paused_stock(stocks, yesterday)

        log.info("获取到 %d 只股票", len(stocks))

        # 获取股票价格数据
        count = 21  # 20日均线 + 当日
        all_data = pd.DataFrame()

        # 分批获取数据
        batch_size = 50
        for i in range(0, len(stocks), batch_size):
            batch_stocks = stocks[i:i+batch_size]
            h = get_price(batch_stocks, end_date=yesterday, frequency='daily',
                         fields=['close'], count=count, panel=False)
            if not h.empty:
                all_data = all_data.append(h)

        if all_data.empty:
            log.info("未获取到有效数据")
            return ""

        # 处理数据
        all_data['date'] = pd.to_datetime(all_data['time']).dt.date
        df_close = all_data.pivot(index='code', columns='date', values='close').dropna(axis=0)
        log.info("有效数据股票数量: %d", len(df_close))

        if len(df_close) < 10:
            log.info("有效数据不足")
            return ""

        # 计算20日均线
        df_ma20 = df_close.rolling(window=20, axis=1).mean().iloc[:, -1:]

        # 计算偏离程度（股价是否高于20日均线）
        df_bias = df_close.iloc[:, -1:] > df_ma20

        # 获取股票所属行业
        industry_dict = getStockIndustry(list(df_bias.index))
        log.info("获取到 %d 只股票的行业信息", len(industry_dict))

        # 将行业信息添加到df_bias
        industry_series = pd.Series(industry_dict)
        df_bias['industry_name'] = industry_series

        # 去除没有行业信息的股票
        df_bias = df_bias.dropna(subset=['industry_name'])
        log.info("有效行业信息股票数量: %d", len(df_bias))

        if len(df_bias) < 10:
            log.info("有效行业数据不足")
            return ""

        # 计算行业偏离比例
        last_date = df_bias.columns[0]
        if isinstance(last_date, datetime):
            last_date = last_date.date()

        # 按行业分组计算偏离度
        grouped = df_bias.groupby('industry_name')
        industry_counts = grouped.size()
        industry_above_ma = grouped[last_date].sum()

        # 计算每个行业高于均线的比例
        industry_ratios = (industry_above_ma * 100.0 / industry_counts).round()

        # 获取偏离程度最高的行业
        if len(industry_ratios) > 0:
            top_industry = industry_ratios.nlargest(1).index[0]
            log.info("市场宽度计算结果 - 领先行业Top1: %s", top_industry)
            return top_industry
        else:
            return ""
    except Exception as e:
        log.info("市场宽度计算失败: %s", str(e))
        # 出错时返回空字符串
        return ""


# 择时判断
def select_timing(context):
    """
    择时判断函数 - 与原策略保持一致
    """
    log.info("开始执行择时判断函数...")
    try:
        # 获取市场宽度Top1行业
        top_industry = get_market_breadth(context)
        log.info("获取到的市场宽度领先行业Top1: %s", top_industry)

        # 需要监控的行业
        restricted_industries = ["银行", "有色金属", "钢铁", "煤炭"]
        log.info("需要监控的行业: %s", restricted_industries)

        # 检查Top1行业是否在需要监控的行业中
        is_restricted = False
        for industry in restricted_industries:
            if industry in top_industry:
                is_restricted = True
                log.info("Top1行业 '%s' 包含监控行业 '%s'", top_industry, industry)
                break

        if not is_restricted:
            log.info("Top1行业不在监控行业中，择时条件满足，可以交易")
            return True
        else:
            log.info("Top1行业在监控行业中，择时条件不满足，不进行交易")
            return False
    except Exception as e:
        log.info("择时判断出错: %s", str(e))
        # 出错时默认允许交易
        return True


# 每日初始股票池2（用于首版低开策略）
def prepare_stock_list2(date):
    """
    准备初始股票池
    """
    # 获取所有A股
    initial_list = get_Ashares(date)
    # 过滤新股
    initial_list = filter_new_stock(initial_list, date, days=250)
    # 过滤ST股
    initial_list = filter_st_stock(initial_list, date)
    # 过滤科创板、北交所
    initial_list = filter_kcbj_stock(initial_list)
    # 过滤停牌
    initial_list = filter_paused_stock(initial_list, date)

    return initial_list


# 计算股票处于一段时间内相对位置
def get_relative_position_df(stock_list, date, watch_days):
    """
    计算股票在一段时间内的相对位置
    """
    if len(stock_list) == 0:
        return pd.DataFrame(columns=['rp'])

    # 批量获取数据
    batch_size = 50
    all_data = pd.DataFrame()

    for i in range(0, len(stock_list), batch_size):
        batch_stocks = stock_list[i:i+batch_size]
        # 获取历史数据
        df = get_price(batch_stocks, end_date=date, fields=['high', 'low', 'close'],
                      count=watch_days, frequency='daily', panel=False)

        if not df.empty:
            all_data = all_data.append(df)

    if all_data.empty:
        return pd.DataFrame(columns=['rp'])

    # 去除缺失值
    all_data = all_data.dropna()

    # 按股票代码分组
    grouped = all_data.groupby('code')

    # 计算每只股票的最后收盘价、最高价和最低价
    close = grouped.apply(lambda df: df['close'].iloc[-1])
    high = grouped.apply(lambda df: df['high'].max())
    low = grouped.apply(lambda df: df['low'].min())

    # 计算相对位置
    result = pd.DataFrame()
    result['rp'] = (close - low) / (high - low)

    # 处理可能的无效值
    result = result.replace([np.inf, -np.inf], np.nan).dropna()

    return result


# 现金管理函数 - 投资货币ETF
def invest_in_money_fund(context):
    """
    将闲置资金投资到货币ETF
    """
    try:
        # 计算可用资金
        available_cash = context.portfolio.available_cash

        # 如果可用资金占总资产的比例超过阈值，则买入货币ETF
        if available_cash / context.portfolio.total_value > g.money_fund_threshold:
            # 获取货币ETF的最新价格
            snapshot = get_snapshot(g.money_fund)
            if snapshot and 'last' in snapshot:
                current_price = snapshot['last']

                # 计算可买入的数量（保留100的整数倍）
                buy_amount = int(available_cash / current_price / 100) * 100

                # 如果可买入数量大于最小交易数量，则执行买入
                if buy_amount >= g.min_fund_amount:
                    order(g.money_fund, buy_amount)
                    log.info(f"现金管理: 买入 {g.money_fund} {buy_amount}份，价格: {current_price:.4f}")
                else:
                    log.info(f"现金管理: 可买入数量 {buy_amount} 小于最小交易数量 {g.min_fund_amount}，不执行买入")
            else:
                log.warning(f"现金管理: 无法获取 {g.money_fund} 的价格信息")
        else:
            log.info(f"现金管理: 可用资金比例 {available_cash/context.portfolio.total_value:.2%} 小于阈值 {g.money_fund_threshold:.2%}，不执行买入")
    except Exception as e:
        log.error(f"现金管理买入出错: {str(e)}")


# 现金管理函数 - 收盘前检查
def manage_cash(context):
    """
    收盘前检查是否需要卖出货币ETF，为第二天交易准备资金
    """
    try:
        # 如果择时结果为True，且持有货币ETF，则卖出为明日交易准备资金
        if g.last_timing_result and g.money_fund in context.portfolio.positions:
            position = context.portfolio.positions[g.money_fund]
            if position.amount > 0:
                order_target(g.money_fund, 0)  # 清空持仓
                log.info(f"现金管理: 择时信号为True，卖出全部 {g.money_fund} {position.amount}份，为明日交易准备资金")
    except Exception as e:
        log.error(f"现金管理卖出出错: {str(e)}")


# 持久化保存函数
def save_strategy_state(context):
    """
    将策略状态保存到文件，用于策略重启后恢复
    """
    try:
        # 创建要保存的数据字典
        save_data = {
            'target_list': g.target_list,
            'target_list2': g.target_list2,
            'last_timing_result': g.last_timing_result,  # 保存择时结果
            # 可以添加其他需要保存的全局变量
        }

        # 保存到文件
        with open(g.notebook_path + 'strategy_3on1_state.pkl', 'wb') as f:
            pickle.dump(save_data, f, -1)  # -1表示使用最高的协议版本

        log.info("策略状态已成功保存")
    except Exception as e:
        log.error("保存策略状态失败: %s", str(e))

# 盘后处理函数
def after_trading_end(context, data):
    """
    每日收盘后处理函数，用于执行收盘后的操作
    """
    log.info("执行盘后处理...")

    # 保存策略状态
    save_strategy_state()

    # 记录当日持仓情况
    positions_info = []
    for security, position in context.portfolio.positions.items():
        if position.amount > 0:
            positions_info.append(f"{security}: {position.amount}股, 成本: {position.avg_cost:.2f}")

    if positions_info:
        log.info("当日收盘持仓情况:")
        for info in positions_info:
            log.info(info)
    else:
        log.info("当日收盘无持仓")

    log.info("盘后处理完成")

# 处理函数
def handle_data(context, data):
    """
    策略主要逻辑已在run_daily中实现，此处作为占位函数
    """
    pass
