# ... existing code ...

# 以下函数原代码未完整定义，需根据实际情况补充
def initial_buy(context):
    # 这里可以添加初始建仓的逻辑
    # 示例：将总资金的一定比例分配到各个标的
    total_value = context.portfolio.portfolio_value * context.total_position_pct
    for security in context.stock_list:
        current_price = context.get_price(security)
        amount = int((total_value / len(context.stock_list)) / current_price / 100) * 100
        if amount > 0:
            context.order(security, amount)
            log.info(f"初始建仓 {security}，数量: {amount}")

# ... existing code ...